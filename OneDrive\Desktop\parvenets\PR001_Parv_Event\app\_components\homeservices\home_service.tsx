"use client";
import React from 'react';
import CardGrid from './components/cargrid';

const HomeService = () => {
  return (
    <div className="w-full flex flex-col justify-center items-center gap-y-6 mb-8">
      {/* text container and card container */}
      <div className="w-full px-4 sm:px-6 md:px-10 lg:px-20 flex flex-col md:flex-row justify-center items-center gap-y-10 md:gap-y-0 gap-x-2 py-10">
        <h1 className="text-3xl sm:text-4xl md:text-4xl lg:text-[55px] font-urbanist text-[#13031F] font-normal w-full md:w-[58%] text-center md:text-left">
          <span className="block mb-0 sm:mb-12 md:mb-8">Our Comprehensive</span>
          <span className="block">Planning Services</span>
        </h1>

        <div className="flex flex-col justify-center items-start sm:w-full w-full md:w-[35%] gap-y-8">
          <div className="flex items-center justify-start gap-5">
            <h1 className="text-2xl sm:text-xl md:text-2xl text-[#13031FB2] font-urbanist font-medium">Our Service</h1>
            <h2 className='md:w-[5.9rem] sm:w-[6rem] w-[8rem] h-0 border'></h2>
          </div>
          <h2 className="text-sm sm:text-base text-[#13031FB2] font-urbanist">
            We offer a wide range of services tailored to meet your unique needs and preferences.
            Let us take care of everything from start to finish.
          </h2>
        </div>
      </div>

      {/* card container */}
      <div className="w-full flex justify-center items-center px-4 sm:px-6 md:px-10 lg:px-20">
        <CardGrid />
      </div>
    </div>
  );
};

export default HomeService;
