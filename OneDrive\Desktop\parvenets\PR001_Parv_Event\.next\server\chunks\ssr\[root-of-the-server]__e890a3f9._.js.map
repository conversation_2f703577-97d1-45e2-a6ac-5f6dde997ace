{"version": 3, "sources": [], "sections": [{"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/logo.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 150, height: 150, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA60lEQVR42lXOv2vCUBAHcAul0NJSunQoxbZ0TIeuToogMfDuRVGjOIiKvxLUPI15KhjiD6KD+GN2cBEH3VxFFyf/LBPFgMPdwfc+HOfwE+RoNDFzHMB03cXpQx8mch0xrJlbdW4FCk5zMY4o8DjWQJi3oXgDchQ59wYMBQU9jzTMzXQcjSnwYoOsir62BjaMFv+/6/O6OV1JFb/bAEqJj3xZ9OFS9qdARC8rid8eKfXGErgAQgPMQg9L1Wb4b9UJlUUa+uTk4IN9IV5Dr9YfGQq/yw7IUEH3XAXd2eBaFtr0MBlqvFtQ+ScrOwEySFZU89CdXwAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAka,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/downborderflower.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 511, height: 524, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAeElEQVR42nWOSw7DIAxEwRD8AxOFdNHe/6C1qy6JpZGseWONU3qYAgB8ID7xVKGUk3rfQsg5G4pcPPYB51kb0Xusa3st3m2ocus0NwBaqTWeil6sxzEa80vP6YGZqLYWfQOF7a/YA3rIfgFtTN0V8DPWumVaeJNUvp8DBVR+TmAQAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsQ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/footer/footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport logo from \"@/public/image/logo.png\";\r\nimport bottomflower from \"@/public/image/assests/downborderflower.png\";\r\nimport { Instagram, Facebook, Twitter } from \"lucide-react\";\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"relative bg-[#F6E4DB] py-12 px-4 sm:px-6 md:px-8 overflow-hidden\">\r\n      {/* Background flower decoration */}\r\n      <div className=\"absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none\">\r\n        <Image \r\n          src={bottomflower} \r\n          alt=\"Decorative flower\" \r\n          className=\"absolute top-0 right-0 w-auto h-full object-contain object-right-top\"\r\n          priority\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"max-w-7xl mx-auto relative z-10\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {/* Column 1: Logo and Description */}\r\n          <div className=\"space-y-4\">\r\n            <Image src={logo} alt=\"Parv Events Logo\" width={60} height={60} />\r\n            \r\n            <p className=\"text-sm text-gray-700 max-w-xs mt-4\">\r\n              From intimate gatherings to grand celebrations, our experienced wedding planners ensure you create unforgettable memories. We handle every detail so you can enjoy every moment.\r\n            </p>\r\n            \r\n            <div className=\"pt-2\">\r\n              <p className=\"text-sm text-gray-700\">+91-9735284928</p>\r\n              <p className=\"text-sm text-gray-700\"><EMAIL></p>\r\n            </div>\r\n            \r\n            {/* Social Media Icons */}\r\n            <div className=\"flex gap-3 pt-2\">\r\n              <Link href=\"#\" className=\"w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors\">\r\n                <Instagram size={18} />\r\n              </Link>\r\n              <Link href=\"#\" className=\"w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors\">\r\n                <Facebook size={18} />\r\n              </Link>\r\n              <Link href=\"#\" className=\"w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors\">\r\n                <Twitter size={18} />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Column 2: Quick Links */}\r\n          <div className=\"lg:ml-auto\">\r\n            <h3 className=\"text-base font-medium mb-4\">Quick Links</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Home</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">About Us</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Venue</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Service</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Gallery</Link></li>\r\n            </ul>\r\n          </div>\r\n          \r\n          {/* Column 3: Service new idss*/}\r\n          <div>\r\n            <h3 className=\"text-base font-medium mb-4\">Service</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Full-Service Planning</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Partial Planning</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Day-of Coordination</Link></li>\r\n              <li><Link href=\"#\" className=\"text-sm text-gray-700 hover:text-[#FE904B]\">Destination Weddings</Link></li>\r\n            </ul>\r\n          </div>\r\n          \r\n          {/* Column 4: Empty space to balance layout */}\r\n          <div className=\"hidden lg:block\"></div>\r\n        </div>\r\n        \r\n        {/* Bottom Section with Copyright and Policy Links */}\r\n        <div className=\"md:w-[50%]  border-t border-gray-200 mt-10 pt-6 flex flex-col sm:flex-row justify-between items-center\">\r\n          <div className=\"flex gap-4 mb-4 sm:mb-0\">\r\n            <Link href=\"#\" className=\"text-xs text-gray-700 hover:text-[#FE904B]\">Privacy policy</Link>\r\n            <Link href=\"#\" className=\"text-xs text-gray-700 hover:text-[#FE904B]\">Terms and Condition</Link>\r\n          </div>\r\n          <div className=\"text-xs text-gray-700\">\r\n            Copyright 2024\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,4TAAA,CAAA,UAAY;oBACjB,KAAI;oBACJ,WAAU;oBACV,QAAQ;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,8QAAA,CAAA,UAAI;wCAAE,KAAI;wCAAmB,OAAO;wCAAI,QAAQ;;;;;;kDAE5D,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAInD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DACvB,cAAA,8OAAC,4MAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;;;;;;0DAEnB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DACvB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,MAAM;;;;;;;;;;;0DAElB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DACvB,cAAA,8OAAC,wMAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;;;;;;0CAK9E,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;0DAC1E,8OAAC;0DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;;;;;;0CAK9E,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA6C;;;;;;kDACtE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA6C;;;;;;;;;;;;0CAExE,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/globalurl.ts"], "sourcesContent": ["// Global URL configuration\r\nexport const GLOBAL_CONFIG = {\r\n  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,\r\n  TIMEOUT: 10000, // 10 seconds\r\n} as const;\r\n\r\n// API endpoints configuration\r\nexport const API_ENDPOINTS = {\r\n  // Add your API endpoints here\r\n  // Example:\r\n  // AUTH: '/auth',\r\n  // USERS: '/users',\r\n  // EVENTS: '/events',\r\n} as const;\r\n\r\n// Simple URL builder - exactly like Postman: {{base_url}}/api/services\r\nexport const buildUrl = (endpoint: string): string => {\r\n  const baseUrl = GLOBAL_CONFIG.BASE_URL;\r\n  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\r\n  return `${baseUrl}/api${cleanEndpoint}`;\r\n};\r\n\r\n// Same function for consistency\r\nexport const buildApiUrl = buildUrl;\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;AACpB,MAAM,gBAAgB;IAC3B,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,gBAAgB;AAM7B;AAGO,MAAM,WAAW,CAAC;IACvB,MAAM,UAAU,cAAc,QAAQ;IACtC,MAAM,gBAAgB,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IAC1E,OAAO,GAAG,QAAQ,IAAI,EAAE,eAAe;AACzC;AAGO,MAAM,cAAc", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/customaxios.ts"], "sourcesContent": ["import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';\r\nimport { GLOBAL_CONFIG } from './globalurl';\r\n\r\n// Check if we're on the client side\r\nconst isClient = typeof window !== 'undefined';\r\n\r\n// Create custom axios instance\r\nconst customAxios: AxiosInstance = axios.create({\r\n  // Don't set baseURL here since buildUrl() provides full URLs\r\n  timeout: GLOBAL_CONFIG.TIMEOUT,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n  withCredentials: false, // Set to true if you need cookies\r\n});\r\n\r\n// Request interceptor\r\ncustomAxios.interceptors.request.use(\r\n  (config: InternalAxiosRequestConfig) => {\r\n    // Add auth token if available (only on client side)\r\n    if (typeof window !== 'undefined') {\r\n      const token = localStorage.getItem('authToken');\r\n      if (token && config.headers) {\r\n        config.headers.Authorization = `Bearer ${token}`;\r\n      }\r\n    }\r\n    \r\n    // Log request in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('🚀 API Request:', {\r\n        method: config.method?.toUpperCase(),\r\n        url: config.url,\r\n        baseURL: config.baseURL,\r\n        fullURL: config.baseURL ? `${config.baseURL}${config.url}` : config.url,\r\n        data: config.data,\r\n      });\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error: AxiosError) => {\r\n    console.error('❌ Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor\r\ncustomAxios.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    // Log response in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('✅ API Response:', {\r\n        status: response.status,\r\n        url: response.config.url,\r\n        data: response.data,\r\n      });\r\n    }\r\n    \r\n    return response;\r\n  },\r\n  (error: AxiosError) => {\r\n    // Handle common errors\r\n    if (error.response) {\r\n      const { status, data } = error.response;\r\n\r\n      switch (status) {\r\n        case 401:\r\n          // Unauthorized - redirect to login or refresh token\r\n          console.error('❌ Unauthorized access');\r\n          // localStorage.removeItem('authToken');\r\n          // window.location.href = '/login';\r\n          break;\r\n        case 403:\r\n          console.error('❌ Forbidden access');\r\n          break;\r\n        case 404:\r\n          console.error('❌ Resource not found');\r\n          break;\r\n        case 500:\r\n          console.error('❌ Server error');\r\n          break;\r\n        default:\r\n          console.error('❌ API Error:', data);\r\n      }\r\n    } else if (error.request) {\r\n      // Network error - no response received\r\n      console.error('❌ Network Error - No response from server:', {\r\n        message: error.message,\r\n        code: error.code,\r\n        config: {\r\n          url: error.config?.url,\r\n          method: error.config?.method,\r\n          baseURL: error.config?.baseURL,\r\n        }\r\n      });\r\n\r\n      // Add user-friendly error message\r\n      error.message = 'Network error: Unable to connect to server. Please check your internet connection.';\r\n    } else {\r\n      console.error('❌ Error:', error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default customAxios;\r\n\r\n// Helper functions for common HTTP methods\r\nexport const apiClient = {\r\n  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>\r\n    customAxios.get(url, config),\r\n  \r\n  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>\r\n    customAxios.post(url, data, config),\r\n  \r\n  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>\r\n    customAxios.put(url, data, config),\r\n  \r\n  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>\r\n    customAxios.patch(url, data, config),\r\n  \r\n  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>\r\n    customAxios.delete(url, config),\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,oCAAoC;AACpC,MAAM,WAAW,gBAAkB;AAEnC,+BAA+B;AAC/B,MAAM,cAA6B,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9C,6DAA6D;IAC7D,SAAS,gHAAA,CAAA,gBAAa,CAAC,OAAO;IAC9B,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;IACA,iBAAiB;AACnB;AAEA,sBAAsB;AACtB,YAAY,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;IACC,oDAAoD;IACpD,uCAAmC;;IAKnC;IAEA,6BAA6B;IAC7B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,mBAAmB;YAC7B,QAAQ,OAAO,MAAM,EAAE;YACvB,KAAK,OAAO,GAAG;YACf,SAAS,OAAO,OAAO;YACvB,SAAS,OAAO,OAAO,GAAG,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE,GAAG,OAAO,GAAG;YACvE,MAAM,OAAO,IAAI;QACnB;IACF;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,uBAAuB;AACvB,YAAY,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;IACC,8BAA8B;IAC9B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,mBAAmB;YAC7B,QAAQ,SAAS,MAAM;YACvB,KAAK,SAAS,MAAM,CAAC,GAAG;YACxB,MAAM,SAAS,IAAI;QACrB;IACF;IAEA,OAAO;AACT,GACA,CAAC;IACC,uBAAuB;IACvB,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ;QAEvC,OAAQ;YACN,KAAK;gBACH,oDAAoD;gBACpD,QAAQ,KAAK,CAAC;gBAGd;YACF,KAAK;gBACH,QAAQ,KAAK,CAAC;gBACd;YACF,KAAK;gBACH,QAAQ,KAAK,CAAC;gBACd;YACF,KAAK;gBACH,QAAQ,KAAK,CAAC;gBACd;YACF;gBACE,QAAQ,KAAK,CAAC,gBAAgB;QAClC;IACF,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,uCAAuC;QACvC,QAAQ,KAAK,CAAC,8CAA8C;YAC1D,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,SAAS,MAAM,MAAM,EAAE;YACzB;QACF;QAEA,kCAAkC;QAClC,MAAM,OAAO,GAAG;IAClB,OAAO;QACL,QAAQ,KAAK,CAAC,YAAY,MAAM,OAAO;IACzC;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa;AAGR,MAAM,YAAY;IACvB,KAAK,CAAU,KAAa,SAC1B,YAAY,GAAG,CAAC,KAAK;IAEvB,MAAM,CAAU,KAAa,MAAY,SACvC,YAAY,IAAI,CAAC,KAAK,MAAM;IAE9B,KAAK,CAAU,KAAa,MAAY,SACtC,YAAY,GAAG,CAAC,KAAK,MAAM;IAE7B,OAAO,CAAU,KAAa,MAAY,SACxC,YAAY,KAAK,CAAC,KAAK,MAAM;IAE/B,QAAQ,CAAU,KAAa,SAC7B,YAAY,MAAM,CAAC,KAAK;AAC5B", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/api/contact/contactApi.ts"], "sourcesContent": ["// Contact API module\r\nimport { apiClient } from '../../customaxios';\r\nimport { buildUrl } from '../../globalurl';\r\n\r\n// Types for contact API matching backend schema\r\nexport interface ContactFormData {\r\n  name: string;\r\n  email: string;\r\n  countryCode: string;\r\n  phoneNumber: string;\r\n  service: string;\r\n  message: string;\r\n}\r\n\r\nexport interface ContactResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data?: {\r\n    id: string;\r\n    name: string;\r\n    email: string;\r\n    countryCode: string;\r\n    phoneNumber: string;\r\n    phone: string; // Virtual field from backend\r\n    service: string;\r\n    message: string;\r\n    status: string;\r\n    priority: string;\r\n    createdAt: string;\r\n    updatedAt: string;\r\n  };\r\n}\r\n\r\nexport interface ContactError {\r\n  success: false;\r\n  message: string;\r\n  errors?: {\r\n    [key: string]: string;\r\n  };\r\n}\r\n\r\n// Contact API functions\r\nexport const contactApi = {\r\n  // Submit contact form\r\n  submitContact: async (contactData: ContactFormData): Promise<ContactResponse> => {\r\n    try {\r\n      const response = await apiClient.post<ContactResponse>(\r\n        buildUrl('/contacts'),\r\n        contactData\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Handle API errors\r\n      if (error.response?.data) {\r\n        throw error.response.data;\r\n      }\r\n      throw {\r\n        success: false,\r\n        message: 'Failed to submit contact form. Please try again.',\r\n      };\r\n    }\r\n  },\r\n\r\n  // Submit quote request (same endpoint, different service type)\r\n  submitQuote: async (quoteData: ContactFormData): Promise<ContactResponse> => {\r\n    try {\r\n      const response = await apiClient.post<ContactResponse>(\r\n        buildUrl('/contacts'),\r\n        {\r\n          ...quoteData,\r\n          service: quoteData.service || 'Quote Request'\r\n        }\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Handle API errors\r\n      if (error.response?.data) {\r\n        throw error.response.data;\r\n      }\r\n      throw {\r\n        success: false,\r\n        message: 'Failed to submit quote request. Please try again.',\r\n      };\r\n    }\r\n  }\r\n};\r\n\r\n// Helper function for form validation\r\nexport const validateContactForm = (data: Partial<ContactFormData>): { isValid: boolean; errors: { [key: string]: string } } => {\r\n  const errors: { [key: string]: string } = {};\r\n\r\n  // Name validation\r\n  if (!data.name?.trim()) {\r\n    errors.name = 'Name is required';\r\n  } else if (data.name.length > 100) {\r\n    errors.name = 'Name cannot exceed 100 characters';\r\n  }\r\n\r\n  // Email validation\r\n  if (!data.email?.trim()) {\r\n    errors.email = 'Email is required';\r\n  } else {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(data.email)) {\r\n      errors.email = 'Invalid email format';\r\n    }\r\n  }\r\n\r\n  // Country code validation\r\n  if (!data.countryCode?.trim()) {\r\n    errors.countryCode = 'Country code is required';\r\n  }\r\n\r\n  // Phone number validation\r\n  if (!data.phoneNumber?.trim()) {\r\n    errors.phoneNumber = 'Phone number is required';\r\n  } else {\r\n    const phoneRegex = /^[\\d\\s\\-]{8,15}$/;\r\n    const cleanPhone = data.phoneNumber.replace(/\\s|-/g, '');\r\n    if (!phoneRegex.test(cleanPhone)) {\r\n      errors.phoneNumber = 'Invalid phone number format (8-15 digits)';\r\n    }\r\n  }\r\n\r\n  // Service validation\r\n  if (!data.service?.trim()) {\r\n    errors.service = 'Service is required';\r\n  }\r\n\r\n  // Message validation\r\n  if (!data.message?.trim()) {\r\n    errors.message = 'Message is required';\r\n  } else if (data.message.length > 1000) {\r\n    errors.message = 'Message cannot exceed 1000 characters';\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n\r\n// Export types for components\r\nexport type { ContactFormData, ContactResponse, ContactError };\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;AACrB;AACA;;;AAwCO,MAAM,aAAa;IACxB,sBAAsB;IACtB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,IAAI,CACnC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,cACT;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,oBAAoB;YACpB,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,MAAM,MAAM,QAAQ,CAAC,IAAI;YAC3B;YACA,MAAM;gBACJ,SAAS;gBACT,SAAS;YACX;QACF;IACF;IAEA,+DAA+D;IAC/D,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,IAAI,CACnC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,cACT;gBACE,GAAG,SAAS;gBACZ,SAAS,UAAU,OAAO,IAAI;YAChC;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,oBAAoB;YACpB,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,MAAM,MAAM,QAAQ,CAAC,IAAI;YAC3B;YACA,MAAM;gBACJ,SAAS;gBACT,SAAS;YACX;QACF;IACF;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,SAAoC,CAAC;IAE3C,kBAAkB;IAClB,IAAI,CAAC,KAAK,IAAI,EAAE,QAAQ;QACtB,OAAO,IAAI,GAAG;IAChB,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;QACjC,OAAO,IAAI,GAAG;IAChB;IAEA,mBAAmB;IACnB,IAAI,CAAC,KAAK,KAAK,EAAE,QAAQ;QACvB,OAAO,KAAK,GAAG;IACjB,OAAO;QACL,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,KAAK,GAAG;YAChC,OAAO,KAAK,GAAG;QACjB;IACF;IAEA,0BAA0B;IAC1B,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ;QAC7B,OAAO,WAAW,GAAG;IACvB;IAEA,0BAA0B;IAC1B,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ;QAC7B,OAAO,WAAW,GAAG;IACvB,OAAO;QACL,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,WAAW,CAAC,OAAO,CAAC,SAAS;QACrD,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa;YAChC,OAAO,WAAW,GAAG;QACvB;IACF;IAEA,qBAAqB;IACrB,IAAI,CAAC,KAAK,OAAO,EAAE,QAAQ;QACzB,OAAO,OAAO,GAAG;IACnB;IAEA,qBAAqB;IACrB,IAAI,CAAC,KAAK,OAAO,EAAE,QAAQ;QACzB,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,MAAM;QACrC,OAAO,OAAO,GAAG;IACnB;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/quote-modal/quote-modal.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState } from 'react';\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport { contactApi, ContactFormData, validateContactForm } from \"@/lib/api/contact/contactApi\";\r\nimport { toast } from 'react-toastify';\r\n\r\ninterface QuoteModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSubmit?: (e: React.FormEvent) => void; // Made optional since we'll handle internally\r\n}\r\n\r\nconst QuoteModal: React.FC<QuoteModalProps> = ({ isOpen, onClose, onSubmit }) => {\r\n  // Form state\r\n  const [formData, setFormData] = useState<ContactFormData>({\r\n    name: '',\r\n    email: '',\r\n    countryCode: '+91',\r\n    phoneNumber: '',\r\n    service: 'Quote Request',\r\n    message: ''\r\n  });\r\n\r\n  const [errors, setErrors] = useState<{ [key: string]: string }>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // <PERSON>le input changes\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n\r\n    // Clear error for this field when user starts typing\r\n    if (errors[name]) {\r\n      setErrors(prev => ({\r\n        ...prev,\r\n        [name]: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const validation = validateContactForm(formData);\r\n    if (!validation.isValid) {\r\n      setErrors(validation.errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    setErrors({});\r\n\r\n    try {\r\n      const response = await contactApi.submitQuote(formData);\r\n\r\n      if (response.success) {\r\n        // Show success toast\r\n        toast.success('🎉 Thank you for your quote request! We\\'ll get back to you soon.', {\r\n          position: \"top-right\",\r\n          autoClose: 5000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n\r\n        // Reset form\r\n        setFormData({\r\n          name: '',\r\n          email: '',\r\n          countryCode: '+91',\r\n          phoneNumber: '',\r\n          service: 'Quote Request',\r\n          message: ''\r\n        });\r\n\r\n        // Close modal after showing success\r\n        setTimeout(() => {\r\n          onClose();\r\n        }, 1500);\r\n      } else {\r\n        // Show error toast\r\n        toast.error(response.message || 'Failed to submit quote request. Please try again.', {\r\n          position: \"top-right\",\r\n          autoClose: 5000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Quote form error:', error);\r\n\r\n      if (error.errors) {\r\n        setErrors(error.errors);\r\n        toast.error('Please fix the errors below and try again.', {\r\n          position: \"top-right\",\r\n          autoClose: 5000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      } else {\r\n        toast.error(error.message || 'Failed to submit quote request. Please try again.', {\r\n          position: \"top-right\",\r\n          autoClose: 5000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Reset form when modal closes\r\n  const handleClose = () => {\r\n    setFormData({\r\n      name: '',\r\n      email: '',\r\n      countryCode: '+91',\r\n      phoneNumber: '',\r\n      service: 'Quote Request',\r\n      message: ''\r\n    });\r\n    setErrors({});\r\n    onClose();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 overflow-y-auto\">\r\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-lg relative my-4 max-h-[90vh] overflow-y-auto\">\r\n        {/* Close button */}\r\n        <button\r\n          onClick={handleClose}\r\n          className=\"absolute right-4 top-4 text-gray-500 hover:text-gray-700 z-10\"\r\n          aria-label=\"Close modal\"\r\n        >\r\n          <IoMdClose size={24} />\r\n        </button>\r\n\r\n        {/* Modal header */}\r\n        <div className=\"p-4 sm:p-6 border-b\">\r\n          <h3 className=\"text-xl sm:text-2xl font-medium text-gray-900\">Request a Quote</h3>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            Fill out the form below and our team will get back to you shortly.\r\n          </p>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Modal body */}\r\n        <form onSubmit={handleSubmit} className=\"p-4 sm:p-6\">\r\n          <div className=\"space-y-3 sm:space-y-4\">\r\n            {/* Row 1: Name and Email */}\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"w-full sm:w-1/2\">\r\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Full Name *\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  required\r\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${\r\n                    errors.name ? 'border-red-500' : 'border-gray-300'\r\n                  }`}\r\n                  placeholder=\"Your full name\"\r\n                />\r\n                {errors.name && <p className=\"text-red-500 text-xs mt-1\">{errors.name}</p>}\r\n              </div>\r\n              \r\n              <div className=\"w-full sm:w-1/2\">\r\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Email Address *\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  required\r\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${\r\n                    errors.email ? 'border-red-500' : 'border-gray-300'\r\n                  }`}\r\n                  placeholder=\"<EMAIL>\"\r\n                />\r\n                {errors.email && <p className=\"text-red-500 text-xs mt-1\">{errors.email}</p>}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Row 2: Country Code and Phone Number */}\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"w-full sm:w-1/3\">\r\n                <label htmlFor=\"countryCode\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Country Code *\r\n                </label>\r\n                <select\r\n                  id=\"countryCode\"\r\n                  name=\"countryCode\"\r\n                  value={formData.countryCode}\r\n                  onChange={handleChange}\r\n                  required\r\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${\r\n                    errors.countryCode ? 'border-red-500' : 'border-gray-300'\r\n                  }`}\r\n                >\r\n                  <option value=\"+91\">+91 (India)</option>\r\n                  <option value=\"+1\">+1 (US/Canada)</option>\r\n                  <option value=\"+44\">+44 (UK)</option>\r\n                  <option value=\"+61\">+61 (Australia)</option>\r\n                  <option value=\"+971\">+971 (UAE)</option>\r\n                  <option value=\"+65\">+65 (Singapore)</option>\r\n                </select>\r\n                {errors.countryCode && <p className=\"text-red-500 text-xs mt-1\">{errors.countryCode}</p>}\r\n              </div>\r\n\r\n              <div className=\"w-full sm:w-2/3\">\r\n                <label htmlFor=\"phoneNumber\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Phone Number *\r\n                </label>\r\n                <input\r\n                  type=\"tel\"\r\n                  id=\"phoneNumber\"\r\n                  name=\"phoneNumber\"\r\n                  value={formData.phoneNumber}\r\n                  onChange={handleChange}\r\n                  required\r\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${\r\n                    errors.phoneNumber ? 'border-red-500' : 'border-gray-300'\r\n                  }`}\r\n                  placeholder=\"9876543210\"\r\n                />\r\n                {errors.phoneNumber && <p className=\"text-red-500 text-xs mt-1\">{errors.phoneNumber}</p>}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Row 3: Service */}\r\n            <div>\r\n              <label htmlFor=\"service\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Service Required *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"service\"\r\n                name=\"service\"\r\n                value={formData.service}\r\n                onChange={handleChange}\r\n                required\r\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${\r\n                  errors.service ? 'border-red-500' : 'border-gray-300'\r\n                }`}\r\n                placeholder=\"e.g., Wedding Planning, Event Management\"\r\n              />\r\n              {errors.service && <p className=\"text-red-500 text-xs mt-1\">{errors.service}</p>}\r\n            </div>\r\n\r\n            {/* Row 4: Event Details */}\r\n            <div>\r\n              <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Event Details *\r\n              </label>\r\n              <textarea\r\n                id=\"message\"\r\n                name=\"message\"\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                rows={3}\r\n                required\r\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${\r\n                  errors.message ? 'border-red-500' : 'border-gray-300'\r\n                }`}\r\n                placeholder=\"Tell us more about your event requirements...\"\r\n              ></textarea>\r\n              {errors.message && <p className=\"text-red-500 text-xs mt-1\">{errors.message}</p>}\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                {formData.message.length}/1000 characters\r\n              </p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mt-4 sm:mt-6\">\r\n            <button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className={`w-full py-2 px-4 rounded-md transition-colors duration-300 ${\r\n                isSubmitting\r\n                  ? 'bg-gray-400 cursor-not-allowed'\r\n                  : 'bg-[#FE904B] hover:bg-[#e87f3d] text-white'\r\n              }`}\r\n            >\r\n              {isSubmitting ? 'Submitting...' : 'Submit Quote Request'}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuoteModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAYA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;IAC1E,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,MAAM;QACN,OAAO;QACP,aAAa;QACb,aAAa;QACb,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uBAAuB;IACvB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,qDAAqD;QACrD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,gBAAgB;QAChB,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;QACvC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,UAAU,WAAW,MAAM;YAC3B;QACF;QAEA,gBAAgB;QAChB,UAAU,CAAC;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,mIAAA,CAAA,aAAU,CAAC,WAAW,CAAC;YAE9C,IAAI,SAAS,OAAO,EAAE;gBACpB,qBAAqB;gBACrB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qEAAqE;oBACjF,UAAU;oBACV,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;gBAEA,aAAa;gBACb,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,aAAa;oBACb,SAAS;oBACT,SAAS;gBACX;gBAEA,oCAAoC;gBACpC,WAAW;oBACT;gBACF,GAAG;YACL,OAAO;gBACL,mBAAmB;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI,qDAAqD;oBACnF,UAAU;oBACV,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,IAAI,MAAM,MAAM,EAAE;gBAChB,UAAU,MAAM,MAAM;gBACtB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,8CAA8C;oBACxD,UAAU;oBACV,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI,qDAAqD;oBAChF,UAAU;oBACV,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,+BAA+B;IAC/B,MAAM,cAAc;QAClB,YAAY;YACV,MAAM;YACN,OAAO;YACP,aAAa;YACb,aAAa;YACb,SAAS;YACT,SAAS;QACX;QACA,UAAU,CAAC;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC,8IAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAC9D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAQ5C,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA+C;;;;;;8DAG/E,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,iHAAiH,EAC3H,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oDACF,aAAY;;;;;;gDAEb,OAAO,IAAI,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI;;;;;;;;;;;;sDAGvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,iHAAiH,EAC3H,OAAO,KAAK,GAAG,mBAAmB,mBAClC;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;8CAK3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAA+C;;;;;;8DAGtF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,iHAAiH,EAC3H,OAAO,WAAW,GAAG,mBAAmB,mBACxC;;sEAEF,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAM;;;;;;;;;;;;gDAErB,OAAO,WAAW,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,WAAW;;;;;;;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAA+C;;;;;;8DAGtF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,QAAQ;oDACR,WAAW,CAAC,iHAAiH,EAC3H,OAAO,WAAW,GAAG,mBAAmB,mBACxC;oDACF,aAAY;;;;;;gDAEb,OAAO,WAAW,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,WAAW;;;;;;;;;;;;;;;;;;8CAKvF,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,QAAQ;4CACR,WAAW,CAAC,iHAAiH,EAC3H,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4CACF,aAAY;;;;;;wCAEb,OAAO,OAAO,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO;;;;;;;;;;;;8CAI7E,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,MAAM;4CACN,QAAQ;4CACR,WAAW,CAAC,iHAAiH,EAC3H,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4CACF,aAAY;;;;;;wCAEb,OAAO,OAAO,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO;;;;;;sDAC3E,8OAAC;4CAAE,WAAU;;gDACV,SAAS,OAAO,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC,2DAA2D,EACrE,eACI,mCACA,8CACJ;0CAED,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/navbar/navbar.tsx"], "sourcesContent": ["'use client'\r\nimport Image from 'next/image';\r\nimport React, { useState, useEffect } from 'react';\r\nimport img1 from \"@/public/image/logo.png\";\r\nimport { IoMdAdd, IoMdMenu, IoMdClose } from \"react-icons/io\";\r\nimport { FaInstagram, FaYoutube } from \"react-icons/fa\";\r\nimport { FaSquareFacebook } from \"react-icons/fa6\";\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport QuoteModal from '../quote-modal/quote-modal';\r\n\r\nconst Navbar = () => {\r\n  const pathname = usePathname();\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [quoteModalOpen, setQuoteModalOpen] = useState(false);\r\n  const [isMobileOrTablet, setIsMobileOrTablet] = useState(false);\r\n  const [scrolled, setScrolled] = useState(false);\r\n\r\n  // Check if the screen is mobile or tablet size\r\n  useEffect(() => {\r\n    const checkScreenSize = () => {\r\n      setIsMobileOrTablet(window.innerWidth < 1024);\r\n    };\r\n    \r\n    // Initial check\r\n    checkScreenSize();\r\n    \r\n    // Add event listener for window resize\r\n    window.addEventListener('resize', checkScreenSize);\r\n    \r\n    // Cleanuppppp\r\n    return () => window.removeEventListener('resize', checkScreenSize);\r\n  }, []);\r\n\r\n  // Add scroll event listener to detect when page is scrolled\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      if (window.scrollY > 10) {\r\n        setScrolled(true);\r\n      } else {\r\n        setScrolled(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    \r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n    };\r\n  }, []);\r\n\r\n  const tabs = [\r\n    { name: \"Home\", path: \"/\" },\r\n    { name: \"About Us\", path: \"/about\" },\r\n    { name: \"Services\", path: \"/services\" },\r\n    { name: \"Venue\", path: \"/venue\" },\r\n    { name: \"Gallary\", path: \"/gallary\" },\r\n    { name: \"Blog\", path: \"/blog\" },\r\n    { name: \"Contact\", path: \"/contact\" },\r\n  ];\r\n\r\n\r\n\r\n  return (\r\n    <>\r\n      {/* Spacer div to prevent content from jumping when navbar becomes fixed */}\r\n      <div className=\"h-[76px]\"></div>\r\n      \r\n      <header className={`w-full border-b border-gray-200 bg-white shadow-sm fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${\r\n        scrolled ? 'shadow-md' : ''\r\n      }`}>\r\n        <div className=\"max-w-7xl mx-auto px-4 py-3 flex justify-between items-center z-50\">\r\n          {/* Logo */}\r\n          <Link href=\"/\">\r\n            <Image src={img1} alt=\"logo\" width={60} height={60} className=\"cursor-pointer\" />\r\n          </Link>\r\n\r\n          {/* Desktop Nav - only visible on large screens */}\r\n          <nav className=\"hidden lg:flex gap-6 items-center\">\r\n            <ul className=\"flex gap-6 font-urbanist text-sm tracking-wider\">\r\n              {tabs.map((item, index) => (\r\n                <li key={index}>\r\n                  <Link\r\n                    href={item.path}\r\n                    className={`uppercase transition-colors duration-200 hover:text-[#FE904B] ${\r\n                      pathname === item.path ? 'text-[#FE904B] font-semibold' : 'text-[#0D0D0D]'\r\n                    }`}\r\n                  >\r\n                    {item.name}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </nav>\r\n\r\n          {/* Social & Button - only visible on large screens */}\r\n          <div className=\"hidden lg:flex items-center gap-5\">\r\n            <div className=\"flex gap-3 text-lg\">\r\n              <FaSquareFacebook className=\"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200\" />\r\n              <FaInstagram className=\"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200\" />\r\n              <FaYoutube className=\"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200\" />\r\n            </div>\r\n            <button \r\n              onClick={() => setQuoteModalOpen(true)} \r\n              className=\"flex items-center py-2 px-4 border border-transparent hover:border-[#FE904B] hover:text-[#FE904B] text-sm transition\"\r\n            >\r\n              GET A QUOTE <IoMdAdd className=\"ml-1\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Mobile/Tablet Menu Toggle Button */}\r\n          <button\r\n            className=\"lg:hidden text-2xl text-[#0D0D0D]\"\r\n            onClick={() => setMenuOpen(!menuOpen)}\r\n            aria-label=\"Toggle Menu\"\r\n          >\r\n            {menuOpen ? <IoMdClose /> : <IoMdMenu />}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Mobile/Tablet Menu - slides in from the right */}\r\n        <div \r\n          className={`fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${\r\n            menuOpen ? 'translate-x-0' : 'translate-x-full'\r\n          } lg:hidden`}\r\n        >\r\n          <div className=\"p-4 border-b flex justify-between items-center\">\r\n            <h3 className=\"font-medium\">Menu</h3>\r\n            <button \r\n              onClick={() => setMenuOpen(false)}\r\n              className=\"text-2xl text-[#0D0D0D]\"\r\n              aria-label=\"Close menu\"\r\n            >\r\n              <IoMdClose />\r\n            </button>\r\n          </div>\r\n          \r\n          <ul className=\"flex flex-col gap-4 p-4 text-sm font-urbanist\">\r\n            {tabs.map((item, index) => (\r\n              <li key={index}>\r\n                <Link\r\n                  href={item.path}\r\n                  className={`block py-2 uppercase transition-colors duration-200 hover:text-[#FE904B] ${\r\n                    pathname === item.path ? 'text-[#FE904B] font-semibold' : 'text-[#0D0D0D]'\r\n                  }`}\r\n                  onClick={() => setMenuOpen(false)}\r\n                >\r\n                  {item.name}\r\n                </Link>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n          \r\n          <div className=\"p-4 border-t\">\r\n            <div className=\"flex gap-3 text-lg mb-4 justify-center\">\r\n              <FaSquareFacebook className=\"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]\" />\r\n              <FaInstagram className=\"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]\" />\r\n              <FaYoutube className=\"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]\" />\r\n            </div>\r\n            <button \r\n              onClick={() => {\r\n                setQuoteModalOpen(true);\r\n                setMenuOpen(false);\r\n              }} \r\n              className=\"w-full flex items-center justify-center py-2 px-4 border border-[#FE904B] text-[#FE904B] hover:bg-[#FE904B] hover:text-white text-sm transition\"\r\n            >\r\n              GET A QUOTE <IoMdAdd className=\"ml-1\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Overlay for mobile/tablet menu */}\r\n        {menuOpen && (\r\n          <div \r\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\r\n            onClick={() => setMenuOpen(false)}\r\n          ></div>\r\n        )}\r\n      </header>\r\n\r\n      {/* Quote Modal Component */}\r\n      <QuoteModal\r\n        isOpen={quoteModalOpen}\r\n        onClose={() => setQuoteModalOpen(false)}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;;AAWA,MAAM,SAAS;IACb,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,oBAAoB,OAAO,UAAU,GAAG;QAC1C;QAEA,gBAAgB;QAChB;QAEA,uCAAuC;QACvC,OAAO,gBAAgB,CAAC,UAAU;QAElC,cAAc;QACd,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,IAAI;gBACvB,YAAY;YACd,OAAO;gBACL,YAAY;YACd;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,MAAM,OAAO;QACX;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAID,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAO,WAAW,CAAC,+GAA+G,EACjI,WAAW,cAAc,IACzB;;kCACA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAK,8QAAA,CAAA,UAAI;oCAAE,KAAI;oCAAO,OAAO;oCAAI,QAAQ;oCAAI,WAAU;;;;;;;;;;;0CAIhE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,8DAA8D,EACxE,aAAa,KAAK,IAAI,GAAG,iCAAiC,kBAC1D;0DAED,KAAK,IAAI;;;;;;2CAPL;;;;;;;;;;;;;;;0CAef,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;0DAC5B,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;kDAEvB,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;4CACX;0DACa,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAKnC,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,YAAY,CAAC;gCAC5B,cAAW;0CAEV,yBAAW,8OAAC,8IAAA,CAAA,YAAS;;;;yDAAM,8OAAC,8IAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;kCAKzC,8OAAC;wBACC,WAAW,CAAC,gHAAgH,EAC1H,WAAW,kBAAkB,mBAC9B,UAAU,CAAC;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;;;;;;;0CAId,8OAAC;gCAAG,WAAU;0CACX,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,yEAAyE,EACnF,aAAa,KAAK,IAAI,GAAG,iCAAiC,kBAC1D;4CACF,SAAS,IAAM,YAAY;sDAE1B,KAAK,IAAI;;;;;;uCARL;;;;;;;;;;0CAcb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;0DAC5B,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;kDAEvB,8OAAC;wCACC,SAAS;4CACP,kBAAkB;4CAClB,YAAY;wCACd;wCACA,WAAU;;4CACX;0DACa,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;oBAMpC,0BACC,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,YAAY;;;;;;;;;;;;0BAMjC,8OAAC,uJAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,kBAAkB;;;;;;;;AAIzC;uCAEe", "debugId": null}}]}