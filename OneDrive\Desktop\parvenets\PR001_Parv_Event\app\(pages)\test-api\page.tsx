"use client";

import React, { useState, useEffect } from 'react';
import { testApi } from '@/lib/api/test/testApi';

export default function TestApiPage() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<any>(null);

  useEffect(() => {
    // Show debug configuration on load
    setConfig(testApi.debugConfig());
  }, []);

  const runConnectionTest = async () => {
    setLoading(true);
    try {
      const result = await testApi.testConnection();
      setResults(prev => [...prev, { type: 'Connection Test', ...result }]);
    } catch (error) {
      console.error('Test failed:', error);
    }
    setLoading(false);
  };

  const runApiTest = async () => {
    setLoading(true);
    try {
      const result = await testApi.testApiEndpoint();
      setResults(prev => [...prev, { type: 'API Test', ...result }]);
    } catch (error) {
      console.error('API test failed:', error);
    }
    setLoading(false);
  };

  const runMultipleTests = async () => {
    setLoading(true);
    try {
      const results = await testApi.testMultipleEndpoints();
      setResults(prev => [...prev, ...results.map(r => ({ type: 'Multiple Tests', ...r }))]);
    } catch (error) {
      console.error('Multiple tests failed:', error);
    }
    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API Network Test</h1>
        
        {/* Configuration Debug */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Configuration Debug</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>

        {/* Test Buttons */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Network Tests</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={runConnectionTest}
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              Test Connection
            </button>
            <button
              onClick={runApiTest}
              disabled={loading}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              Test API Endpoint
            </button>
            <button
              onClick={runMultipleTests}
              disabled={loading}
              className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              Test Multiple Endpoints
            </button>
            <button
              onClick={clearResults}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
            >
              Clear Results
            </button>
          </div>
          {loading && (
            <div className="mt-4 text-blue-600">
              Running tests...
            </div>
          )}
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          {results.length === 0 ? (
            <p className="text-gray-500">No test results yet. Run a test to see results.</p>
          ) : (
            <div className="space-y-4">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded border-l-4 ${
                    result.success
                      ? 'border-green-500 bg-green-50'
                      : 'border-red-500 bg-red-50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{result.type}</span>
                    <span className={`px-2 py-1 rounded text-sm ${
                      result.success
                        ? 'bg-green-200 text-green-800'
                        : 'bg-red-200 text-red-800'
                    }`}>
                      {result.success ? 'SUCCESS' : 'FAILED'}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2">{result.message}</p>
                  <p className="text-xs text-gray-500 mb-2">{result.timestamp}</p>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm text-gray-600">
                        View Details
                      </summary>
                      <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
