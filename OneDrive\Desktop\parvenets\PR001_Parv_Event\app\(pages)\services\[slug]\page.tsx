import { Suspense } from "react";
import ServiceDetailClient from "./service-detail-client";
import { Metadata } from "next";

// Define the types for the params
type PageParams = {
  slug: string;
};

// Generate metadata function to handle dynamic params
export async function generateMetadata({ 
  params 
}: { 
  params: PageParams 
}): Promise<Metadata> {
  // Await params before using it
  const resolvedParams = await Promise.resolve(params);
  
  return {
    title: `Service: ${resolvedParams.slug}`,
  };
}

// Server component that receives the correct Next.js page props
export default async function ServiceDetailPage({
  params,
  searchParams,
}: {
  params: PageParams;
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  // Await params and searchParams before using them
  const resolvedParams = await Promise.resolve(params);
  const resolvedSearchParams = await Promise.resolve(searchParams);
  
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ServiceDetailClient 
        params={resolvedParams} 
        searchParams={resolvedSearchParams} 
      />
    </Suspense>
  );
}






