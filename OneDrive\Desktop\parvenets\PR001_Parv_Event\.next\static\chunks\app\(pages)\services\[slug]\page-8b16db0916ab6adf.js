(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[97],{654:(e,t,r)=>{Promise.resolve().then(r.bind(r,5958))},1168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a={src:"/_next/static/media/pinktree.b502aac7.png",height:195,width:256,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAKlBMVEVMaXH/q9bdorPZoK7YoK/RlafMnaeyg43Ll6LCjprTnavgq7jWobB/XV3u3GU9AAAADnRSTlMAAjtgjSgXUCVqL0x4Hph5UpkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAQAwDMMwp3+PP91JgCQsgBqOJ4VviwKb2ysRHuvtFLS3CfgR1AC3VmqfqgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6}},1506:(e,t,r)=>{"use strict";r.d(t,{_Z:()=>i,dU:()=>n,jE:()=>o,s7:()=>l});var a=r(7693),s=r(1966);let l={getServices:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&t.append(r,a.toString())}),(await a.u.get((0,s.c$)("/services?".concat(t.toString())))).data},getServiceById:async e=>(await a.u.get((0,s.c$)("/services/".concat(e)))).data,getServiceBySlug:async e=>(await a.u.get((0,s.c$)("/services/slug/".concat(e)))).data,getServiceCategories:async()=>(await a.u.get((0,s.c$)("/services/categories"))).data.categories},o={getGallery:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&(Array.isArray(a)?a.forEach(e=>t.append(r,e.toString())):t.append(r,a.toString()))}),(await a.u.get((0,s.c$)("/gallery?".concat(t.toString())))).data},getGalleryItems:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o.getGallery(e)},getGalleryCategories:async()=>(await a.u.get((0,s.c$)("/gallery/categories"))).data.categories,getGalleryByCategory:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;null!=a&&r.append(t,a.toString())}),(await a.u.get((0,s.c$)("/gallery/category/".concat(e,"?").concat(r.toString())))).data}},i={getReviews:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&t.append(r,a.toString())}),(await a.u.get((0,s.c$)("/reviews?".concat(t.toString())))).data},getServiceReviews:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;null!=a&&r.append(t,a.toString())}),(await a.u.get((0,s.c$)("/reviews/service/".concat(e,"?").concat(r.toString())))).data}},n={formatPrice:e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(e),createSlug:e=>e.toLowerCase().replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),truncateText:(e,t)=>e.length<=t?e:e.substring(0,t).trim()+"...",formatRating:e=>e.toFixed(1),getStarRating:e=>{let t=Math.floor(e),r=e%1>=.5;return{filled:t,half:r,empty:5-t-!!r}},formatRelationship:e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),formatCardTitle:e=>e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "),formatCardDescription:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150;return e?e.length<=t?e:e.substring(0,t).trim()+"...":""},formatGalleryCategory:e=>e?e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):"All",formatGalleryTitle:e=>e?e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):""};l.getServices,l.getServiceById,l.getServiceBySlug,l.getServiceCategories,o.getGallery,o.getGalleryCategories,o.getGalleryByCategory,i.getReviews,i.getServiceReviews},1966:(e,t,r)=>{"use strict";r.d(t,{JW:()=>a,KB:()=>l,c$:()=>s});let a={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},s=e=>{let t=a.BASE_URL,r=e.startsWith("/")?e:"/".concat(e);return"".concat(t,"/api").concat(r)},l=s},5958:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(5155),s=r(2115),l=r(7771),o=r(6766),i=r(1506),n=r(8543);let c=e=>{let{params:t,searchParams:r}=e,[c,d]=(0,s.useState)(null),[x,g]=(0,s.useState)(!0),[m,h]=(0,s.useState)(null);if(!t||!t.slug)return(0,a.jsx)("div",{className:"py-20 text-center",children:(0,a.jsx)("p",{children:"Invalid service URL."})});let u=async()=>{try{g(!0),h(null);let e=await i.s7.getServiceById(t.slug);if(e.success&&e.data)d(e.data);else throw Error("Service not found")}catch(e){console.error("Error fetching service:",e),h(e.message||"Failed to load service"),n.oR.error("Failed to load service details.")}finally{g(!1)}};return((0,s.useEffect)(()=>{u()},[t.slug]),x)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{title:"LOADING...",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICES",href:"/services"}]}),(0,a.jsxs)("div",{className:"py-20 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FE904B] mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading service details..."})]})]}):m||!c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{title:"SERVICE NOT FOUND",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICES",href:"/services"}]}),(0,a.jsxs)("div",{className:"py-20 text-center",children:[(0,a.jsxs)("div",{className:"text-red-500 mb-4",children:[(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Service not found"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:m})]}),(0,a.jsx)("button",{onClick:u,className:"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors mr-4",children:"Try Again"}),(0,a.jsx)("a",{href:"/services",className:"px-4 py-2 border border-[#FE904B] text-[#FE904B] rounded-md hover:bg-[#FE904B] hover:text-white transition-colors",children:"Back to Services"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{title:"SERVICE DETAILS",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICE DETAILS",href:"/services"}]}),(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-white py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-xs sm:text-sm text-[#BC7B77] uppercase tracking-wider mb-3 sm:mb-4 font-medium",children:"LATEST SERVICE"}),(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 uppercase text-gray-900 leading-tight",children:c.title}),(0,a.jsxs)("div",{className:"max-w-[68rem] mx-auto space-y-4 sm:space-y-6",children:[(0,a.jsx)("p",{className:"text-gray-700 text-sm sm:text-base lg:text-lg leading-relaxed text-center sm:text-left px-4 sm:px-0",children:c.description}),c.description2&&(0,a.jsx)("p",{className:"text-gray-600 text-xs sm:text-sm lg:text-base leading-relaxed text-center sm:text-left px-4 sm:px-0",children:c.description2})]})]})}),(0,a.jsx)("div",{className:"py-6 sm:py-8 lg:py-12",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"relative h-[250px] sm:h-[350px] md:h-[450px] lg:h-[500px] xl:h-[550px] rounded-xl sm:rounded-2xl overflow-hidden shadow-xl",children:[(0,a.jsx)(o.default,{src:c.image,alt:c.title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-700"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})})}),(0,a.jsx)("div",{className:"py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-orange-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"text-center mb-12 sm:mb-16",children:(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-gray-900 mb-4 sm:mb-6 uppercase tracking-wide",children:"How We Do It"})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10",children:c.howWeDoIt&&c.howWeDoIt.length>0?c.howWeDoIt.slice(0,3).map((e,t)=>(0,a.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 border ",style:{borderColor:"rgba(229, 218, 207, 1)"},children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl  text-center  font-semibold text-gray-900 leading-tight ",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-center text-sm sm:text-base leading-relaxed",children:e.description})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 "})]},t)):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 ",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ",children:"Professional Planning"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base leading-relaxed",children:"Our experienced team creates detailed plans tailored to your specific needs and vision."})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})]}),(0,a.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-orange-200 hover:-translate-y-2",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ",children:"Quality Execution"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base leading-relaxed",children:"We ensure every detail is executed flawlessly with attention to quality and precision."})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})]}),(0,a.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 ",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ",children:"Memorable Experience"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base leading-relaxed",children:"Creating unforgettable moments that exceed your expectations and delight your guests."})]}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 "})]})]})})]})})]})}},7693:(e,t,r)=>{"use strict";r.d(t,{u:()=>o});var a=r(3464),s=r(1966);let l=a.A.create({timeout:s.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});l.interceptors.request.use(e=>{{let t=localStorage.getItem("authToken");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>(console.error("❌ Request Error:",e),Promise.reject(e))),l.interceptors.response.use(e=>e,e=>{if(e.response){let{status:t,data:r}=e.response;switch(t){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",r)}}else if(e.request){var t,r,a;console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:null==(t=e.config)?void 0:t.url,method:null==(r=e.config)?void 0:r.method,baseURL:null==(a=e.config)?void 0:a.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."}else console.error("❌ Error:",e.message);return Promise.reject(e)});let o={get:(e,t)=>l.get(e,t),post:(e,t,r)=>l.post(e,t,r),put:(e,t,r)=>l.put(e,t,r),patch:(e,t,r)=>l.patch(e,t,r),delete:(e,t)=>l.delete(e,t)}},7771:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(5155),s=r(2115),l=r(6766),o=r(6874),i=r.n(o),n=r(1168);let c=e=>{let{title:t,breadcrumbs:r}=e;return(0,a.jsx)("div",{className:"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0",children:t}),(0,a.jsx)("div",{className:"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10",children:(0,a.jsx)(l.default,{src:n.default,alt:"Cherry Blossom",width:130,height:100,style:{width:"auto",height:"auto"},className:"object-contain"})}),(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm z-20",children:r.map((e,t)=>(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)(i(),{href:e.href,className:"hover:text-[#FE904B] transition-colors",children:e.label}),t<r.length-1&&(0,a.jsx)("span",{className:"text-gray-400",children:"›"})]},t))})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[83,854,543,441,684,358],()=>t(654)),_N_E=e.O()}]);