"use client";
import React from 'react';
import Image from 'next/image';
import dots from "@/public/image/assests/aboutdots.png";
import weddingExperience from "@/public/image/assests/moments/aboutexperience.png"; // Make sure this image exists
import checkboxicon from "@/public/image/icons/checkborder.svg"
import Link from 'next/link';
const Experience = () => {
  return (
    <div className="py-16 px-4 sm:px-6 md:px-8 relative mb-8">
      <div className="max-w-5xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          {/* Left side - Image */}
          <div className="relative">
            <div className="relative ">
              <Image 
                src={weddingExperience} 
                alt="Wedding Experience" 
                width={450}
                height={400}
                className=" object-cover"
              />
            </div>
            
            {/* Activity label */}
            <div className="absolute -bottom-12 right-0 bg-[#FDEBE1] p-4 rounded-lg shadow-md w-48 flex flex-col items-center justify-center gap-y-2">
            <span className='bg-[#E5DACF] w-[3rem] h-[3rem] rounded-full'></span>
              <p className="text-[15-75px] text-[#FE904B] font-medium">DAILY ACTIVITY</p>
              <p className="text-[13px] font-medium ">Wedding Excellence</p>
            </div>
            
            {/* Dots decoration */}
            <div className="absolute -bottom-8 left-[14rem] -z-10">
              <Image 
                src={dots} 
                alt="Decorative dots" 
                width={100}
                height={50}
              />
            </div>
          </div>
          
          {/* Right side - Content */}
          <div className="pt-12 md:pt-0 md:pl-8">
            <div className="text-[11px] text-[#BC7B77] font-thin mb-2">ABOUT OUR COMPANY</div>
            
            <h2 className="text-3xl font-medium mb-4">
              EXPERIENCE UNFORGETTABLE<br />MOMENTS
            </h2>
            
            <p className="text-gray-700 mb-8">
              Join us for exciting events that bring people together for meaningful experiences. Whether it&apos;s 
              workshops, social gatherings, or live performances, we ensure every event is memorable.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 rounded-full  flex items-center justify-center">
                <Image src={checkboxicon} alt="Checkbox" width={16} height={16}/>
                </div>
                <p className="font-medium">WEDDING WONDERS: WHIMSICAL GARDENS</p>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 rounded-full  flex items-center justify-center">
                <Image src={checkboxicon} alt="Checkbox" width={16} height={16}/>
                </div>
                <p className="font-medium">ENGAGING WORKSHOPS & SEMINARS</p>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 rounded-full flex items-center justify-center">
                  <Image src={checkboxicon} alt="Checkbox" width={16} height={16}/>
                </div>
                <p className="font-medium">LIVE PERFORMANCES & FESTIVALS</p>
              </div>
            </div>
            
            <Link href="/gallary">
            <button className="mt-8 bg-[#FE904B] hover:bg-[#e87f3d] text-white px-6 font-urbanist  py-3 rounded-lg transition-colors">
              Read More
            </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Experience
