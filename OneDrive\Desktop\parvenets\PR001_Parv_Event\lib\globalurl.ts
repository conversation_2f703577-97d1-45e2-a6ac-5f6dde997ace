// Global URL configuration
export const GLOBAL_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:8005/api',
  TIMEOUT: 10000, // 10 seconds
} as const;

// API endpoints configuration
export const API_ENDPOINTS = {
  // Add your API endpoints here
  // Example:
  // AUTH: '/auth',
  // USERS: '/users',
  // EVENTS: '/events',
} as const;

// Helper function to build full API URL
export const buildApiUrl = (endpoint: string): string => {
  // Remove leading slash if present to avoid double slashes
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${GLOBAL_CONFIG.BASE_URL}/${cleanEndpoint}`;
};

// Helper function to build full URL (same as buildApiUrl for consistency)
export const buildUrl = (endpoint: string): string => {
  // Remove leading slash if present to avoid double slashes
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${GLOBAL_CONFIG.BASE_URL}/${cleanEndpoint}`;
};
