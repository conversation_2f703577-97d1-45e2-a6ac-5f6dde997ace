// Global URL configuration
export const GLOBAL_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
  TIMEOUT: 10000, // 10 seconds
} as const;

// API endpoints configuration
export const API_ENDPOINTS = {
  // Add your API endpoints here
  // Example:
  // AUTH: '/auth',
  // USERS: '/users',
  // EVENTS: '/events',
} as const;

// Simple URL builder - exactly like Postman: {{base_url}}/api/services
export const buildUrl = (endpoint: string): string => {
  const baseUrl = GLOBAL_CONFIG.BASE_URL;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}/api${cleanEndpoint}`;
};

// Same function for consistency
export const buildApiUrl = buildUrl;
