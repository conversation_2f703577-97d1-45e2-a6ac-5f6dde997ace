"use client";
import React from 'react';
import Image from 'next/image';
import abouthall from "@/public/image/assests/moments/abouthall.png";
import topleftflower from "@/public/image/assests/aboutmiddleflower.png";

const InquerySection = () => {
  return (
    <div className="relative">
      {/* Background Image with Overlay */}
      <div className="relative w-full h-[400px] sm:h-[450px] md:h-[500px]">
        <Image 
          src={abouthall} 
          alt="Event Hall" 
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        
        {/* Text Overlay */}
        <div className="absolute inset-0 flex flex-col justify-center items-start px-8 sm:px-16 md:px-24">
          <p className="text-white text-sm uppercase tracking-wider mb-2">WEDDING HALL</p>
          <h2 className="text-white text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-medium max-w-xl leading-tight">
            ERADICATING WEEDS A OF ELEVATING
          </h2>
          <button className="mt-4 sm:mt-6 px-4 sm:px-6 py-2 border border-white text-white hover:bg-white hover:text-black transition-all duration-300">
            LEARN MORE
          </button>
        </div>
      </div>
      
      {/* Form Section - Positioned to overlap with the next section */}
      <div className="max-w-7xl mx-auto px-4 relative" style={{ marginBottom: "-100px" }}>
        <div className="bg-white shadow-lg rounded-lg p-6 sm:p-8 mt-[-80px] sm:mt-[-100px] md:mt-[-250px] w-full md:max-w-md mx-auto md:ml-auto md:mr-0 z-20 relative">
          <div className="text-center mb-4 sm:mb-6">
            <p className="text-gray-500 text-xs sm:text-sm">LET'S MEET</p>
            <h3 className="text-[#FE904B] text-xl sm:text-2xl font-medium">MAKE AN INQUIRY</h3>
          </div>
          
          <form className="space-y-3 sm:space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <input 
                  type="text" 
                  placeholder="Name" 
                  className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B]"
                />
              </div>
              <div>
                <input 
                  type="email" 
                  placeholder="Email" 
                  className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B]"
                />
              </div>
            </div>
            
            <div>
              <select className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B] appearance-none bg-white">
                <option value="" disabled selected>Select Country</option>
                <option value="India">India</option>
                <option value="United States">United States</option>
                <option value="United Kingdom">United Kingdom</option>
                <option value="Canada">Canada</option>
                <option value="Australia">Australia</option>
                <option value="Singapore">Singapore</option>
                <option value="UAE">UAE</option>
                <option value="Other">Other</option>
              </select>
            </div>
            
            <div>
              <input 
                type="tel" 
                placeholder="Phone" 
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B]"
              />
            </div>
            
            <div>
              <select className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B] appearance-none bg-white">
                <option value="" disabled selected>Select Package</option>
                <option value="Full-Service Planning">Full-Service Planning</option>
                <option value="Partial Planning">Partial Planning</option>
                <option value="Day-of Coordination">Day-of Coordination</option>
                <option value="Destination Wedding">Destination Wedding</option>
                <option value="Custom Package">Custom Package</option>
              </select>
            </div>

            
            <div>
              <textarea 
                placeholder="Description" 
                rows={3}
                className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B]"
              ></textarea>
            </div>
            
            <button 
              type="submit" 
              className="w-full py-2 sm:py-3 bg-[#FE904B] text-white rounded hover:bg-[#e87f3d] transition-colors duration-300"
            >
              SEND REQUEST
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default InquerySection
