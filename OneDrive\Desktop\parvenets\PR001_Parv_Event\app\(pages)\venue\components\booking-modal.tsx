"use client";
import React, { useState, useEffect } from "react";
import { VenueData } from "../venualldummydata/venue_dummydata";
import { VenueBookingFormData } from "@/lib/api/venue/venueApi";
import { X } from "lucide-react";
import { toast } from "react-toastify";

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  venue: VenueData;
  onSubmit: (formData: VenueBookingFormData) => Promise<void>;
}

const BookingModal: React.FC<BookingModalProps> = ({ isOpen, onClose, venue, onSubmit }) => {
  const [formData, setFormData] = useState<VenueBookingFormData>({
    name: "",
    email: "",
    phone: "",
    venueId: venue._id,
    eventDate: "",
    eventType: "",
    guestCount: venue.capacity || 50,
    message: ""
  });

  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      venueId: venue._id,
      guestCount: venue.capacity || 50,
    }));
  }, [venue._id, venue.capacity]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle number inputs
    const processedValue = name === 'guestCount' ? parseInt(value) || 0 : value;

    setFormData((prev) => ({
      ...prev,
      [name]: processedValue,
    }));

    if (validationErrors[name]) {
      setValidationErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      errors.name = "Full name is required";
    } else if (formData.name.length > 100) {
      errors.name = "Full name must be less than 100 characters";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    if (!formData.phone.trim()) {
      errors.phone = "Phone number is required";
    } else if (!/^[6-9]\d{9}$/.test(formData.phone.replace(/\s+/g, ""))) {
      errors.phone = "Please enter a valid 10-digit Indian mobile number";
    }

    if (!formData.eventDate) {
      errors.eventDate = "Event date is required";
    } else {
      const eventDate = new Date(formData.eventDate);
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      if (eventDate < tomorrow) {
        errors.eventDate = "Event date must be at least tomorrow";
      }
    }

    if (!formData.eventType.trim()) {
      errors.eventType = "Event type is required";
    }

    if (formData.guestCount <= 0) {
      errors.guestCount = "Guest count must be greater than 0";
    } else if (formData.guestCount > venue.capacity) {
      errors.guestCount = `Guest count cannot exceed venue capacity of ${venue.capacity}`;
    }

    if (formData.message && formData.message.length > 1000) {
      errors.message = "Message must be less than 1000 characters";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      phone: "",
      venueId: venue._id,
      eventDate: "",
      eventType: "",
      guestCount: venue.capacity || 50,
      message: ""
    });
    setValidationErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!validateForm()) {
      setIsSubmitting(false);
      toast.error("Please fix the form errors before submitting.");
      return;
    }

    const submitData: VenueBookingFormData = {
      ...formData,
      venueId: venue._id,
    };

    console.log("📋 Final booking data to submit:", submitData);

    try {
      await onSubmit(submitData);
    } catch (error) {
      console.error("Booking submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  const getMinDate = (): string => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split("T")[0];
  };

  const minDate = getMinDate();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 p-4 overflow-y-auto">
      <div className="relative mx-auto my-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-lg bg-white shadow-xl">
        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute right-4 top-4 z-10 text-white hover:text-gray-200"
          aria-label="Close modal"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="border-b bg-gradient-to-r from-[#FE904B] to-[#e87f3a] p-6 text-white">
          <h3 className="mb-2 text-2xl font-bold">Book {venue.name}</h3>
          <div className="flex items-center text-sm opacity-90">
            <span className="mr-4">📍 {venue.location}</span>
            <span>👥 Capacity: {venue.capacity} guests</span>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6 rounded-lg bg-gray-50 p-4">
            <h4 className="mb-2 font-semibold text-gray-900">Selected Venue</h4>
            <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
              <div>
                <span className="font-medium text-gray-700">Venue Name:</span>
                <p className="text-gray-900">{venue.name}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Location:</span>
                <p className="text-gray-900">{venue.location}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Capacity:</span>
                <p className="text-gray-900">{venue.capacity} guests</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Seats:</span>
                <p className="text-gray-900">{venue.seats} seats</p>
              </div>
            </div>
            <input type="hidden" name="venueId" value={formData.venueId} />
          </div>

          <div className="space-y-4">
            {/* Full Name */}
            <div>
              <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                maxLength={100}
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.name ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter your full name"
              />
              {validationErrors.name && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.name}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="mb-1 block text-sm font-medium text-gray-700">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.email ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="<EMAIL>"
              />
              {validationErrors.email && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.email}</p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label htmlFor="phone" className="mb-1 block text-sm font-medium text-gray-700">
                Phone Number *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                maxLength={10}
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.phone ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="10-digit mobile number"
              />
              {validationErrors.phone && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.phone}</p>
              )}
            </div>

            {/* Event Date */}
            <div>
              <label htmlFor="eventDate" className="mb-1 block text-sm font-medium text-gray-700">
                Event Date *
              </label>
              <input
                type="date"
                id="eventDate"
                name="eventDate"
                value={formData.eventDate}
                onChange={handleInputChange}
                required
                min={minDate}
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.eventDate ? "border-red-500" : "border-gray-300"
                }`}
              />
              {validationErrors.eventDate && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.eventDate}</p>
              )}
            </div>

            {/* Event Type */}
            <div>
              <label htmlFor="eventType" className="mb-1 block text-sm font-medium text-gray-700">
                Event Type *
              </label>
              <select
                id="eventType"
                name="eventType"
                value={formData.eventType}
                onChange={handleInputChange}
                required
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.eventType ? "border-red-500" : "border-gray-300"
                }`}
              >
                <option value="">Select event type</option>
                <option value="wedding">Wedding</option>
                <option value="birthday">Birthday Party</option>
                <option value="corporate">Corporate Event</option>
                <option value="conference">Conference</option>
                <option value="reception">Reception</option>
                <option value="anniversary">Anniversary</option>
                <option value="other">Other</option>
              </select>
              {validationErrors.eventType && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.eventType}</p>
              )}
            </div>

            {/* Guest Count */}
            <div>
              <label htmlFor="guestCount" className="mb-1 block text-sm font-medium text-gray-700">
                Guest Count *
              </label>
              <input
                type="number"
                id="guestCount"
                name="guestCount"
                value={formData.guestCount}
                onChange={handleInputChange}
                required
                min={1}
                max={venue.capacity}
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.guestCount ? "border-red-500" : "border-gray-300"
                }`}
                placeholder={`Max ${venue.capacity} guests`}
              />
              {validationErrors.guestCount && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.guestCount}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">Maximum capacity: {venue.capacity} guests</p>
            </div>

            {/* Message */}
            <div>
              <label htmlFor="message" className="mb-1 block text-sm font-medium text-gray-700">
                Message (Optional)
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message || ""}
                onChange={handleInputChange}
                rows={4}
                maxLength={1000}
                className={`w-full rounded-md border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                  validationErrors.message ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Please describe your event requirements, budget, and any special requests..."
              />
              {validationErrors.message && (
                <p className="mt-1 text-xs text-red-500">{validationErrors.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">{formData.message?.length || 0}/1000 characters</p>
            </div>
          </div>

          <div className="mt-6 text-center">
            <button
              type="submit"
              disabled={isSubmitting}
              className="rounded-md bg-[#FE904B] px-8 py-3 font-medium text-white transition-colors duration-300 hover:bg-[#e87f3a] disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isSubmitting ? "Submitting..." : "SUBMIT BOOKING REQUEST"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BookingModal;
