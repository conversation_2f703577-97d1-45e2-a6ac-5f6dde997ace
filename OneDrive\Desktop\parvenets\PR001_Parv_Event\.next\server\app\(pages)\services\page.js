(()=>{var e={};e.id=719,e.ids=[719],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10013:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s={src:"/_next/static/media/pinktree.b502aac7.png",height:195,width:256,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAKlBMVEVMaXH/q9bdorPZoK7YoK/RlafMnaeyg43Ll6LCjprTnavgq7jWobB/XV3u3GU9AAAADnRSTlMAAjtgjSgXUCVqL0x4Hph5UpkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAQAwDMMwp3+PP91JgCQsgBqOJ4VviwKb2ysRHuvtFLS3CfgR1AC3VmqfqgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13858:(e,t,r)=>{"use strict";r.d(t,{_Z:()=>n,dU:()=>l,jE:()=>o,s7:()=>i});var s=r(34115),a=r(684);let i={getServices:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())}),(await s.u.get((0,a.c$)(`/services?${t.toString()}`))).data},getServiceById:async e=>(await s.u.get((0,a.c$)(`/services/${e}`))).data,getServiceBySlug:async e=>(await s.u.get((0,a.c$)(`/services/slug/${e}`))).data,getServiceCategories:async()=>(await s.u.get((0,a.c$)("/services/categories"))).data.categories},o={getGallery:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&(Array.isArray(r)?r.forEach(r=>t.append(e,r.toString())):t.append(e,r.toString()))}),(await s.u.get((0,a.c$)(`/gallery?${t.toString()}`))).data},getGalleryItems:async(e={})=>o.getGallery(e),getGalleryCategories:async()=>(await s.u.get((0,a.c$)("/gallery/categories"))).data.categories,getGalleryByCategory:async(e,t={})=>{let r=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,t.toString())}),(await s.u.get((0,a.c$)(`/gallery/category/${e}?${r.toString()}`))).data}},n={getReviews:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())}),(await s.u.get((0,a.c$)(`/reviews?${t.toString()}`))).data},getServiceReviews:async(e,t={})=>{let r=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,t.toString())}),(await s.u.get((0,a.c$)(`/reviews/service/${e}?${r.toString()}`))).data}},l={formatPrice:e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(e),createSlug:e=>e.toLowerCase().replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),truncateText:(e,t)=>e.length<=t?e:e.substring(0,t).trim()+"...",formatRating:e=>e.toFixed(1),getStarRating:e=>{let t=Math.floor(e),r=e%1>=.5;return{filled:t,half:r,empty:5-t-!!r}},formatRelationship:e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),formatCardTitle:e=>e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "),formatCardDescription:(e,t=150)=>e?e.length<=t?e:e.substring(0,t).trim()+"...":"",formatGalleryCategory:e=>e?e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):"All",formatGalleryTitle:e=>e?e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):""};i.getServices,i.getServiceById,i.getServiceBySlug,i.getServiceCategories,o.getGallery,o.getGalleryCategories,o.getGalleryByCategory,n.getReviews,n.getServiceReviews},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38700:(e,t,r)=>{Promise.resolve().then(r.bind(r,57648))},44780:(e,t,r)=>{Promise.resolve().then(r.bind(r,74678))},54420:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["(pages)",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57648)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,48754)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\services\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(pages)/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56947:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(60687),a=r(43210),i=r.n(a),o=r(30474),n=r(85814),l=r.n(n),c=r(10013);let d=({title:e,breadcrumbs:t})=>(0,s.jsx)("div",{className:"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0",children:e}),(0,s.jsx)("div",{className:"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10",children:(0,s.jsx)(o.default,{src:c.default,alt:"Cherry Blossom",width:130,height:100,style:{width:"auto",height:"auto"},className:"object-contain"})}),(0,s.jsx)("div",{className:"flex items-center space-x-2 text-sm z-20",children:t.map((e,r)=>(0,s.jsxs)(i().Fragment,{children:[(0,s.jsx)(l(),{href:e.href,className:"hover:text-[#FE904B] transition-colors",children:e.label}),r<t.length-1&&(0,s.jsx)("span",{className:"text-gray-400",children:"›"})]},r))})]})})},57648:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\(pages)\\\\services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\services\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74678:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),a=r(43210),i=r(56947),o=r(30474),n=r(85814),l=r.n(n),c=r(13858),d=r(93853);let m={src:"/_next/static/media/servicerighttopflower.ab643a48.png",height:203,width:426,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAABlBMVEUAAABMaXFP2lwvAAAAAnRSTlMCAESlr7oAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAeSURBVHicY2BkAAFGRgYQgxGMGBgZGcHCIJqRkQEAARQAESJ8Vm0AAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},p=()=>{let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!0),[p,g]=(0,a.useState)(null),u=async()=>{try{n(!0),g(null);let e=await c.s7.getServices({page:1,limit:20,sortBy:"sortOrder",sortOrder:"asc"});if(e.success&&e.data.services)t(e.data.services),console.log("✅ Services loaded:",e.data.services.length,"services");else throw Error("Failed to fetch services")}catch(e){console.error("Error fetching services:",e),g(e.message||"Failed to load services"),d.oR.error("Failed to load services. Please try again later.")}finally{n(!1)}};return(0,a.useEffect)(()=>{u()},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{title:"SERVICES",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICES",href:"/services"}]}),(0,s.jsxs)("div",{className:"relative py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"absolute top-10 right-0 -z-10 hidden lg:block opacity-30",children:(0,s.jsx)(o.default,{src:m,alt:"Decorative flower",width:300,height:250,className:"object-contain"})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12 sm:mb-16 lg:mb-20",children:[(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 font-urbanist uppercase text-gray-900 leading-tight",children:"Our Professional Services"}),(0,s.jsx)("div",{className:"w-20 sm:w-24 h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 mx-auto rounded-full mb-4 sm:mb-6"})]}),r&&(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] p-4 sm:p-6 animate-pulse",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4 sm:mb-6",children:(0,s.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-gray-300 rounded-full"})}),(0,s.jsx)("div",{className:"h-4 sm:h-5 bg-gray-300 rounded mb-3"}),(0,s.jsx)("div",{className:"h-3 sm:h-4 bg-gray-300 rounded mb-2"}),(0,s.jsx)("div",{className:"h-3 sm:h-4 bg-gray-300 rounded mb-4 sm:mb-6"}),(0,s.jsx)("div",{className:"h-8 sm:h-10 bg-gray-300 rounded-lg"})]},t))}),p&&!r&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsxs)("div",{className:"text-red-500 mb-4",children:[(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Failed to load services"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:p})]}),(0,s.jsx)("button",{onClick:u,className:"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors",children:"Try Again"})]}),!r&&!p&&(0,s.jsx)(s.Fragment,{children:0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"text-gray-500",children:[(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"No services available"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Please check back later"})]})}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:e.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] hover:shadow-lg transition-all duration-300 text-center p-4 sm:p-6 group hover:transform hover:-translate-y-1",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4 sm:mb-6",children:(0,s.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center bg-orange-50 rounded-full group-hover:bg-orange-100 transition-colors duration-300 shadow-sm overflow-hidden",children:(0,s.jsx)("img",{src:e.icons,alt:e.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 rounded-full",onLoad:()=>{console.log("✅ Backend service icon loaded:",e.title)},onError:()=>{console.error("❌ Backend service icon failed:",e.title,e.icons)}})})}),(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold uppercase mb-2 sm:mb-3 text-gray-800 group-hover:text-[#FE904D] transition-colors duration-300 leading-tight",children:c.dU.formatCardTitle(e.title)}),(0,s.jsx)("p",{className:"text-gray-600 text-xs sm:text-sm mb-4 sm:mb-6 line-clamp-3 leading-relaxed px-2",children:c.dU.formatCardDescription(e.description)}),(0,s.jsx)(l(),{href:`/services/${e._id}`,children:(0,s.jsx)("div",{className:"inline-block border-2 border-[#FE904D] hover:bg-[#FE904D] hover:text-white text-[#FE904D] text-xs sm:text-sm font-medium uppercase tracking-wider py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-all duration-300 cursor-pointer group-hover:bg-[#FE904D] group-hover:text-white group-hover:shadow-md",children:"See More"})})]},e._id))})})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,814,950,443],()=>r(54420));module.exports=s})();