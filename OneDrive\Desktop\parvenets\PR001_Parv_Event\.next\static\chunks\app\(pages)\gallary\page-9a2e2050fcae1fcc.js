(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[899],{1168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a={src:"/_next/static/media/pinktree.b502aac7.png",height:195,width:256,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAKlBMVEVMaXH/q9bdorPZoK7YoK/RlafMnaeyg43Ll6LCjprTnavgq7jWobB/XV3u3GU9AAAADnRSTlMAAjtgjSgXUCVqL0x4Hph5UpkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAQAwDMMwp3+PP91JgCQsgBqOJ4VviwKb2ysRHuvtFLS3CfgR1AC3VmqfqgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6}},1506:(e,t,r)=>{"use strict";r.d(t,{_Z:()=>l,dU:()=>n,jE:()=>i,s7:()=>o});var a=r(7693),s=r(1966);let o={getServices:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&t.append(r,a.toString())}),(await a.u.get((0,s.c$)("/services?".concat(t.toString())))).data},getServiceById:async e=>(await a.u.get((0,s.c$)("/services/".concat(e)))).data,getServiceBySlug:async e=>(await a.u.get((0,s.c$)("/services/slug/".concat(e)))).data,getServiceCategories:async()=>(await a.u.get((0,s.c$)("/services/categories"))).data.categories},i={getGallery:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&(Array.isArray(a)?a.forEach(e=>t.append(r,e.toString())):t.append(r,a.toString()))}),(await a.u.get((0,s.c$)("/gallery?".concat(t.toString())))).data},getGalleryItems:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return i.getGallery(e)},getGalleryCategories:async()=>(await a.u.get((0,s.c$)("/gallery/categories"))).data.categories,getGalleryByCategory:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;null!=a&&r.append(t,a.toString())}),(await a.u.get((0,s.c$)("/gallery/category/".concat(e,"?").concat(r.toString())))).data}},l={getReviews:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&t.append(r,a.toString())}),(await a.u.get((0,s.c$)("/reviews?".concat(t.toString())))).data},getServiceReviews:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,a]=e;null!=a&&r.append(t,a.toString())}),(await a.u.get((0,s.c$)("/reviews/service/".concat(e,"?").concat(r.toString())))).data}},n={formatPrice:e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(e),createSlug:e=>e.toLowerCase().replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),truncateText:(e,t)=>e.length<=t?e:e.substring(0,t).trim()+"...",formatRating:e=>e.toFixed(1),getStarRating:e=>{let t=Math.floor(e),r=e%1>=.5;return{filled:t,half:r,empty:5-t-!!r}},formatRelationship:e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),formatCardTitle:e=>e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "),formatCardDescription:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150;return e?e.length<=t?e:e.substring(0,t).trim()+"...":""},formatGalleryCategory:e=>e?e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):"All",formatGalleryTitle:e=>e?e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):""};o.getServices,o.getServiceById,o.getServiceBySlug,o.getServiceCategories,i.getGallery,i.getGalleryCategories,i.getGalleryByCategory,l.getReviews,l.getServiceReviews},1966:(e,t,r)=>{"use strict";r.d(t,{JW:()=>a,KB:()=>o,c$:()=>s});let a={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},s=e=>{let t=a.BASE_URL,r=e.startsWith("/")?e:"/".concat(e);return"".concat(t,"/api").concat(r)},o=s},2788:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(5155),s=r(2115),o=r(7771),i=r(6766),l=r(1506),n=r(8543),c=r(2355),d=r(3052),m=r(6209),g=r(4178),u=r(4216);r(8561),r(4901);let h=()=>{let[e,t]=(0,s.useState)(1),[r,h]=(0,s.useState)(!1),[p,x]=(0,s.useState)(0),[v,y]=(0,s.useState)([]),[j,f]=(0,s.useState)([]),[b,w]=(0,s.useState)(""),[N,A]=(0,s.useState)([]),[k,C]=(0,s.useState)(!0),[S,L]=(0,s.useState)(null),E=Math.ceil(j.length/6),B=6*e,R=B-6,M=j.slice(R,B),U=j.map(e=>({src:e.image,alt:e.title})),G=async e=>{try{C(!0),L(null);let t=await l.jE.getGalleryItems({page:1,limit:100,category:e,sortBy:"sortOrder",sortOrder:"asc"});if(t.success&&t.data.galleries){y(t.data.galleries),f(t.data.galleries);let e=[...new Set(t.data.galleries.map(e=>e.category))];A(e)}else throw Error("Failed to fetch gallery items")}catch(e){console.error("Error fetching gallery:",e),L(e.message||"Failed to load gallery"),n.oR.error("Failed to load gallery. Please try again later.")}finally{C(!1)}},F=e=>{w(e),t(1),""===e?f(v):f(v.filter(t=>t.category===e))};(0,s.useEffect)(()=>{G()},[]);let P=e=>{t(e),window.scrollTo({top:0,behavior:"smooth"})},z=e=>{x(R+e),h(!0)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{title:"GALLARY",breadcrumbs:[{label:"HOME",href:"/"},{label:"GALLARY",href:"/gallary"}]}),(0,a.jsx)("div",{className:"py-20 px-4 sm:px-20 lg:px-10",children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("p",{className:"text-[12px] text-[#BC7B77] uppercase tracking-wider mb-2",children:"PHOTO GALLERY"}),(0,a.jsx)("h2",{className:"text-[32px] sm:text-4xl font-medium mb-4 uppercase",children:"Our Professional Gallery"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto mb-8",children:"Explore our collection of beautiful events and celebrations we've created for our clients."})]}),!k&&N.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-2 mb-8",children:[(0,a.jsx)("button",{onClick:()=>F(""),className:"px-4 py-2 rounded-full text-sm font-medium transition-colors ".concat(""===b?"bg-[#FE904B] text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"All"}),N.map(e=>(0,a.jsx)("button",{onClick:()=>F(e),className:"px-4 py-2 rounded-full text-sm font-medium transition-colors ".concat(b===e?"bg-[#FE904B] text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:l.dU.formatGalleryCategory(e)},e))]}),k&&(0,a.jsx)("div",{className:"grid grid-cols-12 gap-4",children:[...Array(6)].map((e,t)=>(0,a.jsx)("div",{className:"".concat(t<3?"col-span-12 sm:col-span-4":3===t?"col-span-12 sm:col-span-8":4===t?"col-span-12 sm:col-span-4":"col-span-12"),children:(0,a.jsx)("div",{className:"bg-gray-300 animate-pulse rounded-md ".concat(t<3||4===t?"aspect-square":3===t?"aspect-[16/9]":"aspect-[21/9]")})},t))}),S&&!k&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsxs)("div",{className:"text-red-500 mb-4",children:[(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Failed to load gallery"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:S})]}),(0,a.jsx)("button",{onClick:()=>G(),className:"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors",children:"Try Again"})]}),!k&&!S&&0===j.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"text-gray-500",children:[(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"No images found"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:b?"No images in ".concat(l.dU.formatGalleryCategory(b)," category"):"No gallery images available"})]})}),!k&&!S&&M.length>0&&(0,a.jsxs)("div",{className:"grid grid-cols-12 gap-4",children:[M.length>0&&(0,a.jsx)("div",{className:"col-span-12 sm:col-span-4",children:(0,a.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-md cursor-pointer group",onClick:()=>z(0),children:[(0,a.jsx)(i.default,{src:M[0].image,alt:M[0].title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})})})})]})}),M.length>1&&(0,a.jsx)("div",{className:"col-span-12 sm:col-span-4",children:(0,a.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-md cursor-pointer group",onClick:()=>z(1),children:[(0,a.jsx)(i.default,{src:M[1].image,alt:M[1].title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})})})})]})}),M.length>2&&(0,a.jsx)("div",{className:"col-span-12 sm:col-span-4",children:(0,a.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-md cursor-pointer group",onClick:()=>z(2),children:[(0,a.jsx)(i.default,{src:M[2].image,alt:M[2].title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})})})})]})}),M.length>3&&(0,a.jsx)("div",{className:"col-span-12 sm:col-span-8",children:(0,a.jsxs)("div",{className:"relative aspect-[16/9] overflow-hidden rounded-md cursor-pointer group",onClick:()=>z(3),children:[(0,a.jsx)(i.default,{src:M[3].image,alt:M[3].title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})})})})]})}),M.length>4&&(0,a.jsx)("div",{className:"col-span-12 sm:col-span-4",children:(0,a.jsxs)("div",{className:"relative aspect-square overflow-hidden rounded-md cursor-pointer group",onClick:()=>z(4),children:[(0,a.jsx)(i.default,{src:M[4].image,alt:M[4].title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})})})})]})}),M.length>5&&(0,a.jsx)("div",{className:"col-span-12",children:(0,a.jsxs)("div",{className:"relative aspect-[21/9] overflow-hidden rounded-md cursor-pointer group",onClick:()=>z(5),children:[(0,a.jsx)(i.default,{src:M[5].image,alt:M[5].title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})})})})]})})]}),!k&&!S&&j.length>6&&(0,a.jsxs)("div",{className:"flex justify-center items-center mt-12 space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{e>1&&P(e-1)},disabled:1===e,className:"w-8 h-8 flex items-center justify-center rounded-md border ".concat(1===e?"border-gray-200 text-gray-400 cursor-not-allowed":"border-gray-300 hover:bg-gray-100 cursor-pointer"),children:(0,a.jsx)(c.A,{size:16})}),Array.from({length:E},(e,t)=>t+1).map(t=>(0,a.jsx)("button",{onClick:()=>P(t),className:"w-8 h-8 flex items-center justify-center rounded-md ".concat(e===t?"bg-[#FE904B] text-white":"border border-gray-300 hover:bg-gray-100"),children:t},t)),(0,a.jsx)("button",{onClick:()=>{e<E&&P(e+1)},disabled:e===E,className:"w-8 h-8 flex items-center justify-center rounded-md border ".concat(e===E?"border-gray-200 text-gray-400 cursor-not-allowed":"border-gray-300 hover:bg-gray-100 cursor-pointer"),children:(0,a.jsx)(d.A,{size:16})})]})]})}),(0,a.jsx)(m.Ay,{open:r,close:()=>h(!1),slides:U,index:p,plugins:[g.A,u.A],zoom:{maxZoomPixelRatio:3,zoomInMultiplier:1.5,doubleTapDelay:300},thumbnails:{position:"bottom",width:120,height:80,gap:16}})]})}},3112:(e,t,r)=>{Promise.resolve().then(r.bind(r,2788))},7693:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var a=r(3464),s=r(1966);let o=a.A.create({timeout:s.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});o.interceptors.request.use(e=>{{let t=localStorage.getItem("authToken");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>(console.error("❌ Request Error:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(e.response){let{status:t,data:r}=e.response;switch(t){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",r)}}else if(e.request){var t,r,a;console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:null==(t=e.config)?void 0:t.url,method:null==(r=e.config)?void 0:r.method,baseURL:null==(a=e.config)?void 0:a.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."}else console.error("❌ Error:",e.message);return Promise.reject(e)});let i={get:(e,t)=>o.get(e,t),post:(e,t,r)=>o.post(e,t,r),put:(e,t,r)=>o.put(e,t,r),patch:(e,t,r)=>o.patch(e,t,r),delete:(e,t)=>o.delete(e,t)}},7771:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(5155),s=r(2115),o=r(6766),i=r(6874),l=r.n(i),n=r(1168);let c=e=>{let{title:t,breadcrumbs:r}=e;return(0,a.jsx)("div",{className:"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0",children:t}),(0,a.jsx)("div",{className:"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10",children:(0,a.jsx)(o.default,{src:n.default,alt:"Cherry Blossom",width:130,height:100,style:{width:"auto",height:"auto"},className:"object-contain"})}),(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm z-20",children:r.map((e,t)=>(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)(l(),{href:e.href,className:"hover:text-[#FE904B] transition-colors",children:e.label}),t<r.length-1&&(0,a.jsx)("span",{className:"text-gray-400",children:"›"})]},t))})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[773,83,854,543,344,441,684,358],()=>t(3112)),_N_E=e.O()}]);