{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/pinktree.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 256, height: 195, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAAtklEQVR42k2LXQ+BYACFX4WXO8zHbEyrVqhRSVspq1xUbBUX0QXNpZkb83Fj/HUVNs92trOd5wDwJRODxEk6zOXyAkkLr/3h8dtBvVSukq02nozuRJ/bkrKQuqwIaqVypZCHcKWZS5Hq8eqAly+b6N5pNDvp01cNh8Fw+hxsr/pw5LjKNGQxXEAQBE2FoxecbsHuGZpW1K43CJHucwxGUFkU/QgzTgzXph36muFZY9koQlgEf7wB2pkb9Zp1xhgAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0V,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/page_hero/page_hero.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport cherryBlossom from '@/public/image/assests/pinktree.png'; // Make sure this image exists\r\n\r\ninterface PageHeroProps {\r\n  title: string;\r\n  breadcrumbs: {\r\n    label: string;\r\n    href: string;\r\n  }[];\r\n}\r\n\r\nconst PageHero = ({ title, breadcrumbs }: PageHeroProps) => {\r\n  return (\r\n    <div className=\"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4\">\r\n      <div className=\"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center\">\r\n        {/* Left side - Page Title */}\r\n        <h1 className=\"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0\">\r\n          {title}\r\n        </h1>\r\n        \r\n        {/* Center - Cherry Blossom Image */}\r\n        <div className=\"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10\">\r\n          <Image \r\n            src={cherryBlossom} \r\n            alt=\"Cherry Blossom\" \r\n            width={130} \r\n            height={100} \r\n            style={{ width: 'auto', height: 'auto' }}\r\n            className=\"object-contain\"\r\n          />\r\n        </div>\r\n        \r\n        {/* Right side - Breadcrumbs */}\r\n        <div className=\"flex items-center space-x-2 text-sm z-20\">\r\n          {breadcrumbs.map((crumb, index) => (\r\n            <React.Fragment key={index}>\r\n              <Link \r\n                href={crumb.href} \r\n                className=\"hover:text-[#FE904B] transition-colors\"\r\n              >\r\n                {crumb.label}\r\n              </Link>\r\n              \r\n              {index < breadcrumbs.length - 1 && (\r\n                <span className=\"text-gray-400\">›</span>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageHero;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA,igBAAiE,8BAA8B;;;;;;AAU/F,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,WAAW,EAAiB;IACrD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;8BACX;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,4SAAA,CAAA,UAAa;wBAClB,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,OAAO;4BAAE,OAAO;4BAAQ,QAAQ;wBAAO;wBACvC,WAAU;;;;;;;;;;;8BAKd,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;;gCAGb,QAAQ,YAAY,MAAM,GAAG,mBAC5B,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;2BATf;;;;;;;;;;;;;;;;;;;;;AAiBjC;uCAEe", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/api/service/serviceApi.ts"], "sourcesContent": ["// Service API module\nimport { apiClient } from '../../customaxios';\nimport { buildUrl } from '../../globalurl';\n\n// Types for service API\nexport interface Service {\n  _id: string;\n  id: string;\n  title: string;\n  description: string;\n  description2?: string;\n  shortDescription: string;\n  slug: string;\n  category: string;\n  price: number;\n  duration: string;\n  features: string[];\n  images: string[];\n  image: string; // Primary image URL\n  howWeDoIt?: Array<{\n    title: string;\n    description: string;\n    icon?: string;\n  }>;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Gallery {\n  id: string;\n  title: string;\n  description: string;\n  imageUrl: string;\n  category: string;\n  tags: string[];\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface GalleryCategory {\n  id: string;\n  name: string;\n  slug: string;\n  description: string;\n  imageCount: number;\n}\n\nexport interface Review {\n  _id: string;\n  id: string;\n  name: string;\n  email: string;\n  rating: number;\n  comment: string;\n  review: string; // Alias for comment\n  relationship: string;\n  serviceId?: string;\n  isApproved: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ServiceQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  category?: string;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface GalleryQueryParams {\n  page?: number;\n  limit?: number;\n  category?: string;\n  tags?: string[];\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface ServicesResponse {\n  success: boolean;\n  message: string;\n  data: {\n    services: Service[];\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\nexport interface ServiceResponse {\n  success: boolean;\n  message: string;\n  data?: Service;\n}\n\nexport interface GalleryResponse {\n  gallery: Gallery[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface ReviewsResponse {\n  success: boolean;\n  message: string;\n  data: {\n    reviews: Review[];\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\n// Service API functions\nexport const serviceApi = {\n  // Get all services with pagination and filters\n  getServices: async (params: ServiceQueryParams = {}): Promise<ServicesResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ServicesResponse>(\n      buildUrl(`/services?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get single service by ID or slug\n  getServiceById: async (id: string): Promise<ServiceResponse> => {\n    const response = await apiClient.get<ServiceResponse>(\n      buildUrl(`/services/${id}`)\n    );\n    return response.data;\n  },\n\n  // Get service by slug\n  getServiceBySlug: async (slug: string): Promise<ServiceResponse> => {\n    const response = await apiClient.get<ServiceResponse>(\n      buildUrl(`/services/slug/${slug}`)\n    );\n    return response.data;\n  },\n\n  // Get service categories\n  getServiceCategories: async (): Promise<string[]> => {\n    const response = await apiClient.get<{ categories: string[] }>(\n      buildUrl('/services/categories')\n    );\n    return response.data.categories;\n  },\n};\n\n// Gallery API functions\nexport const galleryApi = {\n  // Get all gallery items with pagination and filters\n  getGallery: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryParams = new URLSearchParams();\n\n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        if (Array.isArray(value)) {\n          value.forEach(v => queryParams.append(key, v.toString()));\n        } else {\n          queryParams.append(key, value.toString());\n        }\n      }\n    });\n\n    const response = await apiClient.get<GalleryResponse>(\n      buildUrl(`/gallery?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Alias for getGallery - for compatibility\n  getGalleryItems: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    return galleryApi.getGallery(params);\n  },\n\n  // Get gallery categories\n  getGalleryCategories: async (): Promise<GalleryCategory[]> => {\n    const response = await apiClient.get<{ categories: GalleryCategory[] }>(\n      buildUrl('/gallery/categories')\n    );\n    return response.data.categories;\n  },\n\n  // Get gallery by category\n  getGalleryByCategory: async (category: string, params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<GalleryResponse>(\n      buildUrl(`/gallery/category/${category}?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n};\n\n// Reviews API functions\nexport const reviewsApi = {\n  // Get all reviews with pagination\n  getReviews: async (params: { page?: number; limit?: number; serviceId?: string; sortBy?: string; sortOrder?: 'asc' | 'desc' } = {}): Promise<ReviewsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ReviewsResponse>(\n      buildUrl(`/reviews?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get reviews for a specific service\n  getServiceReviews: async (serviceId: string, params: { page?: number; limit?: number } = {}): Promise<ReviewsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ReviewsResponse>(\n      buildUrl(`/reviews/service/${serviceId}?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n};\n\n// Helper functions\nexport const serviceHelpers = {\n  // Format price for display\n  formatPrice: (price: number): string => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0,\n    }).format(price);\n  },\n\n  // Create slug from title\n  createSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9 -]/g, '')\n      .replace(/\\s+/g, '-')\n      .replace(/-+/g, '-')\n      .trim();\n  },\n\n  // Truncate text\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + '...';\n  },\n\n  // Format rating for display\n  formatRating: (rating: number): string => {\n    return rating.toFixed(1);\n  },\n\n  // Get star rating array for display\n  getStarRating: (rating: number): { filled: number; half: boolean; empty: number } => {\n    const filled = Math.floor(rating);\n    const half = rating % 1 >= 0.5;\n    const empty = 5 - filled - (half ? 1 : 0);\n\n    return { filled, half, empty };\n  },\n\n  // Format relationship for display\n  formatRelationship: (relationship: string): string => {\n    return relationship\n      .split('-')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n  },\n\n  // Format card title for display\n  formatCardTitle: (title: string): string => {\n    return title\n      .split(' ')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n      .join(' ');\n  },\n\n  // Format card description for display\n  formatCardDescription: (description: string, maxLength: number = 150): string => {\n    if (!description) return '';\n    if (description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  },\n};\n\n// Export individual functions for easier imports\nexport const getServices = serviceApi.getServices;\nexport const getServiceById = serviceApi.getServiceById;\nexport const getServiceBySlug = serviceApi.getServiceBySlug;\nexport const getServiceCategories = serviceApi.getServiceCategories;\nexport const getGallery = galleryApi.getGallery;\nexport const getGalleryCategories = galleryApi.getGalleryCategories;\nexport const getGalleryByCategory = galleryApi.getGalleryByCategory;\nexport const getReviews = reviewsApi.getReviews;\nexport const getServiceReviews = reviewsApi.getServiceReviews;\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;;;;;;;;;;;;AACrB;AACA;;;AAuHO,MAAM,aAAa;IACxB,+CAA+C;IAC/C,aAAa,OAAO,SAA6B,CAAC,CAAC;QACjD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,UAAU,EAAE,YAAY,QAAQ,IAAI;QAEhD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,UAAU,EAAE,IAAI;QAE5B,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,eAAe,EAAE,MAAM;QAEnC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI,CAAC,UAAU;IACjC;AACF;AAGO,MAAM,aAAa;IACxB,oDAAoD;IACpD,YAAY,OAAO,SAA6B,CAAC,CAAC;QAChD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,YAAY,MAAM,CAAC,KAAK,EAAE,QAAQ;gBACvD,OAAO;oBACL,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACxC;YACF;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,SAAS,EAAE,YAAY,QAAQ,IAAI;QAE/C,OAAO,SAAS,IAAI;IACtB;IAEA,2CAA2C;IAC3C,iBAAiB,OAAO,SAA6B,CAAC,CAAC;QACrD,OAAO,WAAW,UAAU,CAAC;IAC/B;IAEA,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI,CAAC,UAAU;IACjC;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO,UAAkB,SAA6B,CAAC,CAAC;QAC5E,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAE,YAAY,QAAQ,IAAI;QAEpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,kCAAkC;IAClC,YAAY,OAAO,SAA6G,CAAC,CAAC;QAChI,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,SAAS,EAAE,YAAY,QAAQ,IAAI;QAE/C,OAAO,SAAS,IAAI;IACtB;IAEA,qCAAqC;IACrC,mBAAmB,OAAO,WAAmB,SAA4C,CAAC,CAAC;QACzF,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE,YAAY,QAAQ,IAAI;QAEpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,2BAA2B;IAC3B,aAAa,CAAC;QACZ,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,yBAAyB;IACzB,YAAY,CAAC;QACX,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;IACT;IAEA,gBAAgB;IAChB,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;IAC/C;IAEA,4BAA4B;IAC5B,cAAc,CAAC;QACb,OAAO,OAAO,OAAO,CAAC;IACxB;IAEA,oCAAoC;IACpC,eAAe,CAAC;QACd,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,MAAM,OAAO,SAAS,KAAK;QAC3B,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC;QAExC,OAAO;YAAE;YAAQ;YAAM;QAAM;IAC/B;IAEA,kCAAkC;IAClC,oBAAoB,CAAC;QACnB,OAAO,aACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,gCAAgC;IAChC,iBAAiB,CAAC;QAChB,OAAO,MACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACpE,IAAI,CAAC;IACV;IAEA,sCAAsC;IACtC,uBAAuB,CAAC,aAAqB,YAAoB,GAAG;QAClE,IAAI,CAAC,aAAa,OAAO;QACzB,IAAI,YAAY,MAAM,IAAI,WAAW,OAAO;QAC5C,OAAO,YAAY,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;IACtD;AACF;AAGO,MAAM,cAAc,WAAW,WAAW;AAC1C,MAAM,iBAAiB,WAAW,cAAc;AAChD,MAAM,mBAAmB,WAAW,gBAAgB;AACpD,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,aAAa,WAAW,UAAU;AACxC,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,aAAa,WAAW,UAAU;AACxC,MAAM,oBAAoB,WAAW,iBAAiB", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/%28pages%29/services/%5Bslug%5D/service-detail-client.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Page<PERSON>ero from \"@/app/_components/page_hero/page_hero\";\r\nimport Image from \"next/image\";\r\nimport { serviceApi, Service } from '@/lib/api/service/serviceApi';\r\nimport { toast } from 'react-toastify';\r\nimport imageservice from \"@/public/image/icons/services/servicesdetails.png\";\r\n\r\n// Client component props\r\ntype ServiceDetailClientProps = {\r\n  params: { slug: string };\r\n  searchParams?: { [key: string]: string | string[] | undefined };\r\n};\r\n\r\nconst ServiceDetailClient = ({ params, searchParams }: ServiceDetailClientProps) => {\r\n  const [service, setService] = useState<Service | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Add safety check to prevent errors\r\n  if (!params || !params.slug) {\r\n    return (\r\n      <div className=\"py-20 text-center\">\r\n        <p>Invalid service URL.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Fetch service by ID\r\n  const fetchService = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n       const response = await serviceApi.getServiceById(params.slug);\r\n\r\n\r\n      if (response.success && response.data) {\r\n        setService(response.data);\r\n      } else {\r\n        throw new Error('Service not found');\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error fetching service:', err);\r\n      setError(err.message || 'Failed to load service');\r\n      toast.error('Failed to load service details.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchService();\r\n  }, [params.slug]);\r\n\r\n  // Loading state\r\n  if (isLoading) {\r\n    return (\r\n      <>\r\n        <PageHero title=\"LOADING...\" breadcrumbs={[\r\n          { label: \"HOME\", href: \"/\" },\r\n          { label: \"SERVICES\", href: \"/services\" }\r\n        ]} />\r\n        <div className=\"py-20 text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FE904B] mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">Loading service details...</p>\r\n        </div>\r\n      </>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error || !service) {\r\n    return (\r\n      <>\r\n        <PageHero title=\"SERVICE NOT FOUND\" breadcrumbs={[\r\n          { label: \"HOME\", href: \"/\" },\r\n          { label: \"SERVICES\", href: \"/services\" }\r\n        ]} />\r\n        <div className=\"py-20 text-center\">\r\n          <div className=\"text-red-500 mb-4\">\r\n            <svg className=\"w-16 h-16 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n            <p className=\"text-lg font-medium\">Service not found</p>\r\n            <p className=\"text-sm text-gray-600 mt-2\">{error}</p>\r\n          </div>\r\n          <button\r\n            onClick={fetchService}\r\n            className=\"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors mr-4\"\r\n          >\r\n            Try Again\r\n          </button>\r\n          <a\r\n            href=\"/services\"\r\n            className=\"px-4 py-2 border border-[#FE904B] text-[#FE904B] rounded-md hover:bg-[#FE904B] hover:text-white transition-colors\"\r\n          >\r\n            Back to Services\r\n          </a>\r\n        </div>\r\n      </>\r\n    );\r\n  }\r\n  // Render service details\r\n  const breadcrumbs = [\r\n    { label: \"HOME\", href: \"/\" },\r\n    { label: \"SERVICE DETAILS\", href: \"/services\" },\r\n\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <PageHero title='SERVICE DETAILS' breadcrumbs={breadcrumbs} />\r\n\r\n      {/* Hero Section */}\r\n      <div className=\"bg-gradient-to-b from-gray-50 to-white py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-6xl mx-auto text-center\">\r\n          <h1 className=\"text-xs sm:text-sm text-[#BC7B77] uppercase tracking-wider mb-3 sm:mb-4 font-medium\">LATEST SERVICE</h1>\r\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 uppercase text-gray-900 leading-tight\">\r\n            {service.title}\r\n          </h2>\r\n\r\n          <div className=\"max-w-[68rem] mx-auto space-y-4 sm:space-y-6\">\r\n            <p className=\"text-gray-700 text-sm sm:text-base lg:text-lg leading-relaxed text-center sm:text-left px-4 sm:px-0\">\r\n              {service.description}\r\n            </p>\r\n\r\n            {service.description2 && (\r\n              <p className=\"text-gray-600 text-xs sm:text-sm lg:text-base leading-relaxed text-center sm:text-left px-4 sm:px-0\">\r\n                {service.description2}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Image Section */}\r\n      <div className=\"py-6 sm:py-8 lg:py-12\">\r\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"relative h-[250px] sm:h-[350px] md:h-[450px] lg:h-[500px] xl:h-[550px] rounded-xl sm:rounded-2xl overflow-hidden shadow-xl\">\r\n            <Image\r\n              src={service.image}\r\n              alt={service.title}\r\n              fill\r\n              className=\"object-cover hover:scale-105 transition-transform duration-700\"\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* How We Do It Section */}\r\n      <div className=\"py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-orange-50\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          {/* Section Header */}\r\n          <div className=\"text-center mb-12 sm:mb-16\">\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-gray-900 mb-4 sm:mb-6 uppercase tracking-wide\">\r\n              How We Do It\r\n            </h2>\r\n           \r\n          </div>\r\n\r\n          {/* Cards Grid */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10\">\r\n            {service.howWeDoIt && service.howWeDoIt.length > 0 ? (\r\n              service.howWeDoIt.slice(0, 3).map((step, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"group relative bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 border \"style={{ borderColor: \"rgba(229, 218, 207, 1)\" }}\r\n                >\r\n                  {/* Card Number */}\r\n                  {/* <div className=\"absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg \">\r\n                    <span className=\"text-white font-bold text-lg sm:text-xl\">{index + 1}</span>\r\n                  </div> */}\r\n\r\n                  {/* Icon */}\r\n                  {/* <div className=\"w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 group-hover:scale-105 transition-transform duration-300\">\r\n                    <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div> */}\r\n\r\n                  {/* Content */}\r\n                  <div className=\"space-y-4\">\r\n                    <h3 className=\"text-lg sm:text-xl lg:text-2xl  text-center  font-semibold text-gray-900 leading-tight \">\r\n                      {step.title}\r\n                    </h3>\r\n                    <p className=\"text-gray-600 text-center text-sm sm:text-base leading-relaxed\">\r\n                      {step.description}\r\n                    </p>\r\n                  </div>\r\n\r\n                  {/* Decorative Element */}\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 \"></div>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              // Default steps if no howWeDoIt data\r\n              <>\r\n                <div className=\"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 \">\r\n                  {/* <div className=\"absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg \">\r\n                    <span className=\"text-white font-bold text-lg sm:text-xl\">1</span>\r\n                  </div> */}\r\n                  {/* <div className=\"w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 \">\r\n                    { <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                      </svg>\r\n                    </div> }\r\n                  </div> */}\r\n                  <div className=\"space-y-4\">\r\n                    <h3 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight \">\r\n                      Professional Planning\r\n                    </h3>\r\n                    <p className=\"text-gray-600 text-sm sm:text-base leading-relaxed\">\r\n                      Our experienced team creates detailed plans tailored to your specific needs and vision.\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"></div>\r\n                </div>\r\n\r\n                <div className=\"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-orange-200 hover:-translate-y-2\">\r\n                  {/* <div className=\"absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300\">\r\n                    <span className=\"text-white font-bold text-lg sm:text-xl\">2</span>\r\n                  </div> */}\r\n                  {/* <div className=\"w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 group-hover:scale-105 transition-transform duration-300\">\r\n                    <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div> */}\r\n                  <div className=\"space-y-4\">\r\n                    <h3 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight \">\r\n                      Quality Execution\r\n                    </h3>\r\n                    <p className=\"text-gray-600 text-sm sm:text-base leading-relaxed\">\r\n                      We ensure every detail is executed flawlessly with attention to quality and precision.\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left\"></div>\r\n                </div>\r\n\r\n                <div className=\"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 \">\r\n                  {/* <div className=\"absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300\">\r\n                    <span className=\"text-white font-bold text-lg sm:text-xl\">3</span>\r\n                  </div> */}\r\n                  {/* <div className=\"w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 \">\r\n                    { <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n                      </svg>\r\n                    </div> }\r\n                  </div> */}\r\n                  <div className=\"space-y-4\">\r\n                    <h3 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight \">\r\n                      Memorable Experience\r\n                    </h3>\r\n                    <p className=\"text-gray-600 text-sm sm:text-base leading-relaxed\">\r\n                      Creating unforgettable moments that exceed your expectations and delight your guests.\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 \"></div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ServiceDetailClient;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAcA,MAAM,sBAAsB,CAAC,EAAE,MAAM,EAAE,YAAY,EAA4B;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,qCAAqC;IACrC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE;QAC3B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,sBAAsB;IACtB,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,SAAS;YAER,MAAM,WAAW,MAAM,mIAAA,CAAA,aAAU,CAAC,cAAc,CAAC,OAAO,IAAI;YAG7D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,WAAW,SAAS,IAAI;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,IAAI,OAAO,IAAI;YACxB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,OAAO,IAAI;KAAC;IAEhB,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE;;8BACE,8OAAC,6IAAA,CAAA,UAAQ;oBAAC,OAAM;oBAAa,aAAa;wBACxC;4BAAE,OAAO;4BAAQ,MAAM;wBAAI;wBAC3B;4BAAE,OAAO;4BAAY,MAAM;wBAAY;qBACxC;;;;;;8BACD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;IAI1C;IAEA,cAAc;IACd,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE;;8BACE,8OAAC,6IAAA,CAAA,UAAQ;oBAAC,OAAM;oBAAoB,aAAa;wBAC/C;4BAAE,OAAO;4BAAQ,MAAM;wBAAI;wBAC3B;4BAAE,OAAO;4BAAY,MAAM;wBAAY;qBACxC;;;;;;8BACD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAyB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAChF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAE7C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;IAMT;IACA,yBAAyB;IACzB,MAAM,cAAc;QAClB;YAAE,OAAO;YAAQ,MAAM;QAAI;QAC3B;YAAE,OAAO;YAAmB,MAAM;QAAY;KAE/C;IAED,qBACE;;0BACE,8OAAC,6IAAA,CAAA,UAAQ;gBAAC,OAAM;gBAAkB,aAAa;;;;;;0BAG/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsF;;;;;;sCACpG,8OAAC;4BAAG,WAAU;sCACX,QAAQ,KAAK;;;;;;sCAGhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;gCAGrB,QAAQ,YAAY,kBACnB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;0BAQ/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,KAAK;gCAClB,KAAK,QAAQ,KAAK;gCAClB,IAAI;gCACJ,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAgH;;;;;;;;;;;sCAOhI,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,IAC/C,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACvC,8OAAC;oCAEC,WAAU;oCAAuE,OAAO;wCAAE,aAAa;oCAAyB;;sDAiBhI,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;sDAKrB,8OAAC;4CAAI,WAAU;;;;;;;mCA5BV;;;;4CAgCT,qCAAqC;0CACrC;;kDACE,8OAAC;wCAAI,WAAU;;0DAWb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwE;;;;;;kEAGtF,8OAAC;wDAAE,WAAU;kEAAqD;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DAWb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwE;;;;;;kEAGtF,8OAAC;wDAAE,WAAU;kEAAqD;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DAWb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwE;;;;;;kEAGtF,8OAAC;wDAAE,WAAU;kEAAqD;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjC;uCAEe", "debugId": null}}]}