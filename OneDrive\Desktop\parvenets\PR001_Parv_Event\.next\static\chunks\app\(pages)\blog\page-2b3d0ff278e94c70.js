(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[803],{1793:(e,t,a)=>{Promise.resolve().then(a.bind(a,6339))},6339:(e,t,a)=>{"use strict";a.d(t,{default:()=>h});var s=a(5155),l=a(2115),r=a(1235),d=a(6766),o=a(5695),i=a(5305),n=a(9911),c=a(1661);function x(e){let{blog:t}=e,a=(0,o.useRouter)(),[x,m]=(0,l.useState)(!1);console.log("BlogCard - blog data:",{id:t._id,title:t.title,createdAt:t.createdAt,createdAtType:typeof t.createdAt,formattedDate:t.createdAt?(0,r._n)(t.createdAt):"No createdAt"});let g=e=>{e.stopPropagation(),m(!0)};return(0,s.jsxs)("div",{onClick:()=>{let e=t.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");a.push("/blog/".concat(t._id,"/").concat(e))},className:"bg-white border border-gray-200 shadow-lg hover:shadow-xl rounded-[16px] md:rounded-[20px] cursor-pointer p-0 flex flex-col relative overflow-hidden transition-all duration-300 group hover:scale-[1.02] md:hover:scale-105",children:[(0,s.jsxs)("div",{className:"relative w-full h-[200px] sm:h-[250px] md:h-[300px] rounded-t-[16px] md:rounded-t-[20px] overflow-hidden",children:[t.imageUrl?(0,s.jsx)(d.default,{src:t.imageUrl,alt:t.title,width:500,height:300,className:"object-cover w-full h-full",onError:e=>{e.target.style.display="none"}}):(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-orange-500 to-orange-700 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-white text-center p-4",children:[(0,s.jsx)("div",{className:"text-2xl md:text-3xl font-bold mb-2",children:"\uD83D\uDCDD"}),(0,s.jsx)("div",{className:"text-sm md:text-base font-medium",children:"Blog Post"})]})}),(0,s.jsx)("div",{className:"absolute top-3 left-3 md:top-4 md:left-4 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 md:px-3 shadow-md",children:(0,s.jsx)("span",{className:"text-gray-900 text-xs md:text-sm font-medium",children:t.createdAt?(0,r._n)(t.createdAt):"No date"})}),(0,s.jsx)("div",{className:"absolute top-3 right-3 md:top-4 md:right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,s.jsx)("div",{className:"bg-white/20 backdrop-blur-sm rounded-full p-1 md:p-2",children:(0,s.jsx)(i.J1z,{className:"w-4 h-4 md:w-5 md:h-5 text-white"})})})]}),(0,s.jsxs)("div",{className:"p-4 md:p-6 flex flex-col gap-3 md:gap-4 flex-1",children:[(0,s.jsxs)("div",{className:"relative group/category",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"bg-orange-500 text-white px-2 py-1 md:px-3 md:py-1 rounded-full text-xs md:text-sm",children:t.category[0]}),t.category.length>1&&(0,s.jsxs)("span",{className:"text-white/50 text-xs md:text-sm cursor-pointer",children:["+",t.category.length-1," more"]})]}),t.category.length>1&&(0,s.jsx)("div",{className:"absolute top-full left-0 mt-1 md:mt-2 bg-zinc-800 rounded-lg p-2 md:p-3 opacity-0 group-hover/category:opacity-100 transition-opacity duration-300 z-10 min-w-[150px] md:min-w-[200px]",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-1 md:gap-2",children:t.category.slice(1).map((e,t)=>(0,s.jsx)("span",{className:"bg-orange-500/80 text-white px-1 py-0.5 md:px-2 md:py-1 rounded-full text-[10px] md:text-xs",children:e},t))})})]}),(0,s.jsx)("h3",{className:"text-[16px] md:text-[20px] font-bold text-gray-900 leading-tight line-clamp-2",children:t.title}),(0,s.jsx)("p",{className:"text-gray-600 text-xs md:text-sm line-clamp-3",children:t.description}),(0,s.jsxs)("div",{className:"flex justify-between items-end mt-auto pt-3 md:pt-4",children:[(0,s.jsx)("div",{className:"flex flex-wrap gap-1 md:gap-2",children:t.keywords.slice(0,2).map((e,t)=>(0,s.jsx)("span",{className:"bg-gray-100 text-gray-700 px-1.5 py-0.5 md:px-2 md:py-1 rounded text-[10px] md:text-xs",children:e.replace(/[\[\]"]/g,"")},t))}),(0,s.jsxs)("div",{className:"flex gap-1 md:gap-2",children:[(0,s.jsx)("button",{onClick:g,className:"p-1.5 md:p-2 bg-gray-100 hover:bg-blue-600 rounded-full transition-colors group",children:(0,s.jsx)(n.iYk,{className:"w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white"})}),(0,s.jsx)("button",{onClick:g,className:"p-1.5 md:p-2 bg-gray-100 hover:bg-pink-600 rounded-full transition-colors group",children:(0,s.jsx)(n.ao$,{className:"w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white"})}),(0,s.jsx)("button",{onClick:g,className:"p-1.5 md:p-2 bg-gray-100 hover:bg-blue-700 rounded-full transition-colors group",children:(0,s.jsx)(n.QEs,{className:"w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white"})})]})]})]}),(0,s.jsx)(c.A,{isOpen:x,onClose:()=>m(!1),blogId:t._id,blogTitle:t.title})]})}var m=a(312);function g(e){let{currentPage:t,totalPages:a,onPageChange:l}=e;return a<=1?null:(0,s.jsxs)("div",{className:"flex justify-center items-center gap-2 md:gap-4 mt-6 md:mt-8",children:[t>1&&(0,s.jsx)("button",{onClick:()=>l(t-1),className:"px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700 text-sm md:text-base",children:"Previous"}),(()=>{let e=[],s=window.innerWidth<768?3:5;if(a<=s)return Array.from({length:a},(e,t)=>t+1);let l=Math.max(1,t-Math.floor(s/2)),r=l+s-1;r>a&&(l=Math.max(1,(r=a)-s+1)),l>1&&(e.push(1),l>2&&e.push("..."));for(let t=l;t<=r;t++)e.push(t);return r<a&&(r<a-1&&e.push("..."),e.push(a)),e})().map((e,a)=>(0,s.jsx)("button",{onClick:()=>"number"==typeof e?l(e):null,className:"w-8 h-8 md:w-12 md:h-12 rounded-lg transition-all duration-300 text-sm md:text-base ".concat(t===e?"bg-orange-500 text-white font-bold":"number"==typeof e?"bg-zinc-800 text-white hover:bg-zinc-700":"bg-transparent text-white cursor-default"),disabled:"..."===e,children:e},a)),t<a&&(0,s.jsx)("button",{onClick:()=>l(t+1),className:"px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700 text-sm md:text-base",children:"Next"})]})}function h(){let[e,t]=(0,l.useState)([]),[a,d]=(0,l.useState)([]),[o,i]=(0,l.useState)([]),[n,c]=(0,l.useState)(!0),[h,p]=(0,l.useState)(""),[u,f]=(0,l.useState)(""),[b,y]=(0,l.useState)(1),[w,j]=(0,l.useState)(1),[v,N]=(0,l.useState)(!1);(0,l.useEffect)(()=>{C(),k(),S()},[b,h,u]);let C=async()=>{try{c(!0);let e=await (0,r.f_)({page:b,pageSize:6,search:h||void 0,category:u||void 0});e&&e.status&&e.data?(t(e.data.data||[]),j(e.data.totalPages||1)):(t([]),j(1))}catch(e){console.error("Error fetching blogs:",e),t([]),j(1)}finally{c(!1)}},k=async()=>{try{let e=await (0,r.tz)();e&&e.status&&e.data?d(e.data):d([])}catch(e){console.error("Error fetching categories:",e),d([])}},S=async()=>{try{let e=await (0,r.z$)(3);e.status&&i(e.data.data)}catch(e){console.error("Error fetching recent blogs:",e)}},z=e=>{e.preventDefault(),y(1),C()},A=e=>{p(e)},_=e=>{f(e===u?"":e),y(1)},E=e=>{p(e.replace(/[\[\]"]/g,"")),y(1),C()},P=e.flatMap(e=>e.keywords).filter((e,t,a)=>a.indexOf(e)===t);return(0,s.jsx)("div",{className:"text-gray-900 w-full flex justify-center py-10 md:py-20 bg-white",children:(0,s.jsxs)("div",{className:"w-[95%] max-w-[1400px] flex flex-col lg:flex-row gap-8",children:[(0,s.jsxs)("div",{className:"lg:hidden flex justify-between items-center mb-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Blog Posts"}),(0,s.jsx)("button",{onClick:()=>N(!v),className:"bg-orange-500 text-white px-4 py-2 rounded-lg",children:v?"Hide Filters":"Show Filters"})]}),v&&(0,s.jsx)("div",{className:"lg:hidden w-full mb-8",children:(0,s.jsx)(m.A,{categories:a,recentBlogs:o,allKeywords:P,searchQuery:h,selectedCategory:u,onSearchChange:A,onSearchSubmit:z,onCategoryFilter:_,onKeywordClick:E})}),(0,s.jsx)("div",{className:"flex-1",children:n?(0,s.jsx)("div",{className:"text-center py-20",children:(0,s.jsx)("div",{className:"text-gray-900",children:"Loading blogs..."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 md:gap-8 mb-8 md:mb-16",children:e.map(e=>(0,s.jsx)(x,{blog:e},e._id))}),e.length>0&&(0,s.jsx)(g,{currentPage:b,totalPages:w,onPageChange:e=>{y(e)}})]})}),(0,s.jsx)("div",{className:"hidden lg:block w-[350px]",children:(0,s.jsx)(m.A,{categories:a,recentBlogs:o,allKeywords:P,searchQuery:h,selectedCategory:u,onSearchChange:A,onSearchSubmit:z,onCategoryFilter:_,onKeywordClick:E})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[711,87,844,83,854,380,586,196,441,684,358],()=>t(1793)),_N_E=e.O()}]);