"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  type CarouselApi,
} from "@/components/ui/carousel";

import { useEffect, useState } from "react";
import { heroApi, HeroSection } from '@/lib/api/hero/heroApi';
import { toast } from 'react-toastify';
import Link from "next/link";




const TopSection = () => {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [heroSections, setHeroSections] = useState<HeroSection[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch hero sections from API
  const fetchHeroSections = async () => {
    try {
      setIsLoading(true);

      const response = await heroApi.getHeroSections({
        activeOnly: true,
        sortBy: 'sortOrder',
        sortOrder: 'asc',
        limit: 10
      });

      if (response.success && response.data.heroSections.length > 0) {
        setHeroSections(response.data.heroSections);
        console.log('✅ Hero sections loaded:', response.data.heroSections.length);
      } else {
        console.log('⚠️ No hero sections found');
      }
    } catch (error: any) {
      console.error('❌ Error fetching hero sections:', error);
      toast.error('Failed to load hero images');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch hero sections on component mount
  useEffect(() => {
    fetchHeroSections();
  }, []);

  // Autoplay every 4 seconds
  useEffect(() => {
    if (!api) return;

    const interval = setInterval(() => {
      api.scrollNext();
    }, 4000);

    return () => clearInterval(interval);
  }, [api]);

  return (
    <div className="relative w-full h-[400px] sm:h-[450px] md:h-[500px] lg:h-[550px] xl:h-[600px] 2xl:h-[650px] overflow-hidden">
      <Carousel orientation="horizontal" opts={{ loop: true }} setApi={setApi}>
        <CarouselContent>
          {heroSections.length > 0 ? (
            heroSections.map((hero) => (
              <CarouselItem key={hero._id} className="w-full h-full">
                <img
                  src={hero.image}
                  alt={hero.title}
                  className="w-full h-[600px] object-cover"
                />
              </CarouselItem>
            ))
          ) : (
            !isLoading && (
              <CarouselItem className="w-full h-full">
                <div className="w-full h-[600px] bg-gray-200 flex items-center justify-center">
                  <p className="text-gray-500">No hero images available</p>
                </div>
              </CarouselItem>
            )
          )}
        </CarouselContent>

        {/* Navigation Buttons - hidden on mobile */}
        <CarouselPrevious className="hidden sm:flex absolute left-4 top-1/2 -translate-y-1/2 z-30 bg-[#FE904B] hover:bg-[#fc873f] text-white" />
        <CarouselNext className="hidden sm:flex absolute right-4 top-1/2 -translate-y-1/2 z-30 bg-[#FE904B] hover:bg-[#fc873f] text-white" />

      </Carousel>

      {/* Black overlay */}
      <div className="absolute inset-0 bg-black opacity-60 z-10" />

      {/* Overlay Text */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white z-20 px-4 text-center gap-y-4 sm:gap-y-5 md:gap-y-6">
        <h1 className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-[#FE904B] font-bold drop-shadow-lg font-cormorant">
          Trust in Our Expert Event Planning
        </h1>
        <p className="text-2xl sm:text-3xl md:text-4xl lg:text-4xl xl:text-4xl max-w-[100%] md:max-w-2xl font-cormorant font-semibold drop-shadow-md">
          JOIN US FOR AN UNFORGETTABLE EXPERIENCE
        </p>
        <p className="text-sm sm:text-base md:text-lg max-w-[90%] md:max-w-xl font-cormorant mb-2">
          Discover amazing moments with top professionals
        </p>
        <div className="flex flex-wrap justify-center gap-3 sm:gap-4">
          <Link href="/services">
            <button className="bg-[#FE904B] hover:bg-[#f18440] px-5 sm:px-6 py-2 text-sm sm:text-base font-urbanist rounded">
              our services
            </button></Link>
          <Link href={"/contact"}>
          <button className="bg-transparent border border-white text-white hover:border-[#FE904B] hover:text-[#FE904B] px-5 sm:px-6 py-2 text-sm sm:text-base font-urbanist rounded">
            contact us
          </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TopSection;
