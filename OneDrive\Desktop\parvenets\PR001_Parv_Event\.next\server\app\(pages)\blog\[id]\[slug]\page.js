(()=>{var e={};e.id=701,e.ids=[701],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4770:(e,t,s)=>{Promise.resolve().then(s.bind(s,65824))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58434:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\(pages)\\\\blog\\\\[id]\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\blog\\[id]\\[slug]\\page.tsx","default")},58504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),m=s(30893),l={};for(let e in m)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>m[e]);s.d(t,l);let d={children:["",{children:["(pages)",{children:["blog",{children:["[id]",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58434)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\blog\\[id]\\[slug]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,48754)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\blog\\[id]\\[slug]\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(pages)/blog/[id]/[slug]/page",pathname:"/blog/[id]/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64602:(e,t,s)=>{Promise.resolve().then(s.bind(s,58434))},65824:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),a=s(43210),n=s(30474),i=s(85814),m=s.n(i),l=s(16189),d=s(17257),o=s(17019),c=s(72837),x=s(83230),p=s(34115),g=s(684);let u={createComment:async e=>(await p.u.post((0,g.c$)("/comments"),e)).data,getApprovedComments:async(e={})=>{let t=new URLSearchParams;e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),e.limit&&t.append("limit",e.limit.toString()),e.blogId&&t.append("blogId",e.blogId.toString());let s=`/comments/approved${t.toString()?`?${t.toString()}`:""}`;return(await p.u.get((0,g.c$)(s))).data},getCommentsByBlogId:async(e,t={})=>{let s=new URLSearchParams;t.page&&s.append("page",t.page.toString()),t.limit&&s.append("limit",t.limit.toString()),t.sortBy&&s.append("sortBy",t.sortBy),t.sortOrder&&s.append("sortOrder",t.sortOrder),t.status&&s.append("status",t.status),void 0!==t.isVisible&&s.append("isVisible",t.isVisible.toString());let r=`/comments/blog/${e}${s.toString()?`?${s.toString()}`:""}`;return(await p.u.get((0,g.c$)(r))).data},getCommentCountByBlogId:async e=>(await p.u.get((0,g.c$)(`/comments/blog/${e}/count`))).data,getAllComments:async(e={})=>{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.status&&t.append("status",e.status),void 0!==e.isVisible&&t.append("isVisible",e.isVisible.toString()),e.blogId&&t.append("blogId",e.blogId.toString()),e.search&&t.append("search",e.search),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s=`/comments${t.toString()?`?${t.toString()}`:""}`;return(await p.u.get((0,g.c$)(s))).data},getCommentById:async e=>(await p.u.get((0,g.c$)(`/comments/${e}`))).data,updateComment:async(e,t)=>(await p.u.put((0,g.c$)(`/comments/${e}`),t)).data,deleteComment:async e=>(await p.u.delete((0,g.c$)(`/comments/${e}`))).data,getCommentStatistics:async()=>(await p.u.get((0,g.c$)("/comments/statistics"))).data,approveComment:async e=>u.updateComment(e,{status:"approved",isVisible:!0}),rejectComment:async e=>u.updateComment(e,{status:"rejected",isVisible:!1}),markAsSpam:async e=>u.updateComment(e,{status:"spam",isVisible:!1}),getPendingComments:async(e={})=>u.getAllComments({...e,status:"pending"})},h=e=>{try{let t=new Date(e),s=new Date().getTime()-t.getTime(),r=Math.floor(s/864e5),a=Math.floor(s/36e5),n=Math.floor(s/6e4);if(n<1)return"Just now";if(n<60)return`${n} minutes ago`;if(a<24)return`${a} hours ago`;if(1===r)return"Yesterday";if(r<7)return`${r} days ago`;return t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(e){return"Unknown"}},b=async e=>u.createComment(e),y=async e=>u.getApprovedComments(e),v=({blogId:e,blogTitle:t})=>{let[s,n]=(0,a.useState)([]),[i,m]=(0,a.useState)(!0),[l,d]=(0,a.useState)(!1),[c,x]=(0,a.useState)(!1),[p,g]=(0,a.useState)(null),[u,v]=(0,a.useState)({name:"",email:"",comment:""}),[f,j]=(0,a.useState)({});(0,a.useEffect)(()=>{e&&w()},[e]);let w=async()=>{if(e)try{m(!0);let t=await y({limit:50,blogId:e});t.status&&t.data?n(Array.isArray(t.data)?t.data:[]):n([])}catch(e){console.error("Error fetching comments:",e),g("Failed to load comments"),n([])}finally{m(!1)}},N=()=>{let e={};return u.name.trim()?u.name.trim().length<2&&(e.name="Name must be at least 2 characters"):e.name="Name is required",u.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u.email)||(e.email="Please enter a valid email address"):e.email="Email is required",u.comment.trim()?u.comment.trim().length<10?e.comment="Comment must be at least 10 characters":u.comment.trim().length>1e3&&(e.comment="Comment must be less than 1000 characters"):e.comment="Comment is required",j(e),0===Object.keys(e).length},C=e=>{let{name:t,value:s}=e.target;v(e=>({...e,[t]:s})),f[t]&&j(e=>({...e,[t]:void 0}))},S=async t=>{if(t.preventDefault(),N()&&e)try{d(!0),g(null);let t=await b({name:u.name.trim(),email:u.email.trim(),comment:u.comment.trim(),blogId:e});t.status?(v({name:"",email:"",comment:""}),x(!0),w(),setTimeout(()=>x(!1),5e3)):g(t.message||"Failed to submit comment. Please try again.")}catch(t){console.error("Error submitting comment:",t);let e="Failed to submit comment. Please try again.";t.response?.data?.message?e=t.response.data.message:t.message?e=t.message:"string"==typeof t&&(e=t),(e.includes("ENOTFOUND")||e.includes("mongodb.net"))&&(e="Database connection error. Please try again later or contact support."),g(e)}finally{d(!1)}};return(0,r.jsx)("div",{className:"w-full text-gray-900 py-8 md:py-12",children:(0,r.jsxs)("div",{className:"w-full px-0 md:px-4",children:[(0,r.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,r.jsxs)("h2",{className:"text-xl md:text-2xl font-bold text-gray-900 mb-2 flex items-center",children:[(0,r.jsx)(o.mEP,{className:"mr-3 h-5 w-5 md:h-6 md:w-6 text-orange-500"}),"Comments (",s.length,")"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm md:text-base",children:"Share your thoughts about this article. Your comment will be reviewed before publishing."})]}),c&&(0,r.jsx)("div",{className:"mb-4 md:mb-6 bg-green-50 border border-green-200 rounded-lg p-3 md:p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A3x,{className:"h-4 w-4 md:h-5 md:w-5 text-green-600 mr-2 md:mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xs md:text-sm font-medium text-green-800",children:"Comment Submitted Successfully!"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-green-700 mt-1",children:"Thank you for your comment. It will be reviewed and published soon."})]})]})}),p&&(0,r.jsx)("div",{className:"mb-4 md:mb-6 bg-red-50 border border-red-200 rounded-lg p-3 md:p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.y3G,{className:"h-4 w-4 md:h-5 md:w-5 text-red-600 mr-2 md:mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xs md:text-sm font-medium text-red-800",children:"Error"}),(0,r.jsx)("p",{className:"text-xs md:text-sm text-red-700 mt-1",children:p})]})]})}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-lg p-4 md:p-6 mb-6 md:mb-8",children:[(0,r.jsx)("h3",{className:"text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4",children:"Leave a Comment"}),(0,r.jsxs)("form",{onSubmit:S,className:"space-y-3 md:space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2",children:[(0,r.jsx)(o.JXP,{className:"inline mr-2 h-3 w-3 md:h-4 md:w-4"}),"Name *"]}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:u.name,onChange:C,className:`w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ${f.name?"border-red-500":"border-gray-300"}`,placeholder:"Your full name",disabled:l}),f.name&&(0,r.jsx)("p",{className:"mt-1 text-xs md:text-sm text-red-400",children:f.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"email",className:"block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2",children:[(0,r.jsx)(o.pHD,{className:"inline mr-2 h-3 w-3 md:h-4 md:w-4"}),"Email *"]}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:u.email,onChange:C,className:`w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ${f.email?"border-red-500":"border-gray-300"}`,placeholder:"<EMAIL>",disabled:l}),f.email&&(0,r.jsx)("p",{className:"mt-1 text-xs md:text-sm text-red-400",children:f.email})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"comment",className:"block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2",children:[(0,r.jsx)(o.mEP,{className:"inline mr-2 h-3 w-3 md:h-4 md:w-4"}),"Comment *"]}),(0,r.jsx)("textarea",{id:"comment",name:"comment",rows:4,value:u.comment,onChange:C,className:`w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ${f.comment?"border-red-500":"border-gray-300"}`,placeholder:"Share your thoughts about this article...",disabled:l}),(0,r.jsx)("div",{className:"flex justify-between items-center mt-1",children:f.comment?(0,r.jsx)("p",{className:"text-xs md:text-sm text-red-400",children:f.comment}):(0,r.jsxs)("p",{className:"text-xs md:text-sm text-gray-500",children:[u.comment.length,"/1000 characters"]})})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:l,className:"inline-flex items-center px-4 py-2 md:px-6 md:py-3 border border-transparent text-xs md:text-sm font-medium rounded-lg text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.kGk,{className:"mr-2 h-3 w-3 md:h-4 md:w-4"}),"Submit Comment"]})})})]})]}),(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 md:mb-6",children:s.length>0?"Recent Comments":"No Comments Yet"}),i?(0,r.jsx)("div",{className:"flex items-center justify-center py-6 md:py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 md:h-8 md:w-8 border-b-2 border-orange-500"})}):0===s.length?(0,r.jsxs)("div",{className:"text-center py-6 md:py-8 text-gray-400",children:[(0,r.jsx)(o.mEP,{className:"mx-auto h-8 w-8 md:h-12 md:w-12 text-gray-600 mb-3 md:mb-4"}),(0,r.jsx)("p",{className:"text-sm md:text-base",children:"Be the first to leave a comment!"})]}):(0,r.jsx)("div",{className:"space-y-4 md:space-y-6",children:s.map(e=>(0,r.jsxs)("div",{className:"bg-white border border-gray-200 shadow-sm rounded-lg p-4 md:p-6",children:[(0,r.jsx)("div",{className:"flex items-start justify-between mb-2 md:mb-3",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 md:w-10 md:h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold",children:e.name.charAt(0).toUpperCase()}),(0,r.jsxs)("div",{className:"ml-2 md:ml-3",children:[(0,r.jsx)("h4",{className:"text-gray-900 font-medium text-sm md:text-base",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center text-xs md:text-sm text-gray-500",children:[(0,r.jsx)(o.Ohp,{className:"mr-1 h-2 w-2 md:h-3 md:w-3"}),h(e.createdAt)]})]})]})}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed text-sm md:text-base",children:e.comment})]},e.id))})]})]})})};var f=s(81244);function j(){let e=(0,l.useParams)(),t=(0,l.useRouter)(),[s,i]=(0,a.useState)(null),[p,g]=(0,a.useState)([]),[u,h]=(0,a.useState)([]),[b,y]=(0,a.useState)(!0),[j,w]=(0,a.useState)(!1),[N,C]=(0,a.useState)(""),[S,P]=(0,a.useState)(!1),[k,_]=(0,a.useState)(!1),$=e=>{e.preventDefault(),N.trim()&&t.push(`/blog?search=${encodeURIComponent(N)}`)},A=e=>{C(e)},D=e=>{t.push(`/blog?category=${encodeURIComponent(e)}`)},B=e=>{t.push(`/blog?search=${encodeURIComponent(e)}`)};return b?(0,r.jsx)("div",{className:"min-h-screen bg-white text-gray-900 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("div",{className:"text-gray-900 text-xl",children:"Loading blog..."})})}):s?(0,r.jsxs)("div",{className:"bg-white text-gray-900 py-10 md:py-16 lg:py-20 overflow-x-hidden",children:[(0,r.jsxs)("div",{className:"w-[95%] max-w-[1400px] mx-auto flex flex-col lg:flex-row gap-6 lg:gap-8",children:[(0,r.jsxs)("div",{className:"lg:hidden flex justify-between items-center mb-4",children:[(0,r.jsxs)("button",{onClick:()=>t.back(),className:"flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors",children:[(0,r.jsx)(d.atu,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"text-sm",children:"Back"})]}),(0,r.jsx)("button",{onClick:()=>_(!k),className:"bg-orange-500 text-white px-4 py-2 rounded-lg text-sm",children:k?"Hide Sidebar":"Show Sidebar"})]}),k&&(0,r.jsx)("div",{className:"lg:hidden w-full mb-8",children:(0,r.jsx)(x.A,{categories:p,recentBlogs:u,allKeywords:s?.keywords||[],searchQuery:N,selectedCategory:"",onSearchChange:A,onSearchSubmit:$,onCategoryFilter:D,onKeywordClick:B})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("button",{onClick:()=>t.back(),className:"hidden lg:flex items-center gap-2 text-gray-600 hover:text-orange-500 mb-6 transition-colors",children:[(0,r.jsx)(d.atu,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Back to Blogs"})]}),(0,r.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-3 md:mb-4",children:s.category.map((e,t)=>(0,r.jsx)("span",{className:"bg-orange-500 text-white px-2 py-1 md:px-3 rounded-full text-xs md:text-sm",children:e},t))}),(0,r.jsx)("h1",{className:"text-2xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-3 md:mb-4",children:s.title}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2 md:gap-4 text-gray-600 text-sm md:text-base mb-4 md:mb-6",children:[(0,r.jsxs)("span",{children:["Published: ",(0,c._n)(s.createdAt)]}),(0,r.jsx)("span",{className:"hidden md:inline",children:"•"}),(0,r.jsxs)("span",{children:["Views: ",s.views]})]}),(0,r.jsx)("div",{className:"relative w-full overflow-hidden h-[250px] sm:h-[350px] md:h-[450px] lg:h-[650px] rounded-[16px] md:rounded-[20px] mb-6 md:mb-8",children:s.imageUrl?(0,r.jsx)(n.default,{src:s.imageUrl,alt:s.title,fill:!0,className:"object-cover w-full h-full",priority:!0}):(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-orange-500 to-orange-700 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-white text-center p-8",children:[(0,r.jsx)("div",{className:"text-6xl md:text-8xl font-bold mb-4",children:"\uD83D\uDCDD"}),(0,r.jsx)("div",{className:"text-xl md:text-2xl font-medium",children:"Blog Post"})]})})})]}),(0,r.jsx)("div",{className:"prose prose-gray max-w-none overflow-hidden break-words mb-6 md:mb-8",children:(0,r.jsx)("div",{className:"text-gray-800 text-base md:text-lg leading-relaxed",dangerouslySetInnerHTML:{__html:s.description}})}),(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-6 md:mb-8",children:[(0,r.jsx)("h3",{className:"text-gray-900 text-lg md:text-xl font-bold mb-3 md:mb-4",children:"Keywords"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s.keywords.slice(0,j?void 0:5).map((e,t)=>(0,r.jsx)("span",{className:"bg-gray-200 text-gray-700 px-2 py-1 md:px-3 rounded-full text-xs md:text-sm",children:e.replace(/[\[\]"]/g,"")},t)),s.keywords.length>5&&(0,r.jsx)("button",{onClick:()=>w(!j),className:"text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors",children:j?"Show Less":"More"})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-8",children:[(0,r.jsx)("h3",{className:"text-gray-900 text-lg md:text-xl font-bold mb-3 md:mb-4",children:"Share this article"}),(0,r.jsxs)("button",{onClick:()=>P(!0),className:"flex items-center gap-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 md:px-6 md:py-3 rounded-lg transition-colors text-sm md:text-base",children:[(0,r.jsx)(o.Pum,{className:"w-4 h-4 md:w-5 md:h-5"}),(0,r.jsx)("span",{children:"Share Article"})]})]}),(0,r.jsx)(v,{blogId:Array.isArray(e.id)?e.id[0]:e.id||void 0,blogTitle:s?.title})]}),(0,r.jsx)("div",{className:"hidden lg:block lg:w-[350px]",children:(0,r.jsx)(x.A,{categories:p,recentBlogs:u,allKeywords:s?.keywords||[],searchQuery:N,selectedCategory:"",onSearchChange:A,onSearchSubmit:$,onCategoryFilter:D,onKeywordClick:B})})]}),(0,r.jsx)(f.A,{isOpen:S,onClose:()=>P(!1),blogId:s?._id||"",blogTitle:s?.title||"",blogUrl:""})]}):(0,r.jsx)("div",{className:"min-h-screen bg-white text-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-gray-900 text-xl mb-4",children:"Blog not found"}),(0,r.jsx)(m(),{href:"/blog",className:"text-orange-500 hover:text-orange-400",children:"Back to Blogs"})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,814,950,579,30,443,143],()=>s(58504));module.exports=r})();