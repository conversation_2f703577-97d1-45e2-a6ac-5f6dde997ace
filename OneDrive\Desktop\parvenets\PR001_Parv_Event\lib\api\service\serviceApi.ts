// Service API module
import { apiClient } from '../../customaxios';
import { buildUrl } from '../../globalurl';

// Types for service API
export interface Service {
  _id: string;
  id: string;
  title: string;
  description: string;
  description2?: string;
  shortDescription: string;
  slug: string;
  category: string;
  price: number;
  duration: string;
  features: string[];
  images: string[];
  image: string; // Primary image URL
  howWeDoIt?: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Gallery {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  category: string;
  tags: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface GalleryCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  imageCount: number;
}

export interface Review {
  _id: string;
  id: string;
  name: string;
  email: string;
  rating: number;
  comment: string;
  review: string; // Alias for comment
  relationship: string;
  serviceId?: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface GalleryQueryParams {
  page?: number;
  limit?: number;
  category?: string;
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ServicesResponse {
  success: boolean;
  message: string;
  data: {
    services: Service[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface ServiceResponse {
  success: boolean;
  message: string;
  data?: Service;
}

export interface GalleryResponse {
  gallery: Gallery[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ReviewsResponse {
  success: boolean;
  message: string;
  data: {
    reviews: Review[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Service API functions
export const serviceApi = {
  // Get all services with pagination and filters
  getServices: async (params: ServiceQueryParams = {}): Promise<ServicesResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<ServicesResponse>(
      buildUrl(`/services?${queryParams.toString()}`)
    );
    return response.data;
  },

  // Get single service by ID or slug
  getServiceById: async (id: string): Promise<ServiceResponse> => {
    const response = await apiClient.get<ServiceResponse>(
      buildUrl(`/services/${id}`)
    );
    return response.data;
  },

  // Get service by slug
  getServiceBySlug: async (slug: string): Promise<ServiceResponse> => {
    const response = await apiClient.get<ServiceResponse>(
      buildUrl(`/services/slug/${slug}`)
    );
    return response.data;
  },

  // Get service categories
  getServiceCategories: async (): Promise<string[]> => {
    const response = await apiClient.get<{ categories: string[] }>(
      buildUrl('/services/categories')
    );
    return response.data.categories;
  },
};

// Gallery API functions
export const galleryApi = {
  // Get all gallery items with pagination and filters
  getGallery: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => queryParams.append(key, v.toString()));
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    const response = await apiClient.get<GalleryResponse>(
      buildUrl(`/gallery?${queryParams.toString()}`)
    );
    return response.data;
  },

  // Get gallery categories
  getGalleryCategories: async (): Promise<GalleryCategory[]> => {
    const response = await apiClient.get<{ categories: GalleryCategory[] }>(
      buildUrl('/gallery/categories')
    );
    return response.data.categories;
  },

  // Get gallery by category
  getGalleryByCategory: async (category: string, params: GalleryQueryParams = {}): Promise<GalleryResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<GalleryResponse>(
      buildUrl(`/gallery/category/${category}?${queryParams.toString()}`)
    );
    return response.data;
  },
};

// Reviews API functions
export const reviewsApi = {
  // Get all reviews with pagination
  getReviews: async (params: { page?: number; limit?: number; serviceId?: string; sortBy?: string; sortOrder?: 'asc' | 'desc' } = {}): Promise<ReviewsResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<ReviewsResponse>(
      buildUrl(`/reviews?${queryParams.toString()}`)
    );
    return response.data;
  },

  // Get reviews for a specific service
  getServiceReviews: async (serviceId: string, params: { page?: number; limit?: number } = {}): Promise<ReviewsResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<ReviewsResponse>(
      buildUrl(`/reviews/service/${serviceId}?${queryParams.toString()}`)
    );
    return response.data;
  },
};

// Helper functions
export const serviceHelpers = {
  // Format price for display
  formatPrice: (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  },

  // Create slug from title
  createSlug: (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  },

  // Truncate text
  truncateText: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  },

  // Format rating for display
  formatRating: (rating: number): string => {
    return rating.toFixed(1);
  },

  // Get star rating array for display
  getStarRating: (rating: number): { filled: number; half: boolean; empty: number } => {
    const filled = Math.floor(rating);
    const half = rating % 1 >= 0.5;
    const empty = 5 - filled - (half ? 1 : 0);

    return { filled, half, empty };
  },

  // Format relationship for display
  formatRelationship: (relationship: string): string => {
    return relationship
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  },
};

// Export individual functions for easier imports
export const getServices = serviceApi.getServices;
export const getServiceById = serviceApi.getServiceById;
export const getServiceBySlug = serviceApi.getServiceBySlug;
export const getServiceCategories = serviceApi.getServiceCategories;
export const getGallery = galleryApi.getGallery;
export const getGalleryCategories = galleryApi.getGalleryCategories;
export const getGalleryByCategory = galleryApi.getGalleryByCategory;
export const getReviews = reviewsApi.getReviews;
export const getServiceReviews = reviewsApi.getServiceReviews;
