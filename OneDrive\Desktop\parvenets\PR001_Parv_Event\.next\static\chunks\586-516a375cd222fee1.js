"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[586],{4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4436:(e,t,n)=>{n.d(t,{k5:()=>s});var r=n(2115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=r.createContext&&r.createContext(o),i=["attr","size","title"];function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){var r,o,a;r=e,o=t,a=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in r?Object.defineProperty(r,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e){return t=>r.createElement(d,u({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,c({key:n},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var n,{attr:o,size:a,title:l}=e,s=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,i),d=a||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",u({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,s,{className:n,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),e.children)};return void 0!==a?r.createElement(a.Consumer,null,e=>t(e)):t(o)}},5695:(e,t,n)=>{var r=n(8999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},6756:(e,t,n)=>{n.d(t,{bm:()=>ta,UC:()=>tr,hJ:()=>tn,ZL:()=>tt,bL:()=>te,hE:()=>to});var r,o,a,i=n(2115),u=n.t(i,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var c=n(6101),s=n(5155),d=globalThis?.document?i.useLayoutEffect:()=>{},f=u[" useId ".trim().toString()]||(()=>void 0),p=0;function v(e){let[t,n]=i.useState(f());return d(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var m=u[" useInsertionEffect ".trim().toString()]||d,h=(Symbol("RADIX:SYNC_STATE"),n(7650)),g=n(9708),y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,g.TL)(`Primitive.${t}`),r=i.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o?n:t,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function b(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var E="dismissableLayer.update",w=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),O=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,h=i.useContext(w),[g,O]=i.useState(null),P=null!=(r=null==g?void 0:g.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,R]=i.useState({}),S=(0,c.s)(t,e=>O(e)),x=Array.from(h.layers),[D]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),j=x.indexOf(D),T=g?x.indexOf(g):-1,L=h.layersWithOutsidePointerEventsDisabled.size>0,M=T>=j,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=b(e),o=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){C("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));M&&!n&&(null==d||d(e),null==p||p(e),e.defaultPrevented||null==v||v())},P),A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=b(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&C("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},P);return!function(e,t=globalThis?.document){let n=b(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===h.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},P),i.useEffect(()=>{if(g)return a&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(o=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(g)),h.layers.add(g),N(),()=>{a&&1===h.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=o)}},[g,P,a,h]),i.useEffect(()=>()=>{g&&(h.layers.delete(g),h.layersWithOutsidePointerEventsDisabled.delete(g),N())},[g,h]),i.useEffect(()=>{let e=()=>R({});return document.addEventListener(E,e),()=>document.removeEventListener(E,e)},[]),(0,s.jsx)(y.div,{...m,ref:S,style:{pointerEvents:L?M?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,A.onFocusCapture),onBlurCapture:l(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,k.onPointerDownCapture)})});function N(){let e=new CustomEvent(E);document.dispatchEvent(e)}function C(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&a.addEventListener(e,t,{once:!0}),o)a&&h.flushSync(()=>a.dispatchEvent(i));else a.dispatchEvent(i)}O.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(w),r=i.useRef(null),o=(0,c.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(y.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var P="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},x=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...u}=e,[l,d]=i.useState(null),f=b(o),p=b(a),v=i.useRef(null),m=(0,c.s)(t,e=>d(e)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(h.paused||!l)return;let t=e.target;l.contains(t)?v.current=t:T(v.current,{select:!0})},t=function(e){if(h.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||T(v.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&T(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,h.paused]),i.useEffect(()=>{if(l){L.add(h);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(P,S);l.addEventListener(P,f),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(T(r,{select:t}),document.activeElement!==n)return}(D(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&T(l))}return()=>{l.removeEventListener(P,f),setTimeout(()=>{let t=new CustomEvent(R,S);l.addEventListener(R,p),l.dispatchEvent(t),t.defaultPrevented||T(null!=e?e:document.body,{select:!0}),l.removeEventListener(R,p),L.remove(h)},0)}}},[l,f,p,h]);let g=i.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=D(e);return[j(t,e),j(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&T(a,{select:!0})):(e.preventDefault(),n&&T(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,s.jsx)(y.div,{tabIndex:-1,...u,ref:m,onKeyDown:g})});function D(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function j(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function T(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}x.displayName="FocusScope";var L=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=M(e,t)).unshift(t)},remove(t){var n;null==(n=(e=M(e,t))[0])||n.resume()}}}();function M(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var k=i.forwardRef((e,t)=>{var n,r;let{container:o,...a}=e,[u,l]=i.useState(!1);d(()=>l(!0),[]);let c=o||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?h.createPortal((0,s.jsx)(y.div,{...a,ref:t}),c):null});k.displayName="Portal";var A=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=i.useState(),a=i.useRef(null),u=i.useRef(e),l=i.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return i.useEffect(()=>{let e=I(a.current);l.current="mounted"===c?e:"none"},[c]),d(()=>{let t=a.current,n=u.current;if(n!==e){let r=l.current,o=I(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),d(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=I(a.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!u.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(l.current=I(a.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(e=>{a.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):i.Children.only(n),a=(0,c.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?i.cloneElement(o,{ref:a}):null};function I(e){return(null==e?void 0:e.animationName)||"none"}A.displayName="Presence";var F=0;function _(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var W=function(){return(W=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function U(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var B=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),$="width-before-scroll-bar";function K(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var z="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,X=new WeakMap;function Y(e){return e}var Z=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=Y),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return a.options=W({async:!0,ssr:!1},e),a}(),H=function(){},q=i.forwardRef(function(e,t){var n,r,o,a,u=i.useRef(null),l=i.useState({onScrollCapture:H,onWheelCapture:H,onTouchMoveCapture:H}),c=l[0],s=l[1],d=e.forwardProps,f=e.children,p=e.className,v=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,b=e.noIsolation,E=e.inert,w=e.allowPinchZoom,O=e.as,N=e.gapMode,C=U(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[u,t],r=function(e){return n.forEach(function(t){return K(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,z(function(){var e=X.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||K(e,null)}),r.forEach(function(e){t.has(e)||K(e,o)})}X.set(a,n)},[n]),a),R=W(W({},C),c);return i.createElement(i.Fragment,null,m&&i.createElement(g,{sideCar:Z,removeScrollBar:v,shards:h,noRelative:y,noIsolation:b,inert:E,setCallbacks:s,allowPinchZoom:!!w,lockRef:u,gapMode:N}),d?i.cloneElement(i.Children.only(f),W(W({},R),{ref:P})):i.createElement(void 0===O?"div":O,W({},R,{className:p,ref:P}),f))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:$,zeroRight:B};var V=function(e){var t=e.sideCar,n=U(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,W({},n))};V.isSideCarExport=!0;var G=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},J=function(){var e=G();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Q=function(){var e=J();return function(t){return e(t.styles,t.dynamic),null}},ee={left:0,top:0,right:0,gap:0},et=function(e){return parseInt(e||"",10)||0},en=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[et(n),et(r),et(o)]},er=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ee;var t=en(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},eo=Q(),ea="data-scroll-locked",ei=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(ea,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(B," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat($," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(B," .").concat(B," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat($," .").concat($," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ea,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},eu=function(){var e=parseInt(document.body.getAttribute(ea)||"0",10);return isFinite(e)?e:0},el=function(){i.useEffect(function(){return document.body.setAttribute(ea,(eu()+1).toString()),function(){var e=eu()-1;e<=0?document.body.removeAttribute(ea):document.body.setAttribute(ea,e.toString())}},[])},ec=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;el();var a=i.useMemo(function(){return er(o)},[o]);return i.createElement(eo,{styles:ei(a,!t,o,n?"":"!important")})},es=!1;if("undefined"!=typeof window)try{var ed=Object.defineProperty({},"passive",{get:function(){return es=!0,!0}});window.addEventListener("test",ed,ed),window.removeEventListener("test",ed,ed)}catch(e){es=!1}var ef=!!es&&{passive:!1},ep=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ev=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),em(e,r)){var o=eh(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},em=function(e,t){return"v"===e?ep(t,"overflowY"):ep(t,"overflowX")},eh=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eg=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,p=0;do{if(!l)break;var v=eh(e,l),m=v[0],h=v[1]-v[2]-i*m;(m||h)&&em(e,l)&&(f+=h,p+=m);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-u>p)&&(s=!0),s},ey=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eb=function(e){return[e.deltaX,e.deltaY]},eE=function(e){return e&&"current"in e?e.current:e},ew=0,eO=[];let eN=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(ew++)[0],a=i.useState(Q)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eE),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=ey(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ev(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ev(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return eg(p,t,e,"h"===p?l:c,!0)},[]),c=i.useCallback(function(e){if(eO.length&&eO[eO.length-1]===a){var n="deltaY"in e?eb(e):ey(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(eE).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=ey(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,eb(t),t.target,l(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,ey(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return eO.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ef),document.addEventListener("touchmove",c,ef),document.addEventListener("touchstart",d,ef),function(){eO=eO.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,ef),document.removeEventListener("touchmove",c,ef),document.removeEventListener("touchstart",d,ef)}},[]);var v=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?i.createElement(ec,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},Z.useMedium(r),V);var eC=i.forwardRef(function(e,t){return i.createElement(q,W({},e,{ref:t,sideCar:eN}))});eC.classNames=q.classNames;var eP=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eR=new WeakMap,eS=new WeakMap,ex={},eD=0,ej=function(e){return e&&(e.host||ej(e.parentNode))},eT=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=ej(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});ex[n]||(ex[n]=new WeakMap);var a=ex[n],i=[],u=new Set,l=new Set(o),c=function(e){!e||u.has(e)||(u.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(eR.get(e)||0)+1,c=(a.get(e)||0)+1;eR.set(e,l),a.set(e,c),i.push(e),1===l&&o&&eS.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),u.clear(),eD++,function(){i.forEach(function(e){var t=eR.get(e)-1,o=a.get(e)-1;eR.set(e,t),a.set(e,o),t||(eS.has(e)||e.removeAttribute(r),eS.delete(e)),o||e.removeAttribute(n)}),--eD||(eR=new WeakMap,eR=new WeakMap,eS=new WeakMap,ex={})}},eL=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||eP(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),eT(r,o,n,"aria-hidden")):function(){return null}},eM="Dialog",[ek,eA]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>i.createContext(e));return function(n){let r=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=i.createContext(r),a=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,l=n?.[e]?.[a]||o,c=i.useMemo(()=>u,Object.values(u));return(0,s.jsx)(l.Provider,{value:c,children:r})};return u.displayName=t+"Provider",[u,function(n,u){let l=u?.[e]?.[a]||o,c=i.useContext(l);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eM),[eI,eF]=ek(eM),e_=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,l=i.useRef(null),c=i.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return m(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:t,onChange:n}),l=void 0!==e,c=l?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[c,i.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[l,e,a,u])]}({prop:r,defaultProp:null!=o&&o,onChange:a,caller:eM});return(0,s.jsx)(eI,{scope:t,triggerRef:l,contentRef:c,contentId:v(),titleId:v(),descriptionId:v(),open:d,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};e_.displayName=eM;var eW="DialogTrigger";i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eF(eW,n),a=(0,c.s)(t,o.triggerRef);return(0,s.jsx)(y.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e5(o.open),...r,ref:a,onClick:l(e.onClick,o.onOpenToggle)})}).displayName=eW;var eU="DialogPortal",[eB,e$]=ek(eU,{forceMount:void 0}),eK=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=eF(eU,t);return(0,s.jsx)(eB,{scope:t,forceMount:n,children:i.Children.map(r,e=>(0,s.jsx)(A,{present:n||a.open,children:(0,s.jsx)(k,{asChild:!0,container:o,children:e})}))})};eK.displayName=eU;var ez="DialogOverlay",eX=i.forwardRef((e,t)=>{let n=e$(ez,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eF(ez,e.__scopeDialog);return a.modal?(0,s.jsx)(A,{present:r||a.open,children:(0,s.jsx)(eZ,{...o,ref:t})}):null});eX.displayName=ez;var eY=(0,g.TL)("DialogOverlay.RemoveScroll"),eZ=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eF(ez,n);return(0,s.jsx)(eC,{as:eY,allowPinchZoom:!0,shards:[o.contentRef],children:(0,s.jsx)(y.div,{"data-state":e5(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eH="DialogContent",eq=i.forwardRef((e,t)=>{let n=e$(eH,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eF(eH,e.__scopeDialog);return(0,s.jsx)(A,{present:r||a.open,children:a.modal?(0,s.jsx)(eV,{...o,ref:t}):(0,s.jsx)(eG,{...o,ref:t})})});eq.displayName=eH;var eV=i.forwardRef((e,t)=>{let n=eF(eH,e.__scopeDialog),r=i.useRef(null),o=(0,c.s)(t,n.contentRef,r);return i.useEffect(()=>{let e=r.current;if(e)return eL(e)},[]),(0,s.jsx)(eJ,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),eG=i.forwardRef((e,t)=>{let n=eF(eH,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return(0,s.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(r.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let u=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eJ=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,l=eF(eH,n),d=i.useRef(null),f=(0,c.s)(t,d);return i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:_()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:_()),F++,()=>{1===F&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),F--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,s.jsx)(O,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e5(l.open),...u,ref:f,onDismiss:()=>l.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(e3,{titleId:l.titleId}),(0,s.jsx)(e7,{contentRef:d,descriptionId:l.descriptionId})]})]})}),eQ="DialogTitle",e0=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eF(eQ,n);return(0,s.jsx)(y.h2,{id:o.titleId,...r,ref:t})});e0.displayName=eQ;var e1="DialogDescription";i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eF(e1,n);return(0,s.jsx)(y.p,{id:o.descriptionId,...r,ref:t})}).displayName=e1;var e2="DialogClose",e6=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eF(e2,n);return(0,s.jsx)(y.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})});function e5(e){return e?"open":"closed"}e6.displayName=e2;var e8="DialogTitleWarning",[e9,e4]=function(e,t){let n=i.createContext(t),r=e=>{let{children:t,...r}=e,o=i.useMemo(()=>r,Object.values(r));return(0,s.jsx)(n.Provider,{value:o,children:t})};return r.displayName=e+"Provider",[r,function(r){let o=i.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(e8,{contentName:eH,titleName:eQ,docsSlug:"dialog"}),e3=e=>{let{titleId:t}=e,n=e4(e8),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},e7=e=>{let{contentRef:t,descriptionId:n}=e,r=e4("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return i.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},te=e_,tt=eK,tn=eX,tr=eq,to=e0,ta=e6}}]);