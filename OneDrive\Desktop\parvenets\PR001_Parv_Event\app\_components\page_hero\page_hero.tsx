import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import cherryBlossom from '@/public/image/assests/pinktree.png'; // Make sure this image exists

interface PageHeroProps {
  title: string;
  breadcrumbs: {
    label: string;
    href: string;
  }[];
}

const PageHero = ({ title, breadcrumbs }: PageHeroProps) => {
  return (
    <div className="relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4">
      <div className="max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center">
        {/* Left side - Page Title */}
        <h1 className="text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0">
          {title}
        </h1>
        
        {/* Center - Cherry Blossom Image */}
        <div className="absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10">
          <Image 
            src={cherryBlossom} 
            alt="Cherry Blossom" 
            width={130} 
            height={100} 
            style={{ width: 'auto', height: 'auto' }}
            className="object-contain"
          />
        </div>
        
        {/* Right side - Breadcrumbs */}
        <div className="flex items-center space-x-2 text-sm z-20">
          {breadcrumbs.map((crumb, index) => (
            <React.Fragment key={index}>
              <Link 
                href={crumb.href} 
                className="hover:text-[#FE904B] transition-colors"
              >
                {crumb.label}
              </Link>
              
              {index < breadcrumbs.length - 1 && (
                <span className="text-gray-400">›</span>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PageHero;

