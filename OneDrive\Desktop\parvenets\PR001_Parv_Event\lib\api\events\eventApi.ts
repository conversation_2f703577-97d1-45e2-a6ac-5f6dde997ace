// Events API module
import { apiClient } from '../../customaxios';
import { buildApiUrl } from '../../globalurl';

// Types for events API
export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  location: string;
  organizer: string;
  attendees: number;
  maxAttendees: number;
  price: number;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateEventRequest {
  title: string;
  description: string;
  date: string;
  location: string;
  maxAttendees: number;
  price: number;
  image?: string;
}

export interface UpdateEventRequest extends Partial<CreateEventRequest> {
  id: string;
}

export interface EventsResponse {
  events: Event[];
  total: number;
  page: number;
  limit: number;
}

// Events API functions
export const eventApi = {
  // Get all events with pagination
  getEvents: async (page = 1, limit = 10): Promise<EventsResponse> => {
    const response = await apiClient.get<EventsResponse>(
      buildApiUrl(`/events?page=${page}&limit=${limit}`)
    );
    return response.data;
  },

  // Get single event by ID
  getEventById: async (id: string): Promise<Event> => {
    const response = await apiClient.get<Event>(
      buildApiUrl(`/events/${id}`)
    );
    return response.data;
  },

  // Create new event
  createEvent: async (eventData: CreateEventRequest): Promise<Event> => {
    const response = await apiClient.post<Event>(
      buildApiUrl('/events'),
      eventData
    );
    return response.data;
  },

  // Update event
  updateEvent: async (eventData: UpdateEventRequest): Promise<Event> => {
    const { id, ...updateData } = eventData;
    const response = await apiClient.put<Event>(
      buildApiUrl(`/events/${id}`),
      updateData
    );
    return response.data;
  },

  // Delete event
  deleteEvent: async (id: string): Promise<void> => {
    await apiClient.delete(buildApiUrl(`/events/${id}`));
  },

  // Join event
  joinEvent: async (eventId: string): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>(
      buildApiUrl(`/events/${eventId}/join`)
    );
    return response.data;
  },

  // Leave event 
  leaveEvent: async (eventId: string): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>(
      buildApiUrl(`/events/${eventId}/leave`)
    );
    return response.data;
  },

  // Get user's events
  getUserEvents: async (): Promise<Event[]> => {
    const response = await apiClient.get<Event[]>(
      buildApiUrl('/events/my-events')
    );
    return response.data;
  },
};
