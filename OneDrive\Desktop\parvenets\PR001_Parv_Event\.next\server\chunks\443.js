exports.id=443,exports.ids=[443],exports.modules={684:(e,t,s)=>{"use strict";s.d(t,{JW:()=>r,KB:()=>o,c$:()=>a});let r={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},a=e=>{let t=r.BASE_URL,s=e.startsWith("/")?e:`/${e}`;return`${t}/api${s}`},o=a},19431:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},24478:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var r=s(60687),a=s(30474),o=s(43210),n=s(34447),l=s(69556),i=s(69587),c=s(33793),d=s(85814),m=s.n(d),u=s(16189),h=s(41474),x=s(93853);let p=({isOpen:e,onClose:t,onSubmit:s})=>{let[a,n]=(0,o.useState)({name:"",email:"",countryCode:"+91",phoneNumber:"",service:"Quote Request",message:""}),[i,c]=(0,o.useState)({}),[d,m]=(0,o.useState)(!1),u=e=>{let{name:t,value:s}=e.target;n(e=>({...e,[t]:s})),i[t]&&c(e=>({...e,[t]:""}))},p=async e=>{e.preventDefault();let s=(0,h.h)(a);if(!s.isValid)return void c(s.errors);m(!0),c({});try{let e=await h.T.submitQuote(a);e.success?(x.oR.success("\uD83C\uDF89 Thank you for your quote request! We'll get back to you soon.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0}),n({name:"",email:"",countryCode:"+91",phoneNumber:"",service:"Quote Request",message:""}),setTimeout(()=>{t()},1500)):x.oR.error(e.message||"Failed to submit quote request. Please try again.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0})}catch(e){console.error("Quote form error:",e),e.errors?(c(e.errors),x.oR.error("Please fix the errors below and try again.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0})):x.oR.error(e.message||"Failed to submit quote request. Please try again.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0})}finally{m(!1)}};return e?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 overflow-y-auto",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-lg relative my-4 max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("button",{onClick:()=>{n({name:"",email:"",countryCode:"+91",phoneNumber:"",service:"Quote Request",message:""}),c({}),t()},className:"absolute right-4 top-4 text-gray-500 hover:text-gray-700 z-10","aria-label":"Close modal",children:(0,r.jsx)(l.WQq,{size:24})}),(0,r.jsxs)("div",{className:"p-4 sm:p-6 border-b",children:[(0,r.jsx)("h3",{className:"text-xl sm:text-2xl font-medium text-gray-900",children:"Request a Quote"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Fill out the form below and our team will get back to you shortly."})]}),(0,r.jsxs)("form",{onSubmit:p,className:"p-4 sm:p-6",children:[(0,r.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,r.jsxs)("div",{className:"w-full sm:w-1/2",children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:a.name,onChange:u,required:!0,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${i.name?"border-red-500":"border-gray-300"}`,placeholder:"Your full name"}),i.name&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.name})]}),(0,r.jsxs)("div",{className:"w-full sm:w-1/2",children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:a.email,onChange:u,required:!0,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${i.email?"border-red-500":"border-gray-300"}`,placeholder:"<EMAIL>"}),i.email&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.email})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,r.jsxs)("div",{className:"w-full sm:w-1/3",children:[(0,r.jsx)("label",{htmlFor:"countryCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Country Code *"}),(0,r.jsxs)("select",{id:"countryCode",name:"countryCode",value:a.countryCode,onChange:u,required:!0,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${i.countryCode?"border-red-500":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"+91",children:"+91 (India)"}),(0,r.jsx)("option",{value:"+1",children:"+1 (US/Canada)"}),(0,r.jsx)("option",{value:"+44",children:"+44 (UK)"}),(0,r.jsx)("option",{value:"+61",children:"+61 (Australia)"}),(0,r.jsx)("option",{value:"+971",children:"+971 (UAE)"}),(0,r.jsx)("option",{value:"+65",children:"+65 (Singapore)"})]}),i.countryCode&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.countryCode})]}),(0,r.jsxs)("div",{className:"w-full sm:w-2/3",children:[(0,r.jsx)("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number *"}),(0,r.jsx)("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:a.phoneNumber,onChange:u,required:!0,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${i.phoneNumber?"border-red-500":"border-gray-300"}`,placeholder:"9876543210"}),i.phoneNumber&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.phoneNumber})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-1",children:"Service Required *"}),(0,r.jsx)("input",{type:"text",id:"service",name:"service",value:a.service,onChange:u,required:!0,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${i.service?"border-red-500":"border-gray-300"}`,placeholder:"e.g., Wedding Planning, Event Management"}),i.service&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.service})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Event Details *"}),(0,r.jsx)("textarea",{id:"message",name:"message",value:a.message,onChange:u,rows:3,required:!0,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] focus:border-transparent ${i.message?"border-red-500":"border-gray-300"}`,placeholder:"Tell us more about your event requirements..."}),i.message&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:i.message}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[a.message.length,"/1000 characters"]})]})]}),(0,r.jsx)("div",{className:"mt-4 sm:mt-6",children:(0,r.jsx)("button",{type:"submit",disabled:d,className:`w-full py-2 px-4 rounded-md transition-colors duration-300 ${d?"bg-gray-400 cursor-not-allowed":"bg-[#FE904B] hover:bg-[#e87f3d] text-white"}`,children:d?"Submitting...":"Submit Quote Request"})})]})]})}):null},g=()=>{let e=(0,u.usePathname)(),[t,s]=(0,o.useState)(!1),[d,h]=(0,o.useState)(!1),[x,g]=(0,o.useState)(!1),[b,v]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=()=>{g(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,o.useEffect)(()=>{let e=()=>{window.scrollY>10?v(!0):v(!1)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]);let f=[{name:"Home",path:"/"},{name:"About Us",path:"/about"},{name:"Services",path:"/services"},{name:"Venue",path:"/venue"},{name:"Gallary",path:"/gallary"},{name:"Blog",path:"/blog"},{name:"Contact",path:"/contact"}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"h-[76px]"}),(0,r.jsxs)("header",{className:`w-full border-b border-gray-200 bg-white shadow-sm fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${b?"shadow-md":""}`,children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-3 flex justify-between items-center z-50",children:[(0,r.jsx)(m(),{href:"/",children:(0,r.jsx)(a.default,{src:n.A,alt:"logo",width:60,height:60,className:"cursor-pointer"})}),(0,r.jsx)("nav",{className:"hidden lg:flex gap-6 items-center",children:(0,r.jsx)("ul",{className:"flex gap-6 font-urbanist text-sm tracking-wider",children:f.map((t,s)=>(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:t.path,className:`uppercase transition-colors duration-200 hover:text-[#FE904B] ${e===t.path?"text-[#FE904B] font-semibold":"text-[#0D0D0D]"}`,children:t.name})},s))})}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center gap-5",children:[(0,r.jsxs)("div",{className:"flex gap-3 text-lg",children:[(0,r.jsx)(c.RvV,{className:"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200"}),(0,r.jsx)(i.ao$,{className:"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200"}),(0,r.jsx)(i.Vk6,{className:"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200"})]}),(0,r.jsxs)("button",{onClick:()=>h(!0),className:"flex items-center py-2 px-4 border border-transparent hover:border-[#FE904B] hover:text-[#FE904B] text-sm transition",children:["GET A QUOTE ",(0,r.jsx)(l.t50,{className:"ml-1"})]})]}),(0,r.jsx)("button",{className:"lg:hidden text-2xl text-[#0D0D0D]",onClick:()=>s(!t),"aria-label":"Toggle Menu",children:t?(0,r.jsx)(l.WQq,{}):(0,r.jsx)(l.nej,{})})]}),(0,r.jsxs)("div",{className:`fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${t?"translate-x-0":"translate-x-full"} lg:hidden`,children:[(0,r.jsxs)("div",{className:"p-4 border-b flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Menu"}),(0,r.jsx)("button",{onClick:()=>s(!1),className:"text-2xl text-[#0D0D0D]","aria-label":"Close menu",children:(0,r.jsx)(l.WQq,{})})]}),(0,r.jsx)("ul",{className:"flex flex-col gap-4 p-4 text-sm font-urbanist",children:f.map((t,a)=>(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:t.path,className:`block py-2 uppercase transition-colors duration-200 hover:text-[#FE904B] ${e===t.path?"text-[#FE904B] font-semibold":"text-[#0D0D0D]"}`,onClick:()=>s(!1),children:t.name})},a))}),(0,r.jsxs)("div",{className:"p-4 border-t",children:[(0,r.jsxs)("div",{className:"flex gap-3 text-lg mb-4 justify-center",children:[(0,r.jsx)(c.RvV,{className:"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]"}),(0,r.jsx)(i.ao$,{className:"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]"}),(0,r.jsx)(i.Vk6,{className:"cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]"})]}),(0,r.jsxs)("button",{onClick:()=>{h(!0),s(!1)},className:"w-full flex items-center justify-center py-2 px-4 border border-[#FE904B] text-[#FE904B] hover:bg-[#FE904B] hover:text-white text-sm transition",children:["GET A QUOTE ",(0,r.jsx)(l.t50,{className:"ml-1"})]})]})]}),t&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>s(!1)})]}),(0,r.jsx)(p,{isOpen:d,onClose:()=>h(!1)})]})}},30812:(e,t,s)=>{Promise.resolve().then(s.bind(s,93853))},34115:(e,t,s)=>{"use strict";s.d(t,{u:()=>n});var r=s(51060),a=s(684);let o=r.A.create({timeout:a.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});o.interceptors.request.use(e=>e,e=>(console.error("❌ Request Error:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(e.response){let{status:t,data:s}=e.response;switch(t){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",s)}}else e.request?(console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:e.config?.url,method:e.config?.method,baseURL:e.config?.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."):console.error("❌ Error:",e.message);return Promise.reject(e)});let n={get:(e,t)=>o.get(e,t),post:(e,t,s)=>o.post(e,t,s),put:(e,t,s)=>o.put(e,t,s),patch:(e,t,s)=>o.patch(e,t,s),delete:(e,t)=>o.delete(e,t)}},34447:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r={src:"/_next/static/media/logo.83d6875e.png",height:150,width:150,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXH5jkr8kk5FWZf/kUr9kUzahFr7kEz6j0v+mU/vjE+QcHL+k0yhs7nHAAAADXRSTlMAHywWjZoYRG99dEFbGESd4QAAAAlwSFlzAAAbrwAAG68BXhqRHAAAADVJREFUeJwtylkOACAIxcDyQHG7/3kNxr8mU1CcZgDWOPNHjhehUJlv3+6Alq1e1E3vrWskXBsSALileIstAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8}},41474:(e,t,s)=>{"use strict";s.d(t,{T:()=>o,h:()=>n});var r=s(34115),a=s(684);let o={submitContact:async e=>{try{return(await r.u.post((0,a.c$)("/contacts"),e)).data}catch(e){if(e.response?.data)throw e.response.data;throw{success:!1,message:"Failed to submit contact form. Please try again."}}},submitQuote:async e=>{try{return(await r.u.post((0,a.c$)("/contacts"),{...e,service:e.service||"Quote Request"})).data}catch(e){if(e.response?.data)throw e.response.data;throw{success:!1,message:"Failed to submit quote request. Please try again."}}}},n=e=>{let t={};if(e.name?.trim()?e.name.length>100&&(t.name="Name cannot exceed 100 characters"):t.name="Name is required",e.email?.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(t.email="Invalid email format"):t.email="Email is required",e.countryCode?.trim()||(t.countryCode="Country code is required"),e.phoneNumber?.trim()){let s=e.phoneNumber.replace(/\s|-/g,"");/^[\d\s\-]{8,15}$/.test(s)||(t.phoneNumber="Invalid phone number format (8-15 digits)")}else t.phoneNumber="Phone number is required";return e.service?.trim()||(t.service="Service is required"),e.message?.trim()?e.message.length>1e3&&(t.message="Message cannot exceed 1000 characters"):t.message="Message is required",{isValid:0===Object.keys(t).length,errors:t}}},44380:(e,t,s)=>{Promise.resolve().then(s.bind(s,81819))},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},47837:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\_components\\\\footer\\\\footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\_components\\footer\\footer.tsx","default")},48754:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413);s(82704);var a=s(47837),o=s(88631);function n({children:e}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.default,{}),e,(0,r.jsx)(a.default,{})]})}},54730:(e,t,s)=>{Promise.resolve().then(s.bind(s,47837)),Promise.resolve().then(s.bind(s,88631))},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>h});var r=s(37413),a=s(260),o=s.n(a),n=s(29815),l=s.n(n),i=s(73298),c=s.n(i),d=s(47535),m=s.n(d),u=s(81819);s(56070);let h={title:"Create Next App",description:"Generated by create next app"};function x({children:e}){return(0,r.jsxs)("html",{lang:"en",className:`${o().variable} ${c().variable} ${m().variable} ${l().variable}`,children:[(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,r.jsxs)("body",{className:"font-urbanist antialiased",suppressHydrationWarning:!0,children:[e,(0,r.jsx)(u.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontSize:"14px",fontFamily:"var(--font-urbanist)"}})]})]})}},82704:()=>{},84927:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},86259:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var r=s(60687);s(43210);var a=s(30474),o=s(85814),n=s.n(o),l=s(34447);let i={src:"/_next/static/media/downborderflower.0f3e204c.png",height:524,width:511,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAABlBMVEU0GSUoJCQ7JUSkAAAAAnRSTlMMAa0hsqIAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAaSURBVHicY2CEAAYGNAYDOoMBhQEiEAxGBgAHIQAoQfiJrAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8};var c=s(66232),d=s(19526),m=s(72575);let u=()=>(0,r.jsxs)("footer",{className:"relative bg-[#F6E4DB] py-12 px-4 sm:px-6 md:px-8 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none",children:(0,r.jsx)(a.default,{src:i,alt:"Decorative flower",className:"absolute top-0 right-0 w-auto h-full object-contain object-right-top",priority:!0})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto relative z-10",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(a.default,{src:l.A,alt:"Parv Events Logo",width:60,height:60}),(0,r.jsx)("p",{className:"text-sm text-gray-700 max-w-xs mt-4",children:"From intimate gatherings to grand celebrations, our experienced wedding planners ensure you create unforgettable memories. We handle every detail so you can enjoy every moment."}),(0,r.jsxs)("div",{className:"pt-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-700",children:"+91-9735284928"}),(0,r.jsx)("p",{className:"text-sm text-gray-700",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-2",children:[(0,r.jsx)(n(),{href:"#",className:"w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors",children:(0,r.jsx)(c.A,{size:18})}),(0,r.jsx)(n(),{href:"#",className:"w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors",children:(0,r.jsx)(d.A,{size:18})}),(0,r.jsx)(n(),{href:"#",className:"w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors",children:(0,r.jsx)(m.A,{size:18})})]})]}),(0,r.jsxs)("div",{className:"lg:ml-auto",children:[(0,r.jsx)("h3",{className:"text-base font-medium mb-4",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Venue"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Service"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Gallery"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base font-medium mb-4",children:"Service"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Full-Service Planning"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Partial Planning"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Day-of Coordination"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"#",className:"text-sm text-gray-700 hover:text-[#FE904B]",children:"Destination Weddings"})})]})]}),(0,r.jsx)("div",{className:"hidden lg:block"})]}),(0,r.jsxs)("div",{className:"md:w-[50%]  border-t border-gray-200 mt-10 pt-6 flex flex-col sm:flex-row justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex gap-4 mb-4 sm:mb-0",children:[(0,r.jsx)(n(),{href:"#",className:"text-xs text-gray-700 hover:text-[#FE904B]",children:"Privacy policy"}),(0,r.jsx)(n(),{href:"#",className:"text-xs text-gray-700 hover:text-[#FE904B]",children:"Terms and Condition"})]}),(0,r.jsx)("div",{className:"text-xs text-gray-700",children:"Copyright 2024"})]})]})]})},88631:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\_components\\\\navbar\\\\navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\_components\\navbar\\navbar.tsx","default")},94482:(e,t,s)=>{Promise.resolve().then(s.bind(s,86259)),Promise.resolve().then(s.bind(s,24478))}};