"use client";
import React, { useState, useEffect } from "react";
import PageH<PERSON> from "@/app/_components/page_hero/page_hero";
import Image from "next/image";
import Link from "next/link";
import { serviceApi, serviceHelpers, Service } from '@/lib/api/service/serviceApi';
import { toast } from 'react-toastify';
import servicerighttopflower from "@/public/image/icons/services/servicerighttopflower.png";

const ServicesPage = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const breadcrumbs = [
    { label: "HOME", href: "/" },
    { label: "SERVICES", href: "/services" }
  ];

  // Fetch services from API
  const fetchServices = async () => {
    try {
      setIsLoading(true);
      setError(null);



      const response = await serviceApi.getServices({
        page: 1,
        limit: 20,
        sortBy: 'sortOrder',
        sortOrder: 'asc'
      });

      if (response.success && response.data.services) {
        setServices(response.data.services);
        console.log('✅ Services loaded:', response.data.services.length, 'services');
      } else {
        throw new Error('Failed to fetch services');
      }
    } catch (err: any) {
      console.error('Error fetching services:', err);
      setError(err.message || 'Failed to load services');
      toast.error('Failed to load services. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  return (
    <>
      <PageHero title="SERVICES" breadcrumbs={breadcrumbs} />
      
      <div className="relative py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
        {/* Decorative flower image */}
        <div className="absolute top-10 right-0 -z-10 hidden lg:block opacity-30">
          <Image
            src={servicerighttopflower}
            alt="Decorative flower"
            width={300}
            height={250}
            className="object-contain"
          />
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12 sm:mb-16 lg:mb-20">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 font-urbanist uppercase text-gray-900 leading-tight">
              Our Professional Services
            </h2>
            <div className="w-20 sm:w-24 h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 mx-auto rounded-full mb-4 sm:mb-6"></div>
            
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] p-4 sm:p-6 animate-pulse">
                  <div className="flex justify-center mb-4 sm:mb-6">
                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-300 rounded-full"></div>
                  </div>
                  <div className="h-4 sm:h-5 bg-gray-300 rounded mb-3"></div>
                  <div className="h-3 sm:h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 sm:h-4 bg-gray-300 rounded mb-4 sm:mb-6"></div>
                  <div className="h-8 sm:h-10 bg-gray-300 rounded-lg"></div>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-lg font-medium">Failed to load services</p>
                <p className="text-sm text-gray-600 mt-2">{error}</p>
              </div>
              <button
                onClick={fetchServices}
                className="px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Services Grid */}
          {!isLoading && !error && (
            <>
              {services.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-500">
                    <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <p className="text-lg font-medium">No services available</p>
                    <p className="text-sm text-gray-600 mt-2">Please check back later</p>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {services.map((service) => (
                    <div
                      key={service._id}
                      className="bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] hover:shadow-lg transition-all duration-300 text-center p-4 sm:p-6 group hover:transform hover:-translate-y-1"
                    >
                      {/* Service Icon Image */}
                      <div className="flex justify-center mb-4 sm:mb-6">
                        <div className="w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center bg-orange-50 rounded-full group-hover:bg-orange-100 transition-colors duration-300 shadow-sm overflow-hidden">
                          {/* Backend Service Icon - PNG/JPG */}
                          <img
                            src={service.icons}
                            alt={service.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 rounded-full"
                            onLoad={() => {
                              console.log('✅ Backend service icon loaded:', service.title);
                            }}
                            onError={() => {
                              console.error('❌ Backend service icon failed:', service.title, service.icons);
                            }}
                          />
                        </div>
                      </div>

                      {/* Service Title */}
                      <h3 className="text-base sm:text-lg font-semibold uppercase mb-2 sm:mb-3 text-gray-800 group-hover:text-[#FE904D] transition-colors duration-300 leading-tight">
                        {serviceHelpers.formatCardTitle(service.title)}
                      </h3>

                      {/* Service Description */}
                      <p className="text-gray-600 text-xs sm:text-sm mb-4 sm:mb-6 line-clamp-3 leading-relaxed px-2">
                        {serviceHelpers.formatCardDescription(service.description)}
                      </p>

                      {/* See More Button */}
                      <Link href={`/services/${service._id}`}>
                        <div className="inline-block border-2 border-[#FE904D] hover:bg-[#FE904D] hover:text-white text-[#FE904D] text-xs sm:text-sm font-medium uppercase tracking-wider py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-all duration-300 cursor-pointer group-hover:bg-[#FE904D] group-hover:text-white group-hover:shadow-md">
                          See More
                        </div>
                      </Link>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default ServicesPage;
