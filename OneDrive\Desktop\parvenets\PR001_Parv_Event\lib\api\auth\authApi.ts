// Authentication API module
import { apiClient } from '../../customaxios';
import { buildApiUrl } from '../../globalurl';

// Types for auth API
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
  };
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

// Auth API functions
export const authApi = {
  // Login user
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>(
      buildApiUrl('/auth/login'),
      credentials
    );
    return response.data;
  },

  // Register user
  register: async (userData: RegisterRequest): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>(
      buildApiUrl('/auth/register'),
      userData
    );
    return response.data;
  },

  // Logout user
  logout: async (): Promise<void> => {
    await apiClient.post(buildApiUrl('/auth/logout'));
    localStorage.removeItem('authToken');
  },

  // Get current user profile
  getProfile: async (): Promise<LoginResponse['user']> => {
    const response = await apiClient.get<LoginResponse['user']>(
      buildApiUrl('/auth/profile')
    );
    return response.data;
  },

  // Refresh token
  refreshToken: async (): Promise<{ token: string }> => {
    const response = await apiClient.post<{ token: string }>(
      buildApiUrl('/auth/refresh')
    );
    return response.data;
  },
};
