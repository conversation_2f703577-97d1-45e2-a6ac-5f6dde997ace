// Contact API module
import { apiClient } from '../../customaxios';
import { buildUrl } from '../../globalurl';

// Types for contact API matching backend schema
export interface ContactFormData {
  name: string;
  email: string;
  countryCode: string;
  phoneNumber: string;
  service: string;
  message: string;
}

export interface ContactResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    name: string;
    email: string;
    countryCode: string;
    phoneNumber: string;
    phone: string; // Virtual field from backend
    service: string;
    message: string;
    status: string;
    priority: string;
    createdAt: string;
    updatedAt: string;
  };
}

export interface ContactError {
  success: false;
  message: string;
  errors?: {
    [key: string]: string;
  };
}

// Contact API functions
export const contactApi = {
  // Submit contact form
  submitContact: async (contactData: ContactFormData): Promise<ContactResponse> => {
    try {
      const response = await apiClient.post<ContactResponse>(
        buildUrl('/contacts'),
        contactData
      );
      return response.data;
    } catch (error: any) {
      // Handle API errors
      if (error.response?.data) {
        throw error.response.data;
      }
      throw {
        success: false,
        message: 'Failed to submit contact form. Please try again.',
      };
    }
  },

  // Submit quote request (same endpoint, different service type)
  submitQuote: async (quoteData: ContactFormData): Promise<ContactResponse> => {
    try {
      const response = await apiClient.post<ContactResponse>(
        buildUrl('/contacts'),
        {
          ...quoteData,
          service: quoteData.service || 'Quote Request'
        }
      );
      return response.data;
    } catch (error: any) {
      // Handle API errors
      if (error.response?.data) {
        throw error.response.data;
      }
      throw {
        success: false,
        message: 'Failed to submit quote request. Please try again.',
      };
    }
  }
};

// Helper function for form validation
export const validateContactForm = (data: Partial<ContactFormData>): { isValid: boolean; errors: { [key: string]: string } } => {
  const errors: { [key: string]: string } = {};

  // Name validation
  if (!data.name?.trim()) {
    errors.name = 'Name is required';
  } else if (data.name.length > 100) {
    errors.name = 'Name cannot exceed 100 characters';
  }

  // Email validation
  if (!data.email?.trim()) {
    errors.email = 'Email is required';
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      errors.email = 'Invalid email format';
    }
  }

  // Country code validation
  if (!data.countryCode?.trim()) {
    errors.countryCode = 'Country code is required';
  }

  // Phone number validation
  if (!data.phoneNumber?.trim()) {
    errors.phoneNumber = 'Phone number is required';
  } else {
    const phoneRegex = /^[\d\s\-]{8,15}$/;
    const cleanPhone = data.phoneNumber.replace(/\s|-/g, '');
    if (!phoneRegex.test(cleanPhone)) {
      errors.phoneNumber = 'Invalid phone number format (8-15 digits)';
    }
  }

  // Service validation
  if (!data.service?.trim()) {
    errors.service = 'Service is required';
  }

  // Message validation
  if (!data.message?.trim()) {
    errors.message = 'Message is required';
  } else if (data.message.length > 1000) {
    errors.message = 'Message cannot exceed 1000 characters';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Export types for components
export type { ContactFormData, ContactResponse, ContactError };
