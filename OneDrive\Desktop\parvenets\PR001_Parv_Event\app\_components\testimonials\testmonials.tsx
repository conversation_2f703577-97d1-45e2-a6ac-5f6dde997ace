"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import arrowleft from "@/public/image/icons/arrowreviewleft.svg";
import arrowright from "@/public/image/icons/arrowreviewright.svg";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import queto from "@/public/image/icons/queto.svg";
import { reviewsApi, Review, serviceHelpers } from "@/lib/api/service/serviceApi";
import { toast } from "react-toastify";

/* ─────────── fallback data if <PERSON> fails ─────────── */
const fallbackTestimonials = [
  {
    id: 1,
    quote:
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form. There are many variations of passages of Lorem. There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.",
    name: "BROOKLYN SIMMONS",
    role: "Parents",
    avatar: "/image/assests/review/1.png",
    rating: 5,
  },
  {
    id: 2,
    quote:
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form. There are many variations of passages of Lorem.",
    name: "JESSICA PARKER",
    role: "Bride",
    avatar: "/image/assests/review/2.png",
    rating: 5,
  },
  {
    id: 3,
    quote:
      "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.",
    name: "MICHAEL JOHNSON",
    role: "Groom",
    avatar: "/image/assests/review/6.png",
    rating: 5,
  },
];

const Testimonials = () => {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [testimonials, setTestimonials] = useState<any[]>(fallbackTestimonials);
  const [isLoading, setIsLoading] = useState(true);

  /* ─────────── fetch reviews from backend ─────────── */
  const fetchReviews = async () => {
    try {
      setIsLoading(true);
      const res = await reviewsApi.getReviews({
        sortBy: "sortOrder",
        sortOrder: "asc",
        limit: 10,
      });

      if (res.success && res.data.reviews?.length) {
        const mapped = res.data.reviews.map((r: Review) => ({
          id: r._id,
          quote: r.review,
          name: r.name.toUpperCase(),
          role: serviceHelpers.formatRelationship(r.relationship),
          avatar: r.image,
          rating: r.star,
        }));
        setTestimonials(mapped);
      } else {
        setTestimonials(fallbackTestimonials);
      }
    } catch (err) {
      console.error("Failed to load reviews:", err);
      toast.error("Failed to load testimonials");
      setTestimonials(fallbackTestimonials);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, []);

  return (
    <div className="py-16 px-4 md:px-8 max-w-7xl mx-auto">
      {/* Heading */}
      <div className="mb-12 text-center">
        <h3 className="mb-2 flex items-center justify-center gap-4 text-[24px] uppercase tracking-wider text-gray-500">
          <span className="h-0 w-[6rem] border border-[#cacaca]" />
          Happy Clients
          <span className="h-0 w-[6rem] border border-[#cacaca]" />
        </h3>
        <h2 className="text-3xl font-normal md:text-4xl">
          LOVE SEALED WITH A KISS
        </h2>
        <h2 className="text-3xl font-normal md:text-4xl">FOREVER IN BLISS</h2>
      </div>

      {/* Carousel */}
      <div className="relative">
        <Carousel
          setApi={setApi}
          opts={{ loop: true, align: "center" }}
          className="w-full"
        >
          <CarouselContent>
            {testimonials.map((t) => (
              <CarouselItem key={t.id} className="flex justify-center">
                <div className="w-full max-w-[700px] rounded-lg bg-[#FEF2EB] p-6 md:p-10 text-center">
                  {/* Avatar */}
                  <div className="relative mx-auto mb-6 h-16 w-16">
                    {t.avatar && t.avatar.startsWith("http") ? (
                      <img
                        src={t.avatar}
                        alt={t.name}
                        className="h-16 w-16 rounded-full object-cover"
                      />
                    ) : t.avatar ? (
                      <Image
                        src={t.avatar}
                        alt={t.name}
                        width={64}
                        height={64}
                        className="rounded-full object-cover"
                      />
                    ) : (
                      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-[#FE904B] text-xl font-bold text-white">
                        {t.name.charAt(0)}
                      </div>
                    )}
                    <div className="absolute -bottom-2 -right-2 flex h-7 w-7 items-center justify-center rounded-full bg-[#FE904B] p-1 text-white">
                      <Image src={queto} alt="Quote" width={18} height={15} />
                    </div>
                  </div>

                  {/* Quote */}
                  <p className="mx-auto mb-6 text-base leading-relaxed text-gray-700 md:text-lg md:leading-loose break-words">
                    {t.quote}
                  </p>

                  {/* Rating */}
                  <div className="mb-3 flex justify-center">
                    {Array.from({ length: t.rating }).map((_, i) => (
                      <span key={i} className="text-xl text-yellow-400">
                        ★
                      </span>
                    ))}
                  </div>

                  {/* Name + Role */}
                  <h4 className="text-lg font-semibold uppercase text-gray-800">
                    {t.name}
                  </h4>
                  <p className="text-sm text-gray-500">{t.role}</p>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>


          {/* arrows – desktop */}
          <button
            onClick={() => api?.scrollPrev()}
            className="absolute left-0 top-[45%] -translate-x-1/2 -translate-y-1/2 hidden sm:block"
          >
            <Image src={arrowleft} alt="Prev" width={71} height={45} />
          </button>
          <button
            onClick={() => api?.scrollNext()}
            className="absolute right-0 top-[45%] translate-x-1/2 -translate-y-1/2 hidden sm:block"
          >
            <Image src={arrowright} alt="Next" width={71} height={45} />
          </button>

          {/* arrows – mobile */}
          <div className="sm:hidden mt-6 flex w-full justify-center gap-8">
            <button onClick={() => api?.scrollPrev()}>
              <Image src={arrowleft} alt="Prev" width={50} height={32} />
            </button>
            <button onClick={() => api?.scrollNext()}>
              <Image src={arrowright} alt="Next" width={50} height={32} />
            </button>
          </div>
        </Carousel>
      </div>
    </div>
  );
};

export default Testimonials;
