import Card from "./cardcomponents";
import img1 from "@/public/image/assests/content1.png";
import img2 from "@/public/image/assests/content 2.png";
import img3 from "@/public/image/assests/Content 3.png";

export default function CardGrid() {
  const cards = [
    {
      number: "01",
      title: "Full‑Service Planning",
      description: "Complete wedding planning from concept to execution.",
      image: img1,
    },
    {
      number: "02",
      title: "Partial Planning",
      description: "Assistance with specific aspects of your wedding.",
      image: img2,
    },
    {
      number: "03",
      title: "Day‑of Coordination",
      description: "Ensuring your big day runs smoothly.",
      image: img3,
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 p-4">
    {cards.map((card, index) => (
      <Card key={index} {...card} />
    ))}
  </div>
  
  );
}
