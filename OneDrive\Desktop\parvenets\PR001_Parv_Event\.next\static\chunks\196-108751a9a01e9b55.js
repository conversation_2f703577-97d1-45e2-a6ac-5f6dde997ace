"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196],{312:(e,t,a)=>{a.d(t,{A:()=>m});var s=a(5155),r=a(2115),o=a(6766),n=a(6874),c=a.n(n),d=a(5695),l=a(5305),i=a(1235);function m(e){let{categories:t,recentBlogs:a,allKeywords:n,searchQuery:m,selectedCategory:p,onSearchChange:g,onSearchSubmit:x,onCategoryFilter:h,onKeywordClick:u}=e,y=(0,d.useRouter)(),[b,w]=(0,r.useState)(!1),[f,j]=(0,r.useState)(!1),[k,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{N(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let v=(e,t)=>{let a=t.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");y.push("/blog/".concat(e,"/").concat(a))};return(0,s.jsxs)("div",{className:"w-full lg:w-[350px] flex flex-col gap-6 md:gap-8",children:[(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Search"}),(0,s.jsxs)("form",{onSubmit:x,className:"relative",children:[(0,s.jsx)("input",{type:"text",value:m,onChange:e=>g(e.target.value),placeholder:"Search blogs...",className:"w-full bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 md:py-3 pr-10 md:pr-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-orange-500 text-sm md:text-base"}),(0,s.jsx)("button",{type:"submit",className:"absolute right-2 md:right-3 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-orange-500 transition-colors",children:(0,s.jsx)(l.YQq,{className:"w-4 h-4 md:w-5 md:h-5"})})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Category"}),(0,s.jsxs)("div",{className:"space-y-2 md:space-y-3",children:[t.slice(0,b?t.length:k?3:5).map((e,t)=>(0,s.jsxs)("div",{onClick:()=>h(e.name),className:"flex justify-between items-center p-2 md:p-3 rounded-lg cursor-pointer transition-colors ".concat(p===e.name?"bg-orange-500 text-white":"bg-gray-50 text-gray-700 hover:bg-gray-100"),children:[(0,s.jsx)("span",{className:"font-medium text-sm md:text-base",children:e.name}),(0,s.jsxs)("span",{className:"text-xs md:text-sm",children:["(",e.count,")"]})]},t)),t.length>(k?3:5)&&(0,s.jsx)("button",{onClick:()=>w(!b),className:"w-full text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium py-1 md:py-2 transition-colors",children:b?"Show Less":"More"})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Recent News"}),(0,s.jsx)("div",{className:"space-y-3 md:space-y-4",children:a.map(e=>(0,s.jsxs)("div",{onClick:()=>v(e._id,e.title),className:"flex gap-2 md:gap-3 cursor-pointer group",children:[(0,s.jsx)("div",{className:"w-12 h-12 md:w-16 md:h-16 rounded-lg overflow-hidden flex-shrink-0",children:(0,s.jsx)(o.default,{src:e.imageUrl,alt:e.title,width:64,height:64,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"text-gray-900 text-xs md:text-sm font-medium line-clamp-2 group-hover:text-orange-500 transition-colors",children:e.title}),(0,s.jsx)("p",{className:"text-gray-500 text-[10px] md:text-xs mt-0.5 md:mt-1",children:(0,i._n)(e.createdAt)})]})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Keywords"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 md:gap-2",children:[n.slice(0,f?void 0:k?5:10).map((e,t)=>(0,s.jsx)("span",{onClick:()=>u(e),className:"bg-gray-100 hover:bg-orange-500 text-gray-700 hover:text-white px-2 py-0.5 md:px-3 md:py-1 rounded-full text-xs md:text-sm cursor-pointer transition-colors",children:e.replace(/[\[\]"]/g,"")},t)),n.length>(k?5:10)&&(0,s.jsx)("button",{onClick:()=>j(!f),className:"text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors",children:f?"Less":"More"})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Any Questions?"}),(0,s.jsx)("p",{className:"text-gray-600 text-xs md:text-sm mb-3 md:mb-4",children:"Have questions about our services or need help with your project?"}),(0,s.jsx)(c(),{href:"/contact",className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-3 md:py-3 md:px-4 rounded-lg transition-colors text-center block text-sm md:text-base",children:"Let's Talk"})]})]})}},1235:(e,t,a)=>{a.d(t,{P1:()=>m,_n:()=>i,dZ:()=>l,f_:()=>n,tz:()=>c,z$:()=>d});var s=a(7693),r=a(1966);let o={getBlogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.pageSize&&t.append("pageSize",e.pageSize.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),e.category&&t.append("category",e.category),e.search&&t.append("search",e.search),e.keywords&&t.append("keywords",e.keywords);let a="/blogs".concat(t.toString()?"?".concat(t.toString()):"");return(await s.u.get((0,r.c$)(a))).data},getBlogById:async e=>(await s.u.get((0,r.c$)("/blogs/".concat(e)))).data,getBlogsByCategory:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new URLSearchParams;t.page&&a.append("page",t.page.toString()),t.pageSize&&a.append("pageSize",t.pageSize.toString()),t.sortBy&&a.append("sortBy",t.sortBy),t.sortOrder&&a.append("sortOrder",t.sortOrder),t.search&&a.append("search",t.search),t.keywords&&a.append("keywords",t.keywords);let o="/blogs/category/".concat(e).concat(a.toString()?"?".concat(a.toString()):"");return(await s.u.get((0,r.c$)(o))).data},createBlog:async e=>{let t=new FormData;return t.append("title",e.title),t.append("description",e.description),Array.isArray(e.category)?t.append("category",e.category.join(",")):t.append("category",e.category),e.keywords&&(Array.isArray(e.keywords)?t.append("keywords",e.keywords.join(",")):t.append("keywords",e.keywords)),e.image&&t.append("image",e.image),(await s.u.post((0,r.c$)("/blogs"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},updateBlog:async e=>{let{id:t,...a}=e,o=new FormData;return a.title&&o.append("title",a.title),a.description&&o.append("description",a.description),a.category&&(Array.isArray(a.category)?o.append("category",a.category.join(",")):o.append("category",a.category)),a.keywords&&(Array.isArray(a.keywords)?o.append("keywords",a.keywords.join(",")):o.append("keywords",a.keywords)),a.image&&o.append("image",a.image),(await s.u.put((0,r.c$)("/blogs/".concat(t)),o,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteBlog:async e=>(await s.u.delete((0,r.c$)("/blogs/".concat(e)))).data,searchBlogs:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o.getBlogs({...t,search:e})},getPopularBlogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return o.getBlogs({pageSize:e,sortBy:"views",sortOrder:"desc"})},getRecentBlogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return o.getBlogs({pageSize:e,sortBy:"createdAt",sortOrder:"desc"})}},n=async e=>o.getBlogs({page:e.page,pageSize:e.pageSize||e.limit,search:e.search,category:e.category,keywords:e.keywords}),c=async()=>{try{let e=await o.getBlogs({pageSize:1e3});if(!e.status||!e.data||!e.data.data)return{status:!0,data:[]};let t={};return e.data.data.forEach(e=>{e.category&&Array.isArray(e.category)&&e.category.forEach(e=>{e&&"string"==typeof e&&(t[e]=(t[e]||0)+1)})}),{status:!0,data:Object.entries(t).map(e=>{let[t,a]=e;return{name:t,count:a}})}}catch(e){return console.error("Error getting categories from blogs:",e),{status:!0,data:[]}}},d=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return o.getRecentBlogs(e)},l=async e=>o.getBlogById(e),i=e=>{try{if(!e)return"No Date";if(e.includes("/")&&e.includes(","))return e.split(",")[0];let t=new Date(e);if(isNaN(t.getTime()))return"Invalid Date";let a=t.getDate().toString().padStart(2,"0"),s=(t.getMonth()+1).toString().padStart(2,"0"),r=t.getFullYear();return"".concat(a,"/").concat(s,"/").concat(r)}catch(e){return console.error("Error formatting date:",e),"Invalid Date"}},m=(e,t,a)=>{let s=encodeURIComponent(t),r=encodeURIComponent(a);return{facebook:"https://www.facebook.com/sharer/sharer.php?u=".concat(r),twitter:"https://twitter.com/intent/tweet?text=".concat(s,"&url=").concat(r),linkedin:"https://www.linkedin.com/sharing/share-offsite/?url=".concat(r),whatsapp:"https://wa.me/?text=".concat(s," - ").concat(r),telegram:"https://t.me/share/url?url=".concat(r,"&text=").concat(s),reddit:"https://reddit.com/submit?url=".concat(r,"&title=").concat(s),pinterest:"https://pinterest.com/pin/create/button/?url=".concat(r,"&description=").concat(s)}}},1661:(e,t,a)=>{a.d(t,{A:()=>y});var s=a(5155),r=a(2115),o=a(9911),n=a(351),c=a(6756),d=a(4416),l=a(3999);function i(e){let{...t}=e;return(0,s.jsx)(c.bL,{"data-slot":"dialog",...t})}function m(e){let{...t}=e;return(0,s.jsx)(c.ZL,{"data-slot":"dialog-portal",...t})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(c.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function g(e){let{className:t,children:a,showCloseButton:r=!0,...o}=e;return(0,s.jsxs)(m,{"data-slot":"dialog-portal",children:[(0,s.jsx)(p,{}),(0,s.jsxs)(c.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,r&&(0,s.jsxs)(c.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(d.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(c.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a})}var u=a(1235);function y(e){let{isOpen:t,onClose:a,blogId:c,blogTitle:d,blogUrl:l}=e,[m,p]=(0,r.useState)(!1),y=l||window.location.href,b=(0,u.P1)(c,d,y),w=async()=>{try{await navigator.clipboard.writeText(y),p(!0),setTimeout(()=>p(!1),2e3)}catch(e){console.error("Failed to copy link:",e)}},f=e=>{let t="";switch(e){case"facebook":t=b.facebook;break;case"twitter":t=b.twitter;break;case"linkedin":t=b.linkedin;break;case"whatsapp":t="https://wa.me/?text=".concat(encodeURIComponent("".concat(d," - ").concat(y)));break;case"instagram":w();return}t&&window.open(t,"_blank","width=600,height=400")};return(0,s.jsx)(i,{open:t,onOpenChange:a,children:(0,s.jsxs)(g,{className:"bg-white border-gray-200 max-w-xs sm:max-w-md",children:[(0,s.jsx)(x,{children:(0,s.jsxs)(h,{className:"text-gray-900 text-lg sm:text-xl font-bold flex items-center gap-2",children:[(0,s.jsx)(n.Pum,{className:"w-4 h-4 sm:w-5 sm:h-5"}),"Share this article"]})}),(0,s.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-2 sm:p-3",children:(0,s.jsx)("p",{className:"text-gray-700 text-xs sm:text-sm line-clamp-2",children:d})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 sm:gap-3",children:[(0,s.jsxs)("button",{onClick:()=>f("facebook"),className:"flex items-center gap-2 p-2 sm:p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.iYk,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Facebook"})]}),(0,s.jsxs)("button",{onClick:()=>f("twitter"),className:"flex items-center gap-2 p-2 sm:p-3 bg-sky-500 hover:bg-sky-600 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.feZ,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Twitter"})]}),(0,s.jsxs)("button",{onClick:()=>f("linkedin"),className:"flex items-center gap-2 p-2 sm:p-3 bg-blue-700 hover:bg-blue-800 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.QEs,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"LinkedIn"})]}),(0,s.jsxs)("button",{onClick:()=>f("whatsapp"),className:"flex items-center gap-2 p-2 sm:p-3 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.EcP,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"WhatsApp"})]}),(0,s.jsxs)("button",{onClick:()=>f("instagram"),className:"flex items-center gap-2 p-2 sm:p-3 bg-pink-600 hover:bg-pink-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.ao$,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Instagram"})]}),(0,s.jsx)("button",{onClick:w,className:"flex items-center gap-2 p-2 sm:p-3 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.YrT,{className:"w-4 h-4 sm:w-5 sm:h-5 text-green-400"}),(0,s.jsx)("span",{className:"font-medium text-green-400",children:"Copied!"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.paH,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Copy Link"})]})})]}),(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-2 sm:p-3",children:[(0,s.jsx)("p",{className:"text-gray-600 text-xs mb-1",children:"Link:"}),(0,s.jsx)("p",{className:"text-gray-700 text-xs sm:text-sm break-all",children:y})]})]})]})})}},1966:(e,t,a)=>{a.d(t,{JW:()=>s,KB:()=>o,c$:()=>r});let s={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},r=e=>{let t=s.BASE_URL,a=e.startsWith("/")?e:"/".concat(e);return"".concat(t,"/api").concat(a)},o=r},3999:(e,t,a)=>{a.d(t,{cn:()=>o});var s=a(2596),r=a(9688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},7693:(e,t,a)=>{a.d(t,{u:()=>n});var s=a(3464),r=a(1966);let o=s.A.create({timeout:r.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});o.interceptors.request.use(e=>{{let t=localStorage.getItem("authToken");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>(console.error("❌ Request Error:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(e.response){let{status:t,data:a}=e.response;switch(t){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",a)}}else if(e.request){var t,a,s;console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:null==(t=e.config)?void 0:t.url,method:null==(a=e.config)?void 0:a.method,baseURL:null==(s=e.config)?void 0:s.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."}else console.error("❌ Error:",e.message);return Promise.reject(e)});let n={get:(e,t)=>o.get(e,t),post:(e,t,a)=>o.post(e,t,a),put:(e,t,a)=>o.put(e,t,a),patch:(e,t,a)=>o.patch(e,t,a),delete:(e,t)=>o.delete(e,t)}}}]);