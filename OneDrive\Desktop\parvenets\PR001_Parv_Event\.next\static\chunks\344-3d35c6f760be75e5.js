(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[344],{1804:(e,t,n)=>{"use strict";n.d(t,{A9:()=>$,AH:()=>h,Bv:()=>v,C:()=>R,CC:()=>b,EC:()=>g,HR:()=>w,I1:()=>m,Kv:()=>l,Ky:()=>i,Nc:()=>k,OT:()=>u,P9:()=>p,QQ:()=>d,Qe:()=>s,Qo:()=>f,RK:()=>I,Td:()=>K,Ue:()=>C,Uj:()=>O,V:()=>V,b6:()=>a,e7:()=>H,j7:()=>A,jR:()=>W,jn:()=>_,kJ:()=>o,mY:()=>r,or:()=>T,pc:()=>x,pq:()=>c,qq:()=>L,s:()=>D,sI:()=>N,t$:()=>z,tC:()=>E,tD:()=>S,tu:()=>P,uF:()=>F,vg:()=>y,zn:()=>M});let r="carousel",l="controller",i="navigation",a="no-scroll",o="portal",u="root",c="toolbar",s="fullscreen",d="thumbnails",h="zoom",f="loading",m="error",p="complete",v="placeholder",g=e=>`active-slide-${e}`;g(f),g("playing"),g(m),g(p);let b="fullsize",E="flex_center",w="no_scroll",y="no_scroll_padding",x="slide_wrapper",C="slide_wrapper_interactive",M="prev",k="next",N="swipe",R="close",L="onPointerDown",I="onPointerMove",P="onPointerUp",S="onPointerLeave",D="onPointerCancel",F="onKeyDown",z="onKeyUp",A="onWheel",T="Escape",$="ArrowLeft",_="ArrowRight",O="button",W="icon",K="contain",H="cover",V="Unknown action type"},2355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4178:(e,t,n)=>{"use strict";n.d(t,{A:()=>x});var r=n(2115),l=n(6209),i=n(1804);let a={maxZoomPixelRatio:1,zoomInMultiplier:2,doubleTapDelay:300,doubleClickDelay:500,doubleClickMaxStops:2,keyboardMoveDistance:50,wheelZoomDistanceFactor:100,pinchZoomDistanceFactor:100,scrollToZoom:!1},o=e=>({...a,...e});function u(){let{zoom:e}=(0,l.D$)();return o(e)}function c(e,t){return((e.clientX-t.clientX)**2+(e.clientY-t.clientY)**2)**.5}function s(e,t,n=100,r=2){return e*Math.min(1+Math.abs(t/n),r)**Math.sign(t)}let d=r.createContext(null),h=(0,l.Tz)("useZoom","ZoomControllerContext",d);function f({children:e}){let[t,n]=r.useState(),{slideRect:a}=(0,l.as)(),{imageRect:o,maxZoom:h}=function(e,t){var n,r;let i={width:0,height:0},a={width:0,height:0},{currentSlide:o}=(0,l.mp)(),{imageFit:c}=(0,l.D$)().carousel,{maxZoomPixelRatio:s}=u();if(e&&o){let u={...o,...t};if((0,l.tx)(u)){let t=(0,l.QJ)(u,c),o=Math.max(...((null==(n=u.srcSet)?void 0:n.map(e=>e.width))||[]).concat(u.width?[u.width]:[])),d=Math.max(...((null==(r=u.srcSet)?void 0:r.map(e=>e.height))||[]).concat(u.height?[u.height]:[]));o>0&&d>0&&e.width>0&&e.height>0&&(a={width:(a=t?{width:Math.round(Math.min(o,e.width/e.height*d)),height:Math.round(Math.min(d,e.height/e.width*o))}:{width:o,height:d}).width*s,height:a.height*s},i=t?{width:Math.min(e.width,a.width,o),height:Math.min(e.height,a.height,d)}:{width:Math.round(Math.min(e.width,e.height/d*o,o)),height:Math.round(Math.min(e.height,e.width/o*d,d))})}}let d=i.width?Math.max((0,l.LI)(a.width/i.width,5),1):1;return{imageRect:i,maxZoom:d}}(a,null==t?void 0:t.imageDimensions),{zoom:f,offsetX:m,offsetY:p,disabled:v,changeZoom:g,changeOffsets:b,zoomIn:E,zoomOut:w}=function(e,t,n){let[i,a]=r.useState(1),[o,c]=r.useState(0),[s,d]=r.useState(0),h=function(e,t,n,i){let a=r.useRef(void 0),o=r.useRef(void 0),{zoom:u}=(0,l.D$)().animation,c=(0,l.iJ)(),s=(0,l.DK)(()=>{var r,l,s;if(null==(r=a.current)||r.cancel(),a.current=void 0,o.current&&(null==i?void 0:i.current)){try{a.current=null==(s=(l=i.current).animate)?void 0:s.call(l,[{transform:o.current},{transform:`scale(${e}) translateX(${t}px) translateY(${n}px)`}],{duration:c?0:null!=u?u:500,easing:a.current?"ease-out":"ease-in-out"})}catch(e){console.error(e)}o.current=void 0,a.current&&(a.current.onfinish=()=>{a.current=void 0})}});return(0,l.Nf)(s,[e,t,n,s]),r.useCallback(()=>{o.current=(null==i?void 0:i.current)?window.getComputedStyle(i.current).transform:void 0},[i])}(i,o,s,n),{currentSlide:f,globalIndex:m}=(0,l.mp)(),{containerRect:p,slideRect:v}=(0,l.as)(),{zoomInMultiplier:g}=u(),b=f&&(0,l.tx)(f)?f.src:void 0,E=!b||!(null==n?void 0:n.current);(0,l.Nf)(()=>{a(1),c(0),d(0)},[m,b]);let w=r.useCallback((t,n,r)=>{let l=r||i,a=o-(t||0),u=s-(n||0),h=(e.width*l-v.width)/2/l,f=(e.height*l-v.height)/2/l;c(Math.min(Math.abs(a),Math.max(h,0))*Math.sign(a)),d(Math.min(Math.abs(u),Math.max(f,0))*Math.sign(u))},[i,o,s,v,e.width,e.height]),y=r.useCallback((e,n,r,o)=>{let u=(0,l.LI)(Math.min(Math.max(e+.001<t?e:t,1),t),5);u!==i&&(n||h(),w(r?r*(1/i-1/u):0,o?o*(1/i-1/u):0,u),a(u))},[i,t,w,h]),x=(0,l.DK)(()=>{i>1&&(i>t&&y(t,!0),w())});(0,l.Nf)(x,[p.width,p.height,x]);let C=r.useCallback(()=>y(i*g),[i,g,y]),M=r.useCallback(()=>y(i/g),[i,g,y]);return{zoom:i,offsetX:o,offsetY:s,disabled:E,changeOffsets:w,changeZoom:y,zoomIn:C,zoomOut:M}}(o,h,null==t?void 0:t.zoomWrapperRef),{on:y}=(0,l.D$)(),x=(0,l.DK)(()=>{var e;v||null==(e=y.zoom)||e.call(y,{zoom:f})});r.useEffect(x,[f,x]),function(e,t,n,a,o,d){let h=r.useRef([]),f=r.useRef(0),m=r.useRef(void 0),{globalIndex:p}=(0,l.mp)(),{getOwnerWindow:v}=(0,l.uG)(),{containerRef:g,subscribeSensors:b}=(0,l.as)(),{keyboardMoveDistance:E,zoomInMultiplier:w,wheelZoomDistanceFactor:y,scrollToZoom:x,doubleTapDelay:C,doubleClickDelay:M,doubleClickMaxStops:k,pinchZoomDistanceFactor:N}=u(),R=r.useCallback(e=>{if(g.current){let{pageX:t,pageY:n}=e,{scrollX:r,scrollY:l}=v(),{left:i,top:a,width:o,height:u}=g.current.getBoundingClientRect();return[t-i-r-o/2,n-a-l-u/2]}return[]},[g,v]),L=(0,l.DK)(t=>{let{key:n,metaKey:r,ctrlKey:l}=t,i=r||l,u=()=>{t.preventDefault(),t.stopPropagation()};if(e>1){let e=(e,t)=>{u(),o(e,t)};"ArrowDown"===n?e(0,E):"ArrowUp"===n?e(0,-E):"ArrowLeft"===n?e(-E,0):"ArrowRight"===n&&e(E,0)}let c=e=>{u(),a(e)};"+"===n||i&&"="===n?c(e*w):"-"===n||i&&"_"===n?c(e/w):i&&"0"===n&&c(1)}),I=(0,l.DK)(t=>{if((t.ctrlKey||x)&&Math.abs(t.deltaY)>Math.abs(t.deltaX)){t.stopPropagation(),a(s(e,-t.deltaY,y),!0,...R(t));return}e>1&&(t.stopPropagation(),x||o(t.deltaX,t.deltaY))}),P=r.useCallback(e=>{let t=h.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),S=r.useCallback(e=>{P(e),e.persist(),h.current.push(e)},[P]),D=(0,l.DK)(n=>{var r;let l=h.current;if("mouse"===n.pointerType&&n.buttons>1||!(null==(r=null==d?void 0:d.current)?void 0:r.contains(n.target)))return;e>1&&n.stopPropagation();let{timeStamp:i}=n;0===l.length&&i-f.current<("touch"===n.pointerType?C:M)?(f.current=0,a(e!==t?e*Math.max(t**(1/k),w):1,!1,...R(n))):f.current=i,S(n),2===l.length&&(m.current=c(l[0],l[1]))}),F=(0,l.DK)(t=>{let n=h.current,r=n.find(e=>e.pointerId===t.pointerId);if(2===n.length&&m.current){t.stopPropagation(),S(t);let r=c(n[0],n[1]),l=r-m.current;Math.abs(l)>0&&(a(s(e,l,N),!0,...n.map(e=>R(e)).reduce((e,t)=>t.map((t,n)=>e[n]+t/2))),m.current=r);return}e>1&&(t.stopPropagation(),r&&(1===n.length&&o((r.clientX-t.clientX)/e,(r.clientY-t.clientY)/e),S(t)))}),z=r.useCallback(e=>{let t=h.current;2===t.length&&t.find(t=>t.pointerId===e.pointerId)&&(m.current=void 0),P(e)},[P]),A=r.useCallback(()=>{let e=h.current;e.splice(0,e.length),f.current=0,m.current=void 0},[]);(0,l.yQ)(b,D,F,z,n),r.useEffect(A,[p,A]),r.useEffect(()=>n?()=>{}:(0,l.tP)(A,b(i.uF,L),b(i.j7,I)),[n,b,A,L,I])}(f,h,v,g,b,null==t?void 0:t.zoomWrapperRef);let C=r.useMemo(()=>({zoom:f,maxZoom:h,offsetX:m,offsetY:p,disabled:v,zoomIn:E,zoomOut:w,changeZoom:g}),[f,h,m,p,v,E,w,g]);r.useImperativeHandle(u().ref,()=>C,[C]);let M=r.useMemo(()=>({...C,setZoomWrapper:n}),[C,n]);return r.createElement(d.Provider,{value:M},e)}let m=(0,l.wt)("ZoomIn",r.createElement(r.Fragment,null,r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}),r.createElement("path",{d:"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"}))),p=(0,l.wt)("ZoomOut",r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"})),v=r.forwardRef(function({zoomIn:e,onLoseFocus:t},n){let i=r.useRef(!1),a=r.useRef(!1),{zoom:o,maxZoom:u,zoomIn:c,zoomOut:s,disabled:d}=h(),{render:f}=(0,l.D$)(),v=d||(e?o>=u:o<=1);return r.useEffect(()=>{v&&i.current&&a.current&&t(),v||(i.current=!0)},[v,t]),r.createElement(l.K0,{ref:n,disabled:v,label:e?"Zoom in":"Zoom out",icon:e?m:p,renderIcon:e?f.iconZoomIn:f.iconZoomOut,onClick:e?c:s,onFocus:()=>{a.current=!0},onBlur:()=>{a.current=!1}})});function g(){let e=r.useRef(null),t=r.useRef(null),{focus:n}=(0,l.as)(),i=r.useCallback(e=>{var t,r;(null==(t=e.current)?void 0:t.disabled)?n():null==(r=e.current)||r.focus()},[n]),a=r.useCallback(()=>i(e),[i]),o=r.useCallback(()=>i(t),[i]);return r.createElement(r.Fragment,null,r.createElement(v,{zoomIn:!0,ref:e,onLoseFocus:o}),r.createElement(v,{ref:t,onLoseFocus:a}))}function b(){let{render:e}=(0,l.D$)(),t=h();return e.buttonZoom?r.createElement(r.Fragment,null,e.buttonZoom(t)):r.createElement(g,null)}function E({current:e,preload:t},{type:n,source:r}){switch(n){case"fetch":if(!e)return{current:r};return{current:e,preload:r};case"done":if(r===t)return{current:r};return{current:e,preload:t};default:throw Error(i.V)}}function w(e){var t,n;let[{current:i,preload:a},o]=r.useReducer(E,{}),{slide:u,rect:c,imageFit:s,render:d,interactive:h}=e,f=u.srcSet.sort((e,t)=>e.width-t.width),m=null!=(t=u.width)?t:f[f.length-1].width,p=null!=(n=u.height)?n:f[f.length-1].height,v=(0,l.QJ)(u,s),g=Math.max(...f.map(e=>e.width)),b=Math.min((v?Math.max:Math.min)(c.width,m*(c.height/p)),g),w=(0,l.Y5)(),y=(0,l.DK)(()=>{var e;let t=null!=(e=f.find(e=>e.width>=b*w))?e:f[f.length-1];(!i||f.findIndex(e=>e.src===i)<f.findIndex(e=>e===t))&&o({type:"fetch",source:t.src})});(0,l.Nf)(y,[c.width,c.height,w,y]);let x=(0,l.DK)(e=>o({type:"done",source:e})),C={WebkitTransform:h?"initial":"translateZ(0)"};return v||Object.assign(C,c.width/c.height<m/p?{width:"100%",height:"auto"}:{width:"auto",height:"100%"}),r.createElement(r.Fragment,null,a&&a!==i&&r.createElement(l.Z$,{key:"preload",...e,offset:void 0,slide:{...u,src:a,srcSet:void 0},style:{position:"absolute",visibility:"hidden",...C},onLoad:()=>x(a),render:{...d,iconLoading:()=>null,iconError:()=>null}}),i&&r.createElement(l.Z$,{key:"current",...e,slide:{...u,src:i,srcSet:void 0},style:C}))}function y({render:e,slide:t,offset:n,rect:a}){var o,u;let[c,s]=r.useState(),d=r.useRef(null),{zoom:f,maxZoom:m,offsetX:p,offsetY:v,setZoomWrapper:g}=h(),b=f>1,{carousel:E,on:y}=(0,l.D$)(),{currentIndex:x}=(0,l.mp)();(0,l.Nf)(()=>0===n?(g({zoomWrapperRef:d,imageDimensions:c}),()=>g(void 0)):()=>{},[n,c,g]);let C=null==(o=e.slide)?void 0:o.call(e,{slide:t,offset:n,rect:a,zoom:f,maxZoom:m});if(!C&&(0,l.tx)(t)){let i={slide:t,offset:n,rect:a,render:e,imageFit:E.imageFit,imageProps:E.imageProps,onClick:0===n?()=>{var e;return null==(e=y.click)?void 0:e.call(y,{index:x})}:void 0};C=((null==(u=t.srcSet)?void 0:u.length)||0)>0?r.createElement(w,{...i,slide:t,interactive:b,rect:0===n?{width:a.width*f,height:a.height*f}:a}):r.createElement(l.Z$,{onLoad:e=>s({width:e.naturalWidth,height:e.naturalHeight}),...i})}return C?r.createElement("div",{ref:d,className:(0,l.$z)((0,l.oF)(i.CC),(0,l.oF)(i.tC),(0,l.oF)(i.pc),b&&(0,l.oF)(i.Ue)),style:0===n?{transform:`scale(${f}) translateX(${p}px) translateY(${v}px)`}:void 0},C):null}let x=({augment:e,addModule:t})=>{e(({zoom:e,toolbar:t,render:n,controller:a,...u})=>{let c=o(e);return{zoom:c,toolbar:(0,l.bo)(t,i.AH,r.createElement(b,null)),render:{...n,slide:e=>{var t;return(0,l.tx)(e.slide)?r.createElement(y,{render:n,...e}):null==(t=n.slide)?void 0:t.call(n,e)}},controller:{...a,preventDefaultWheelY:c.scrollToZoom},...u}}),t((0,l.Fi)(i.AH,f))}},4216:(e,t,n)=>{"use strict";n.d(t,{A:()=>P});var r=n(2115),l=n(6209),i=n(1804);let a={ref:null,position:"bottom",width:120,height:80,border:1,borderRadius:4,padding:4,gap:16,imageFit:"contain",vignette:!0,hidden:!1,showToggle:!1},o=e=>({...a,...e});function u(){let{thumbnails:e}=(0,l.D$)();return o(e)}let c=e=>(0,l.f4)(i.QQ,e),s=e=>c((0,l.f4)("thumbnail",e)),d=(0,l.wt)("VideoThumbnail",r.createElement("path",{d:"M10 16.5l6-4.5-6-4.5v9zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"})),h=(0,l.wt)("UnknownThumbnail",r.createElement("path",{d:"M23 18V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2zM8.5 12.5l2.5 3.01L14.5 11l4.5 6H5l3.5-4.5z"})),f=(0,l.fh)("active"),m=(0,l.fh)("fadein"),p=(0,l.fh)("fadeout"),v=(0,l.fh)("placeholder"),g="delay",b="duration";function E({slide:e,onClick:t,active:n,fadeIn:a,fadeOut:o,placeholder:c,onLoseFocus:E}){let w=r.useRef(null),{render:y,styles:x}=(0,l.D$)(),{getOwnerDocument:C}=(0,l.uG)(),{width:M,height:k,imageFit:N}=u(),R=(0,l.DK)(E);return r.useEffect(()=>{o&&C().activeElement===w.current&&R()},[o,R,C]),r.createElement("button",{ref:w,type:"button",className:(0,l.$z)((0,l.oF)(i.tC),(0,l.oF)(s()),n&&(0,l.oF)(s(f())),a&&(0,l.oF)(s(m())),o&&(0,l.oF)(s(p())),c&&(0,l.oF)(s(v()))),style:{...a?{[(0,l.Vg)(s(m(b)))]:`${a.duration}ms`,[(0,l.Vg)(s(m(g)))]:`${a.delay}ms`}:null,...o?{[(0,l.Vg)(s(p(b)))]:`${o.duration}ms`,[(0,l.Vg)(s(p(g)))]:`${o.delay}ms`}:null,...x.thumbnail},onClick:t},e&&function({slide:e,render:t,rect:n,imageFit:a}){var o;let u=null==(o=t.thumbnail)?void 0:o.call(t,{slide:e,render:t,rect:n,imageFit:a});if(u)return u;let c={render:t,rect:n,imageFit:a};if(e.thumbnail)return r.createElement(l.Z$,{slide:{src:e.thumbnail},...c});if((0,l.tx)(e))return r.createElement(l.Z$,{slide:e,...c});let f=(0,l.oF)(s(i.jR));return"video"===e.type?r.createElement(r.Fragment,null,e.poster&&r.createElement(l.Z$,{slide:{src:e.poster},...c}),r.createElement(d,{className:f})):r.createElement(h,{className:f})}({slide:e,render:y,rect:{width:M,height:k},imageFit:N}))}function w(e){return["top","bottom"].includes(e)}function y(e,t){return t+2*(e.border+e.padding)+e.gap}function x({visible:e,containerRef:t}){let n=r.useRef(null),o=(0,l.x3)(),{publish:d,subscribe:h}=(0,l.LJ)(),{carousel:f,styles:m}=(0,l.D$)(),{slides:p,globalIndex:v,animation:g}=(0,l.mp)(),{registerSensors:b,subscribeSensors:x}=(0,l.Yc)();(0,l.JZ)(x);let C=u(),{position:M,width:k,height:N,border:R,borderStyle:L,borderColor:I,borderRadius:P,padding:S,gap:D,vignette:F}=C,z=(null==g?void 0:g.duration)!==void 0&&(null==g?void 0:g.increment)||0,A=(null==g?void 0:g.duration)||0,{prepareAnimation:T}=(0,l.sM)(n,e=>({keyframes:w(M)?[{transform:`translateX(${(o?-1:1)*y(C,k)*z+e}px)`},{transform:"translateX(0)"}]:[{transform:`translateY(${y(C,N)*z+e}px)`},{transform:"translateY(0)"}],duration:A,easing:null==g?void 0:g.easing})),$=(0,l.DK)(()=>{let e=0;if(t.current&&n.current){let r=t.current.getBoundingClientRect(),l=n.current.getBoundingClientRect();e=w(M)?l.left-r.left-(r.width-l.width)/2:l.top-r.top-(r.height-l.height)/2}T(e)});r.useEffect(()=>(0,l.tP)(h(i.sI,$)),[h,$]);let _=(0,l.eS)(f,p),O=[];if((0,l.DB)(p))for(let e=v-_-Math.abs(z);e<=v+_+Math.abs(z);e+=1){let t=f.finite&&(e<0||e>p.length-1)||z<0&&e<v-_||z>0&&e>v+_?null:(0,l.gZ)(p,e),n=[`${e}`,function(e){let{thumbnail:t,poster:n}=e||{thumbnail:"placeholder"};return"string"==typeof t&&t||"string"==typeof n&&n||e&&(0,l.Hw)(e)||void 0}(t)].filter(Boolean).join("|");O.push({key:n,index:e,slide:t})}let W=e=>()=>{e>v?d(i.Nc,{count:e-v}):e<v&&d(i.zn,{count:v-e})};return r.createElement("div",{className:(0,l.$z)((0,l.oF)(c("container")),(0,l.oF)(i.tC)),style:{...!e?{display:"none"}:null,...k!==a.width?{[(0,l.Vg)(s("width"))]:`${k}px`}:null,...N!==a.height?{[(0,l.Vg)(s("height"))]:`${N}px`}:null,...R!==a.border?{[(0,l.Vg)(s("border"))]:`${R}px`}:null,...L?{[(0,l.Vg)(s("border_style"))]:L}:null,...I?{[(0,l.Vg)(s("border_color"))]:I}:null,...P!==a.borderRadius?{[(0,l.Vg)(s("border_radius"))]:`${P}px`}:null,...S!==a.padding?{[(0,l.Vg)(s("padding"))]:`${S}px`}:null,...D!==a.gap?{[(0,l.Vg)(s("gap"))]:`${D}px`}:null,...m.thumbnailsContainer}},r.createElement("nav",{ref:n,style:m.thumbnailsTrack,className:(0,l.$z)((0,l.oF)(c("track")),(0,l.oF)(i.tC)),tabIndex:-1,...b},O.map(({key:e,index:t,slide:l})=>{let i=A/Math.abs(z||1),a=z>0&&t>v+_-z&&t<=v+_||z<0&&t<v-_-z&&t>=v-_?{duration:i,delay:((z>0?t-(v+_-z):v-_-z-t)-1)*i}:void 0,o=z>0&&t<v-_||z<0&&t>v+_?{duration:i,delay:(z>0?z-(v-_-t):-z-(t-(v+_)))*i}:void 0;return r.createElement(E,{key:e,slide:l,active:t===v,fadeIn:a,fadeOut:o,placeholder:!l,onClick:W(t),onLoseFocus:()=>{var e;return null==(e=n.current)?void 0:e.focus()}})})),F&&r.createElement("div",{className:(0,l.oF)(c("vignette"))}))}let C=r.createContext(null),M=(0,l.Tz)("useThumbnails","ThumbnailsContext",C);function k({children:e,...t}){let{ref:n,position:i,hidden:a}=o(t.thumbnails),[u,s]=r.useState(!a),d=r.useRef(null),h=r.useMemo(()=>({visible:u,show:()=>s(!0),hide:()=>s(!1)}),[u]);return r.useImperativeHandle(n,()=>h,[h]),r.createElement(l.RT,{...t},r.createElement(C.Provider,{value:h},r.createElement("div",{ref:d,className:(0,l.$z)((0,l.oF)(c()),(0,l.oF)(c(`${i}`)))},["start","top"].includes(i)&&r.createElement(x,{containerRef:d,visible:u}),r.createElement("div",{className:(0,l.oF)(c("wrapper"))},e),["end","bottom"].includes(i)&&r.createElement(x,{containerRef:d,visible:u}))))}let N=()=>r.createElement(r.Fragment,null,r.createElement("path",{strokeWidth:2,stroke:"currentColor",strokeLinejoin:"round",fill:"none",d:"M3 5l18 0l0 14l-18 0l0-14z"}),r.createElement("path",{d:"M5 14h4v3h-4zM10 14h4v3h-4zM15 14h4v3h-4z"})),R=(0,l.wt)("ThumbnailsVisible",N()),L=(0,l.Q5)("ThumbnailsHidden",N());function I(){let{visible:e,show:t,hide:n}=M(),{render:i}=(0,l.D$)();return i.buttonThumbnails?r.createElement(r.Fragment,null,i.buttonThumbnails({visible:e,show:t,hide:n})):r.createElement(l.K0,{label:e?"Hide thumbnails":"Show thumbnails",icon:e?R:L,renderIcon:e?i.iconThumbnailsVisible:i.iconThumbnailsHidden,onClick:e?n:t})}function P({augment:e,contains:t,append:n,addParent:a}){e(({thumbnails:e,toolbar:t,...n})=>{let a=o(e);return{toolbar:(0,l.bo)(t,i.QQ,a.showToggle?r.createElement(I,null):null),thumbnails:a,...n}});let u=(0,l.Fi)(i.QQ,k);t(i.Qe)?n(i.Qe,u):a(i.Kv,u)}},4901:()=>{},6209:(e,t,n)=>{"use strict";n.d(t,{$z:()=>c,Ay:()=>eZ,D$:()=>K,DB:()=>C,DK:()=>eh,Fi:()=>S,Hw:()=>N,JZ:()=>eF,K0:()=>q,LI:()=>g,LJ:()=>_,Nf:()=>eo,Q5:()=>et,QJ:()=>E,RT:()=>H,Tz:()=>p,Vg:()=>d,Y5:()=>x,Yc:()=>ev,Z$:()=>ew,as:()=>ek,bo:()=>R,eS:()=>L,f4:()=>h,fh:()=>f,gZ:()=>M,iJ:()=>eu,mp:()=>Z,oF:()=>s,sM:()=>ec,tP:()=>m,tx:()=>b,uG:()=>A,wt:()=>ee,x3:()=>ep,yQ:()=>ex});var r,l,i=n(2115),a=n(1804),o=n(7650);let u="yarl__";function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return[...t].filter(Boolean).join(" ")}function s(e){return"".concat(u).concat(e)}function d(e){return"--".concat(u).concat(e)}function h(e,t){return"".concat(e).concat(t?"_".concat(t):"")}function f(e){return t=>h(e,t)}function m(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return()=>{t.forEach(e=>{e()})}}function p(e,t,n){return()=>{let r=i.useContext(n);if(!r)throw Error("".concat(e," must be used within a ").concat(t,".Provider"));return r}}function v(){return"undefined"!=typeof window}function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=10**t;return Math.round((e+Number.EPSILON)*n)/n}function b(e){return void 0===e.type||"image"===e.type}function E(e,t){return e.imageFit===a.e7||e.imageFit!==a.Td&&t===a.e7}function w(e){return"string"==typeof e?Number.parseInt(e,10):e}function y(e){if("number"==typeof e)return{pixel:e};if("string"==typeof e){let t=w(e);return e.endsWith("%")?{percent:t}:{pixel:t}}return{pixel:0}}function x(){return(v()?null==window?void 0:window.devicePixelRatio:void 0)||1}function C(e){return e.length>0}function M(e,t){var n;return e[(n=e.length)>0?(t%n+n)%n:0]}function k(e,t){return C(e)?M(e,t):void 0}function N(e){return b(e)?e.src:void 0}function R(e,t,n){if(!n)return e;let{buttons:r,...l}=e,a=r.findIndex(e=>e===t),o=i.isValidElement(n)?i.cloneElement(n,{key:t},null):n;if(a>=0){let e=[...r];return e.splice(a,1,o),{buttons:e,...l}}return{buttons:[o,...r],...l}}function L(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return Math.min(e.preload,Math.max(e.finite?t.length-1:Math.floor(t.length/2),n))}let I=Number(i.version.split(".")[0])>=19,P={open:!1,close:()=>{},index:0,slides:[],render:{},plugins:[],toolbar:{buttons:[a.C]},labels:{},animation:{fade:250,swipe:500,easing:{fade:"ease",swipe:"ease-out",navigation:"ease-in-out"}},carousel:{finite:!1,preload:2,padding:"16px",spacing:"30%",imageFit:a.Td,imageProps:{}},controller:{ref:null,focus:!0,aria:!1,touchAction:"none",closeOnPullUp:!1,closeOnPullDown:!1,closeOnBackdropClick:!1,preventDefaultWheelX:!0,preventDefaultWheelY:!1,disableSwipeNavigation:!1},portal:{},noScroll:{disabled:!1},on:{},styles:{},className:""};function S(e,t){return{name:e,component:t}}function D(e,t){return{module:e,children:t}}function F(e,t,n){return e.flatMap(e=>{var r;return null!=(r=function e(t,n,r){return t.module.name===n?r(t):t.children?[D(t.module,t.children.flatMap(t=>{var l;return null!=(l=e(t,n,r))?l:[]}))]:[t]}(e,t,n))?r:[]})}let z=i.createContext(null),A=p("useDocument","DocumentContext",z);function T(e){let{nodeRef:t,children:n}=e,r=i.useMemo(()=>{let e=e=>{var n;return(null==(n=e||t.current)?void 0:n.ownerDocument)||document};return{getOwnerDocument:e,getOwnerWindow:t=>{var n;return(null==(n=e(t))?void 0:n.defaultView)||window}}},[t]);return i.createElement(z.Provider,{value:r},n)}let $=i.createContext(null),_=p("useEvents","EventsContext",$);function O(e){let{children:t}=e,[n]=i.useState({});i.useEffect(()=>()=>{Object.keys(n).forEach(e=>delete n[e])},[n]);let r=i.useMemo(()=>{let e=(e,t)=>{var r;null==(r=n[e])||r.splice(0,n[e].length,...n[e].filter(e=>e!==t))};return{publish:function(){for(var e,t=arguments.length,r=Array(t),l=0;l<t;l++)r[l]=arguments[l];let[i,a]=r;null==(e=n[i])||e.forEach(e=>e(a))},subscribe:(t,r)=>(n[t]||(n[t]=[]),n[t].push(r),()=>e(t,r)),unsubscribe:e}},[n]);return i.createElement($.Provider,{value:r},t)}let W=i.createContext(null),K=p("useLightboxProps","LightboxPropsContext",W);function H(e){let{children:t,...n}=e;return i.createElement(W.Provider,{value:n},t)}let V=i.createContext(null),Z=p("useLightboxState","LightboxStateContext",V),Y=i.createContext(null),U=p("useLightboxDispatch","LightboxDispatchContext",Y);function X(e,t){switch(t.type){case"swipe":{var n;let{slides:r}=e,l=(null==t?void 0:t.increment)||0,i=e.globalIndex+l,a=(n=r.length)>0?(i%n+n)%n:0,o=k(r,a);return{slides:r,currentIndex:a,globalIndex:i,currentSlide:o,animation:l||void 0!==t.duration?{increment:l,duration:t.duration,easing:t.easing}:void 0}}case"update":if(t.slides!==e.slides||t.index!==e.currentIndex)return{slides:t.slides,currentIndex:t.index,globalIndex:t.index,currentSlide:k(t.slides,t.index)};return e;default:throw Error(a.V)}}function j(e){let{slides:t,index:n,children:r}=e,[l,a]=i.useReducer(X,{slides:t,currentIndex:n,globalIndex:n,currentSlide:k(t,n)});i.useEffect(()=>{a({type:"update",slides:t,index:n})},[t,n]);let o=i.useMemo(()=>({...l,state:l,dispatch:a}),[l,a]);return i.createElement(Y.Provider,{value:a},i.createElement(V.Provider,{value:o},r))}let B=i.createContext(null),Q=p("useTimeouts","TimeoutsContext",B);function J(e){let{children:t}=e,[n]=i.useState([]);i.useEffect(()=>()=>{n.forEach(e=>window.clearTimeout(e)),n.splice(0,n.length)},[n]);let r=i.useMemo(()=>{let e=e=>{n.splice(0,n.length,...n.filter(t=>t!==e))};return{setTimeout:(t,r)=>{let l=window.setTimeout(()=>{e(l),t()},r);return n.push(l),l},clearTimeout:t=>{void 0!==t&&(e(t),window.clearTimeout(t))}}},[n]);return i.createElement(B.Provider,{value:r},t)}let q=i.forwardRef(function(e,t){var n;let{label:r,className:l,icon:o,renderIcon:u,onClick:d,style:h,...f}=e,{styles:m,labels:p}=K(),v=null!=(n=null==p?void 0:p[r])?n:r;return i.createElement("button",{ref:t,type:"button",title:v,"aria-label":v,className:c(s(a.Uj),l),onClick:d,style:{...h,...m.button},...f},u?u():i.createElement(o,{className:s(a.jR),style:m.icon}))});function G(e,t){let n=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",...e},t);return n.displayName=e,n}function ee(e,t){return G(e,i.createElement("g",{fill:"currentColor"},i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),t))}function et(e,t){return G(e,i.createElement(i.Fragment,null,i.createElement("defs",null,i.createElement("mask",{id:"strike"},i.createElement("path",{d:"M0 0h24v24H0z",fill:"white"}),i.createElement("path",{d:"M0 0L24 24",stroke:"black",strokeWidth:4}))),i.createElement("path",{d:"M0.70707 2.121320L21.878680 23.292883",stroke:"currentColor",strokeWidth:2}),i.createElement("g",{fill:"currentColor",mask:"url(#strike)"},i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),t)))}let en=ee("Close",i.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),er=ee("Previous",i.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})),el=ee("Next",i.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})),ei=ee("Loading",i.createElement(i.Fragment,null,Array.from({length:8}).map((e,t,n)=>i.createElement("line",{key:t,x1:"12",y1:"6.5",x2:"12",y2:"1.8",strokeLinecap:"round",strokeWidth:"2.6",stroke:"currentColor",strokeOpacity:1/n.length*(t+1),transform:"rotate(".concat(360/n.length*t,", 12, 12)")})))),ea=ee("Error",i.createElement("path",{d:"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z"})),eo=v()?i.useLayoutEffect:i.useEffect;function eu(){let[e,t]=i.useState(!1);return i.useEffect(()=>{var e,n;let r=null==(e=window.matchMedia)?void 0:e.call(window,"(prefers-reduced-motion: reduce)");t(null==r?void 0:r.matches);let l=e=>t(e.matches);return null==(n=null==r?void 0:r.addEventListener)||n.call(r,"change",l),()=>{var e;return null==(e=null==r?void 0:r.removeEventListener)?void 0:e.call(r,"change",l)}},[]),e}function ec(e,t){let n=i.useRef(void 0),r=i.useRef(void 0),l=eu();return eo(()=>{var i,a,o;if(e.current&&void 0!==n.current&&!l){let{keyframes:l,duration:u,easing:c,onfinish:s}=t(n.current,e.current.getBoundingClientRect(),function(e){let t=0,n=0,r=0,l=window.getComputedStyle(e).transform.match(/matrix.*\((.+)\)/);if(l){let e=l[1].split(",").map(w);6===e.length?(t=e[4],n=e[5]):16===e.length&&(t=e[12],n=e[13],r=e[14])}return{x:t,y:n,z:r}}(e.current))||{};if(l&&u){null==(i=r.current)||i.cancel(),r.current=void 0;try{r.current=null==(o=(a=e.current).animate)?void 0:o.call(a,l,{duration:u,easing:c})}catch(e){console.error(e)}r.current&&(r.current.onfinish=()=>{r.current=void 0,null==s||s()})}}n.current=void 0}),{prepareAnimation:e=>{n.current=e},isAnimationPlaying:()=>{var e;return(null==(e=r.current)?void 0:e.playState)==="running"}}}function es(){let e=i.useRef(null),t=i.useRef(void 0),[n,r]=i.useState();return{setContainerRef:i.useCallback(n=>{e.current=n,t.current&&(t.current.disconnect(),t.current=void 0);let l=()=>{if(n){let e=window.getComputedStyle(n),t=e=>parseFloat(e)||0;r({width:Math.round(n.clientWidth-t(e.paddingLeft)-t(e.paddingRight)),height:Math.round(n.clientHeight-t(e.paddingTop)-t(e.paddingBottom))})}else r(void 0)};l(),n&&"undefined"!=typeof ResizeObserver&&(t.current=new ResizeObserver(l),t.current.observe(n))},[]),containerRef:e,containerRect:n}}function ed(){let e=i.useRef(void 0),{setTimeout:t,clearTimeout:n}=Q();return i.useCallback((r,l)=>{n(e.current),e.current=t(r,l>0?l:0)},[t,n])}function eh(e){let t=i.useRef(e);return eo(()=>{t.current=e}),i.useCallback(function(){for(var e,n=arguments.length,r=Array(n),l=0;l<n;l++)r[l]=arguments[l];return null==(e=t.current)?void 0:e.call(t,...r)},[])}function ef(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function em(e,t){return i.useMemo(()=>null==e&&null==t?null:n=>{ef(e,n),ef(t,n)},[e,t])}function ep(){let[e,t]=i.useState(!1);return eo(()=>{t("rtl"===window.getComputedStyle(window.document.documentElement).direction)},[]),e}function ev(){let[e]=i.useState({}),t=i.useCallback((t,n)=>{var r;null==(r=e[t])||r.forEach(e=>{n.isPropagationStopped()||e(n)})},[e]);return{registerSensors:i.useMemo(()=>({onPointerDown:e=>t(a.qq,e),onPointerMove:e=>t(a.RK,e),onPointerUp:e=>t(a.tu,e),onPointerLeave:e=>t(a.tD,e),onPointerCancel:e=>t(a.s,e),onKeyDown:e=>t(a.uF,e),onKeyUp:e=>t(a.t$,e),onWheel:e=>t(a.j7,e)}),[t]),subscribeSensors:i.useCallback((t,n)=>(e[t]||(e[t]=[]),e[t].unshift(n),()=>{let r=e[t];r&&r.splice(0,r.length,...r.filter(e=>e!==n))}),[e])}}function eg(e,t){let n=i.useRef(0),r=ed(),l=eh(function(){for(var t=arguments.length,r=Array(t),l=0;l<t;l++)r[l]=arguments[l];n.current=Date.now(),e(r)});return i.useCallback(function(){for(var e=arguments.length,i=Array(e),a=0;a<e;a++)i[a]=arguments[a];r(()=>{l(i)},t-(Date.now()-n.current))},[t,l,r])}let eb=f("slide"),eE=f("slide_image");function ew(e){var t,n,r,l,o,u,d;let{slide:h,offset:f,render:m,rect:p,imageFit:g,imageProps:b,onClick:w,onLoad:y,onError:x,style:C}=e,[M,k]=i.useState(a.Qo),{publish:N}=_(),{setTimeout:R}=Q(),L=i.useRef(null);i.useEffect(()=>{0===f&&N((0,a.EC)(M))},[f,M,N]);let I=eh(e=>{("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{e.parentNode&&(k(a.P9),R(()=>{null==y||y(e)},0))})}),P=i.useCallback(e=>{L.current=e,(null==e?void 0:e.complete)&&I(e)},[I]),S=i.useCallback(e=>{I(e.currentTarget)},[I]),D=eh(()=>{k(a.I1),null==x||x()}),F=E(h,g),z=(e,t)=>Number.isFinite(e)?e:t,A=z(Math.max(...(null!=(n=null==(t=h.srcSet)?void 0:t.map(e=>e.width))?n:[]).concat(h.width?[h.width]:[]).filter(Boolean)),(null==(r=L.current)?void 0:r.naturalWidth)||0),T=z(Math.max(...(null!=(o=null==(l=h.srcSet)?void 0:l.map(e=>e.height))?o:[]).concat(h.height?[h.height]:[]).filter(Boolean)),(null==(u=L.current)?void 0:u.naturalHeight)||0),$=null==(d=h.srcSet)?void 0:d.sort((e,t)=>e.width-t.width).map(e=>"".concat(e.src," ").concat(e.width,"w")).join(", "),O=$&&p&&v()?"".concat(Math.round(Math.min(p&&!F&&h.width&&h.height?p.height/h.height*h.width:Number.MAX_VALUE,p.width)),"px"):void 0,{style:W,className:K,...H}=b||{};return i.createElement(i.Fragment,null,i.createElement("img",{ref:P,onLoad:S,onError:D,onClick:w,draggable:!1,className:c(s(eE()),F&&s(eE("cover")),M!==a.P9&&s(eE("loading")),K),style:{...A&&T?{maxWidth:"min(".concat(A,"px, 100%)"),maxHeight:"min(".concat(T,"px, 100%)")}:{maxWidth:"100%",maxHeight:"100%"},...C,...W},...H,alt:h.alt,sizes:O,srcSet:$,src:h.src}),M!==a.P9&&i.createElement("div",{className:s(eb(a.Bv))},M===a.Qo&&((null==m?void 0:m.iconLoading)?m.iconLoading():i.createElement(ei,{className:c(s(a.jR),s(eb(a.Qo)))})),M===a.I1&&((null==m?void 0:m.iconError)?m.iconError():i.createElement(ea,{className:c(s(a.jR),s(eb(a.I1)))}))))}let ey=i.forwardRef(function(e,t){let{className:n,children:r,...l}=e,a=i.useRef(null);return i.createElement(T,{nodeRef:a},i.createElement("div",{ref:em(t,a),className:c(s("root"),n),...l},r))});function ex(e,t,n,r,l){i.useEffect(()=>l?()=>{}:m(e(a.qq,t),e(a.RK,n),e(a.tu,r),e(a.tD,r),e(a.s,r)),[e,t,n,r,l])}!function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL",e[e.ANIMATION=3]="ANIMATION"}(r||(r={})),function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL"}(l||(l={}));let eC=f("container"),eM=i.createContext(null),ek=p("useController","ControllerContext",eM),eN=S(a.Kv,function(e){var t;let{children:n,...o}=e,{carousel:u,animation:h,controller:f,on:p,styles:v,render:b}=o,{closeOnPullUp:E,closeOnPullDown:w,preventDefaultWheelX:x,preventDefaultWheelY:C}=f,[M,k]=i.useState(),N=Z(),R=U(),[L,I]=i.useState(r.NONE),P=i.useRef(0),S=i.useRef(0),D=i.useRef(1),{registerSensors:F,subscribeSensors:z}=ev(),{subscribe:T,publish:$}=_(),O=ed(),W=ed(),K=ed(),{containerRef:H,setContainerRef:V,containerRect:Y}=es(),X=em(function(e){let{preventDefaultWheelX:t,preventDefaultWheelY:n}=e,r=i.useRef(null),l=eh(e=>{let r=Math.abs(e.deltaX)>Math.abs(e.deltaY);(r&&t||!r&&n||e.ctrlKey)&&e.preventDefault()});return i.useCallback(e=>{var t;e?e.addEventListener("wheel",l,{passive:!1}):null==(t=r.current)||t.removeEventListener("wheel",l),r.current=e},[l])}({preventDefaultWheelX:x,preventDefaultWheelY:C}),V),j=i.useRef(null),B=em(j,void 0),{getOwnerDocument:J}=A(),q=ep(),G=e=>(q?-1:1)*("number"==typeof e?e:1),ee=eh(()=>{var e;return null==(e=H.current)?void 0:e.focus()}),et=eh(()=>o),en=eh(()=>N),er=i.useCallback(e=>$(a.zn,e),[$]),el=i.useCallback(e=>$(a.Nc,e),[$]),ei=i.useCallback(()=>$(a.C),[$]),ea=e=>!(u.finite&&(G(e)>0&&0===N.currentIndex||0>G(e)&&N.currentIndex===N.slides.length-1)),eo=e=>{var t;P.current=e,null==(t=H.current)||t.style.setProperty(d("swipe_offset"),"".concat(Math.round(e),"px"))},eu=e=>{var t,n;S.current=e,D.current=Math.min(Math.max(g(1-(w&&e>0?e:E&&e<0?-e:0)/60*.5,2),.5),1),null==(t=H.current)||t.style.setProperty(d("pull_offset"),"".concat(Math.round(e),"px")),null==(n=H.current)||n.style.setProperty(d("pull_opacity"),"".concat(D.current))},{prepareAnimation:ef}=ec(j,(e,t,n)=>{if(j.current&&Y)return{keyframes:[{transform:"translate(0, ".concat(e.rect.y-t.y+n.y,"px)"),opacity:e.opacity},{transform:"translate(0, 0)",opacity:1}],duration:e.duration,easing:h.easing.fade}}),eg=(e,t)=>{if(E||w){eu(e);let n=0;j.current&&(n=h.fade*(t?2:1),ef({rect:j.current.getBoundingClientRect(),opacity:D.current,duration:n})),K(()=>{eu(0),I(r.NONE)},n),I(r.ANIMATION),t||ei()}},{prepareAnimation:eb,isAnimationPlaying:eE}=ec(j,(e,t,n)=>{var r;if(j.current&&Y&&(null==(r=N.animation)?void 0:r.duration)){let r=y(u.spacing),l=(r.percent?r.percent*Y.width/100:r.pixel)||0;return{keyframes:[{transform:"translate(".concat(G(N.globalIndex-e.index)*(Y.width+l)+e.rect.x-t.x+n.x,"px, 0)")},{transform:"translate(0, 0)"}],duration:N.animation.duration,easing:N.animation.easing}}}),ew=eh(e=>{var t,n;let l=e.offset||0,i=l?h.swipe:null!=(t=h.navigation)?t:h.swipe,o=l||eE()?h.easing.swipe:h.easing.navigation,{direction:u}=e,c=null!=(n=e.count)?n:1,s=r.ANIMATION,d=i*c;if(!u){let t=null==Y?void 0:Y.width,n=e.duration||0,r=t?i/t*Math.abs(l):i;0!==c?(n<r?d=d/r*Math.max(n,r/5):t&&(d=i/t*(t-Math.abs(l))),u=G(l)>0?a.zn:a.Nc):d=i/2}let f=0;u===a.zn?ea(G(1))?f=-c:(s=r.NONE,d=i):u===a.Nc&&(ea(G(-1))?f=c:(s=r.NONE,d=i)),W(()=>{eo(0),I(r.NONE)},d=Math.round(d)),j.current&&eb({rect:j.current.getBoundingClientRect(),index:N.globalIndex}),I(s),$(a.sI,{type:"swipe",increment:f,duration:d,easing:o})});i.useEffect(()=>{var e,t;(null==(e=N.animation)?void 0:e.increment)&&(null==(t=N.animation)?void 0:t.duration)&&O(()=>R({type:"swipe",increment:0}),N.animation.duration)},[N.animation,R,O]);let ey=[z,ea,(null==Y?void 0:Y.width)||0,h.swipe,()=>I(r.SWIPE),e=>eo(e),(e,t)=>ew({offset:e,duration:t,count:1}),e=>ew({offset:e,count:0})],ek=[()=>{w&&I(r.PULL)},e=>eu(e),e=>eg(e),e=>eg(e,!0)];!function(e,t,n,r,a,o,u,c,s,d,h,f,m,p,v){let{disableSwipeNavigation:g}=e,b=i.useRef(0),E=i.useRef([]),w=i.useRef(void 0),y=i.useRef(0),x=i.useRef(l.NONE),C=i.useCallback(e=>{w.current===e.pointerId&&(w.current=void 0,x.current=l.NONE);let t=E.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),M=i.useCallback(e=>{C(e),e.persist(),E.current.push(e)},[C]),k=eh(e=>{M(e)}),N=(e,t)=>h&&e>t||d&&e<-t,R=eh(e=>{if(E.current.find(t=>t.pointerId===e.pointerId)&&w.current===e.pointerId){let e=Date.now()-y.current,t=b.current;x.current===l.SWIPE?Math.abs(t)>.3*r||Math.abs(t)>5&&e<a?c(t,e):s(t):x.current===l.PULL&&(N(t,60)?p(t,e):v(t)),b.current=0,x.current=l.NONE}C(e)});ex(t,k,eh(e=>{let t=E.current.find(t=>t.pointerId===e.pointerId);if(t){let r=w.current===e.pointerId;if(0===e.buttons)return void(r&&0!==b.current?R(e):C(t));let i=e.clientX-t.clientX,a=e.clientY-t.clientY;if(void 0===w.current){let t=t=>{M(e),w.current=e.pointerId,y.current=Date.now(),x.current=t};Math.abs(i)>Math.abs(a)&&Math.abs(i)>30&&n(i)?g||(t(l.SWIPE),o()):Math.abs(a)>Math.abs(i)&&N(a,30)&&(t(l.PULL),f())}else r&&(x.current===l.SWIPE?(b.current=i,u(i)):x.current===l.PULL&&(b.current=a,m(a)))}}),R)}(f,...ey,E,w,...ek),function(e,t,n,l,o,u,c,s,d){let h=i.useRef(0),f=i.useRef(0),m=i.useRef(void 0),p=i.useRef(void 0),v=i.useRef(0),g=i.useRef(void 0),b=i.useRef(0),{setTimeout:E,clearTimeout:w}=Q(),y=i.useCallback(()=>{m.current&&(w(m.current),m.current=void 0)},[w]),x=i.useCallback(()=>{p.current&&(w(p.current),p.current=void 0)},[w]),C=eh(()=>{e!==r.SWIPE&&(h.current=0,b.current=0,y(),x())});i.useEffect(C,[e,C]);let M=eh(e=>{p.current=void 0,h.current===e&&d(h.current)}),k=eh(t=>{if(t.ctrlKey||Math.abs(t.deltaY)>Math.abs(t.deltaX))return;let i=e=>{v.current=e,w(g.current),g.current=e>0?E(()=>{v.current=0,g.current=void 0},300):void 0};if(e===r.NONE){if(Math.abs(t.deltaX)<=1.2*Math.abs(v.current))return void i(t.deltaX);if(!n(-t.deltaX))return;if(f.current+=t.deltaX,y(),Math.abs(f.current)>30)f.current=0,i(0),b.current=Date.now(),u();else{let e=f.current;m.current=E(()=>{m.current=void 0,e===f.current&&(f.current=0)},o)}}else if(e===r.SWIPE){let e=h.current-t.deltaX;if(h.current=e=Math.min(Math.abs(e),l)*Math.sign(e),c(e),x(),Math.abs(e)>.2*l){i(t.deltaX),s(e,Date.now()-b.current);return}p.current=E(()=>M(e),2*o)}else i(t.deltaX)});i.useEffect(()=>t(a.j7,k),[t,k])}(L,...ey);let eN=eh(()=>{f.focus&&J().querySelector(".".concat(s(a.kJ)," .").concat(s(eC())))&&ee()});i.useEffect(eN,[eN]);let eR=eh(()=>{var e;null==(e=p.view)||e.call(p,{index:N.currentIndex})});i.useEffect(eR,[N.globalIndex,eR]),i.useEffect(()=>m(T(a.zn,e=>ew({direction:a.zn,...e})),T(a.Nc,e=>ew({direction:a.Nc,...e})),T(a.sI,e=>R(e))),[T,ew,R]);let eL=i.useMemo(()=>({prev:er,next:el,close:ei,focus:ee,slideRect:Y?function(e,t){let n=y(t),r=void 0!==n.percent?e.width/100*n.percent:n.pixel;return{width:Math.max(e.width-2*r,0),height:Math.max(e.height-2*r,0)}}(Y,u.padding):{width:0,height:0},containerRect:Y||{width:0,height:0},subscribeSensors:z,containerRef:H,setCarouselRef:B,toolbarWidth:M,setToolbarWidth:k}),[er,el,ei,ee,z,Y,H,B,M,k,u.padding]);return i.useImperativeHandle(f.ref,()=>({prev:er,next:el,close:ei,focus:ee,getLightboxProps:et,getLightboxState:en}),[er,el,ei,ee,et,en]),i.createElement("div",{ref:X,className:c(s(eC()),s(a.tC)),style:{...L===r.SWIPE?{[d("swipe_offset")]:"".concat(Math.round(P.current),"px")}:null,...L===r.PULL?{[d("pull_offset")]:"".concat(Math.round(S.current),"px"),[d("pull_opacity")]:"".concat(D.current)}:null,..."none"!==f.touchAction?{[d("controller_touch_action")]:f.touchAction}:null,...v.container},...f.aria?{role:"region","aria-live":"polite","aria-roledescription":"carousel"}:null,tabIndex:-1,...F},Y&&i.createElement(eM.Provider,{value:eL},n,null==(t=b.controls)?void 0:t.call(b)))});function eR(e){return h(a.mY,e)}function eL(e){return h("slide",e)}function eI(e){var t,n,r,l;let o,{slide:u,offset:d}=e,h=i.useRef(null),{currentIndex:f}=Z(),{slideRect:m,close:p,focus:v}=ek(),{render:g,carousel:{imageFit:E,imageProps:w},on:{click:y},controller:{closeOnBackdropClick:x},styles:{slide:C}}=K(),{getOwnerDocument:M}=A(),k=0!==d;return i.useEffect(()=>{var e;k&&(null==(e=h.current)?void 0:e.contains(M().activeElement))&&v()},[k,v,M]),i.createElement("div",{ref:h,className:c(s(eL()),!k&&s(eL("current")),s(a.tC)),...{inert:I?k:k?"":void 0},onClick:e=>{let t=h.current,n=e.target instanceof HTMLElement?e.target:void 0;x&&n&&t&&(n===t||Array.from(t.children).find(e=>e===n)&&n.classList.contains(s(a.pc)))&&p()},style:C,role:"region","aria-roledescription":"slide"},(!(o=null==(t=g.slide)?void 0:t.call(g,{slide:u,offset:d,rect:m}))&&b(u)&&(o=i.createElement(ew,{slide:u,offset:d,render:g,rect:m,imageFit:E,imageProps:w,onClick:k?void 0:()=>null==y?void 0:y({index:f})})),o?i.createElement(i.Fragment,null,null==(n=g.slideHeader)?void 0:n.call(g,{slide:u}),(null!=(r=g.slideContainer)?r:e=>{let{children:t}=e;return t})({slide:u,children:o}),null==(l=g.slideFooter)?void 0:l.call(g,{slide:u})):null))}function eP(){let e=K().styles.slide;return i.createElement("div",{className:s("slide"),style:e})}let eS=S(a.mY,function(e){let{carousel:t}=e,{slides:n,currentIndex:r,globalIndex:l}=Z(),{setCarouselRef:a}=ek(),o=y(t.spacing),u=y(t.padding),h=L(t,n,1),f=[];if(C(n))for(let e=r-h;e<=r+h;e+=1){let i=M(n,e),a=l-r+e,o=t.finite&&(e<0||e>n.length-1);f.push(o?{key:a}:{key:["".concat(a),N(i)].filter(Boolean).join("|"),offset:e-r,slide:i})}return i.createElement("div",{ref:a,className:c(s(eR()),f.length>0&&s(eR("with_slides"))),style:{["".concat(d(eR("slides_count")))]:f.length,["".concat(d(eR("spacing_px")))]:o.pixel||0,["".concat(d(eR("spacing_percent")))]:o.percent||0,["".concat(d(eR("padding_px")))]:u.pixel||0,["".concat(d(eR("padding_percent")))]:u.percent||0}},f.map(e=>{let{key:t,slide:n,offset:r}=e;return n?i.createElement(eI,{key:t,slide:n,offset:r}):i.createElement(eP,{key:t})}))});function eD(){let{carousel:e}=K(),{slides:t,currentIndex:n}=Z();return{prevDisabled:0===t.length||e.finite&&0===n,nextDisabled:0===t.length||e.finite&&n===t.length-1}}function eF(e){var t;let n=ep(),{publish:r}=_(),{animation:l}=K(),{prevDisabled:o,nextDisabled:u}=eD(),c=(null!=(t=l.navigation)?t:l.swipe)/2,s=eg(()=>r(a.zn),c),d=eg(()=>r(a.Nc),c),h=eh(e=>{switch(e.key){case a.or:r(a.C);break;case a.A9:(n?u:o)||(n?d:s)();break;case a.jn:(n?o:u)||(n?s:d)()}});i.useEffect(()=>e(a.uF,h),[e,h])}function ez(e){let{label:t,icon:n,renderIcon:r,action:l,onClick:a,disabled:o,style:u}=e;return i.createElement(q,{label:t,icon:n,renderIcon:r,className:s("navigation_".concat(l)),disabled:o,onClick:a,style:u,...function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=i.useRef(!1);return eo(()=>{t&&n.current&&(n.current=!1,e())},[t,e]),{onFocus:i.useCallback(()=>{n.current=!0},[]),onBlur:i.useCallback(()=>{n.current=!1},[])}}(ek().focus,o)})}let eA=S(a.Ky,function(e){let{render:{buttonPrev:t,buttonNext:n,iconPrev:r,iconNext:l},styles:o}=e,{prev:u,next:c,subscribeSensors:s}=ek(),{prevDisabled:d,nextDisabled:h}=eD();return eF(s),i.createElement(i.Fragment,null,t?t():i.createElement(ez,{label:"Previous",action:a.zn,icon:er,renderIcon:r,style:o.navigationPrev,disabled:d,onClick:u}),n?n():i.createElement(ez,{label:"Next",action:a.Nc,icon:el,renderIcon:l,style:o.navigationNext,disabled:h,onClick:c}))}),eT=s(a.HR),e$=s(a.vg);function e_(e,t,n){let r=window.getComputedStyle(e),l=n?"padding-left":"padding-right",i=n?r.paddingLeft:r.paddingRight,a=e.style.getPropertyValue(l);return e.style.setProperty(l,"".concat((w(i)||0)+t,"px")),()=>{a?e.style.setProperty(l,a):e.style.removeProperty(l)}}let eO=S(a.b6,function(e){let{noScroll:{disabled:t},children:n}=e,r=ep(),{getOwnerDocument:l,getOwnerWindow:a}=A();return i.useEffect(()=>{if(t)return()=>{};let e=[],n=a(),{body:i,documentElement:o}=l(),u=Math.round(n.innerWidth-o.clientWidth);if(u>0){e.push(e_(i,u,r));let t=i.getElementsByTagName("*");for(let l=0;l<t.length;l+=1){let i=t[l];"style"in i&&"fixed"===n.getComputedStyle(i).getPropertyValue("position")&&!i.classList.contains(e$)&&e.push(e_(i,u,r))}}return i.classList.add(eT),()=>{i.classList.remove(eT),e.forEach(e=>e())}},[r,t,l,a]),i.createElement(i.Fragment,null,n)});function eW(e,t,n){let r=e.getAttribute(t);return e.setAttribute(t,n),()=>{r?e.setAttribute(t,r):e.removeAttribute(t)}}let eK=S(a.kJ,function(e){var t;let{children:n,animation:r,styles:l,className:u,on:f,portal:m,close:p}=e,[v,g]=i.useState(!1),[b,E]=i.useState(!1),w=i.useRef([]),y=i.useRef(null),{setTimeout:x}=Q(),{subscribe:C}=_(),M=eu()?0:r.fade;i.useEffect(()=>(g(!0),()=>{g(!1),E(!1)}),[]);let k=eh(()=>{w.current.forEach(e=>e()),w.current=[]}),N=eh(()=>{var e;E(!1),k(),null==(e=f.exiting)||e.call(f),x(()=>{var e;null==(e=f.exited)||e.call(f),p()},M)});i.useEffect(()=>C(a.C,N),[C,N]);let R=eh(e=>{var t,n,r;e.scrollTop,E(!0),null==(t=f.entering)||t.call(f);let l=null!=(r=null==(n=e.parentNode)?void 0:n.children)?r:[];for(let t=0;t<l.length;t+=1){let n=l[t];-1===["TEMPLATE","SCRIPT","STYLE"].indexOf(n.tagName)&&n!==e&&(w.current.push(eW(n,"inert","")),w.current.push(eW(n,"aria-hidden","true")))}w.current.push(()=>{var e,t;null==(t=null==(e=y.current)?void 0:e.focus)||t.call(e)}),x(()=>{var e;null==(e=f.entered)||e.call(f)},M)}),L=i.useCallback(e=>{e?R(e):k()},[R,k]);return v?(0,o.createPortal)(i.createElement(ey,{ref:L,className:c(u,s(h(a.kJ,void 0)),s(a.vg),b&&s((t="open",h(a.kJ,t)))),"aria-modal":!0,role:"dialog","aria-live":"polite","aria-roledescription":"lightbox",style:{...r.fade!==P.animation.fade?{[d("fade_animation_duration")]:"".concat(M,"ms")}:null,...r.easing.fade!==P.animation.easing.fade?{[d("fade_animation_timing_function")]:r.easing.fade}:null,...l.root},onFocus:e=>{y.current||(y.current=e.relatedTarget)}},n),m.root||document.body):null}),eH=S(a.OT,function(e){let{children:t}=e;return i.createElement(i.Fragment,null,t)}),eV=S(a.pq,function(e){let{toolbar:{buttons:t},render:{buttonClose:n,iconClose:r},styles:l}=e,{close:o,setToolbarWidth:u}=ek(),{setContainerRef:c,containerRect:d}=es();eo(()=>{u(null==d?void 0:d.width)},[u,null==d?void 0:d.width]);let f=()=>n?n():i.createElement(q,{key:a.C,label:"Close",icon:en,renderIcon:r,onClick:o});return i.createElement("div",{ref:c,style:l.toolbar,className:s(h(a.pq,void 0))},null==t?void 0:t.map(e=>e===a.C?f():e))});function eZ(e){let{carousel:t,animation:n,render:r,toolbar:l,controller:o,noScroll:u,on:c,plugins:s,slides:d,index:h,...f}=e,{animation:m,carousel:p,render:v,toolbar:g,controller:b,noScroll:E,on:y,slides:x,index:C,plugins:M,...k}=P,{config:N,augmentation:R}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=e,l=e=>{let t=[...r];for(;t.length>0;){let n=t.pop();if((null==n?void 0:n.module.name)===e)return!0;(null==n?void 0:n.children)&&t.push(...n.children)}return!1},i=(e,t)=>{if(""===e){r=[D(t,r)];return}r=F(r,e,e=>[D(t,[e])])},o=(e,t)=>{r=F(r,e,e=>[D(e.module,[D(t,e.children)])])},u=(e,t,n)=>{r=F(r,e,e=>{var r;return[D(e.module,[...n?[D(t)]:[],...null!=(r=e.children)?r:[],...n?[]:[D(t)]])]})},c=(e,t,n)=>{r=F(r,e,e=>[...n?[D(t)]:[],e,...n?[]:[D(t)]])},s=e=>{o(a.Kv,e)},d=(e,t)=>{r=F(r,e,e=>[D(t,e.children)])},h=e=>{r=F(r,e,e=>e.children)},f=e=>{n.push(e)};return t.forEach(e=>{e({contains:l,addParent:i,append:o,addChild:u,addSibling:c,addModule:s,replace:d,remove:h,augment:f})}),{config:r,augmentation:e=>n.reduce((e,t)=>t(e),e)}}([D(eK,[D(eO,[D(eN,[D(eS),D(eV),D(eA)])])])],s||M),L=R({animation:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{easing:n,...r}=e,{easing:l,...i}=t;return{easing:{...n,...l},...r,...i}}(m,n),carousel:{...p,...t},render:{...v,...r},toolbar:{...g,...l},controller:{...b,...o},noScroll:{...E,...u},on:{...y,...c},...k,...f});return L.open?i.createElement(H,{...L},i.createElement(j,{slides:d||x,index:w(h||C)},i.createElement(J,null,i.createElement(O,null,function e(t,n){var r;return i.createElement(t.module.component,{key:t.module.name,...n},null==(r=t.children)?void 0:r.map(t=>e(t,n)))}(D(eH,N),L))))):null}},8561:()=>{},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:s="",children:d,iconNode:h,...f}=e;return(0,r.createElement)("svg",{ref:t,...c,width:l,height:l,stroke:n,strokeWidth:a?24*Number(i)/Number(l):i,className:o("lucide",s),...!d&&!u(f)&&{"aria-hidden":"true"},...f},[...h.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...c}=n;return(0,r.createElement)(s,{ref:i,iconNode:t,className:o("lucide-".concat(l(a(e))),"lucide-".concat(e),u),...c})});return n.displayName=a(e),n}}}]);