"use strict";exports.id=30,exports.ids=[30],exports.modules={11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},17019:(e,t,n)=>{n.d(t,{A3x:()=>i,JXP:()=>f,Ohp:()=>u,Pum:()=>d,YrT:()=>a,kGk:()=>s,mEP:()=>l,pHD:()=>c,y3G:()=>o});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"},child:[]},{tag:"line",attr:{x1:"12",y1:"8",x2:"12",y2:"12"},child:[]},{tag:"line",attr:{x1:"12",y1:"16",x2:"12.01",y2:"16"},child:[]}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"},child:[]},{tag:"polyline",attr:{points:"22 4 12 14.01 9 11.01"},child:[]}]})(e)}function a(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 6 9 17 4 12"},child:[]}]})(e)}function u(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"},child:[]},{tag:"polyline",attr:{points:"12 6 12 12 16 14"},child:[]}]})(e)}function c(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"},child:[]},{tag:"polyline",attr:{points:"22,6 12,13 2,6"},child:[]}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"},child:[]}]})(e)}function s(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"},child:[]},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"},child:[]}]})(e)}function d(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"18",cy:"5",r:"3"},child:[]},{tag:"circle",attr:{cx:"6",cy:"12",r:"3"},child:[]},{tag:"circle",attr:{cx:"18",cy:"19",r:"3"},child:[]},{tag:"line",attr:{x1:"8.59",y1:"13.51",x2:"15.42",y2:"17.49"},child:[]},{tag:"line",attr:{x1:"15.41",y1:"6.51",x2:"8.59",y2:"10.49"},child:[]}]})(e)}function f(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"},child:[]},{tag:"circle",attr:{cx:"12",cy:"7",r:"4"},child:[]}]})(e)}},17257:(e,t,n)=>{n.d(t,{J1z:()=>i,YQq:()=>a,atu:()=>o});var r=n(90296);function o(e){return(0,r.k5)({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"},child:[]}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8"},child:[]}]})(e)}function a(e){return(0,r.k5)({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001q.044.06.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1 1 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0"},child:[]}]})(e)}},69101:(e,t,n)=>{n.d(t,{bm:()=>ti,UC:()=>tr,hJ:()=>tn,ZL:()=>tt,bL:()=>te,hE:()=>to});var r,o,i,a=n(43210),u=n.t(a,2);function c(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var l=n(98599),s=n(60687),d=globalThis?.document?a.useLayoutEffect:()=>{},f=u[" useId ".trim().toString()]||(()=>void 0),p=0;function v(e){let[t,n]=a.useState(f());return d(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var m=u[" useInsertionEffect ".trim().toString()]||d,h=(Symbol("RADIX:SYNC_STATE"),n(51215)),g=n(8730),y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,g.TL)(`Primitive.${t}`),r=a.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function E(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var b="dismissableLayer.update",w=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=a.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:u,onInteractOutside:d,onDismiss:f,...p}=e,v=a.useContext(w),[m,h]=a.useState(null),g=m?.ownerDocument??globalThis?.document,[,x]=a.useState({}),N=(0,l.s)(t,e=>h(e)),L=Array.from(v.layers),[R]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),S=L.indexOf(R),O=m?L.indexOf(m):-1,M=v.layersWithOutsidePointerEventsDisabled.size>0,D=O>=S,T=function(e,t=globalThis?.document){let n=E(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){C("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));D&&!n&&(i?.(e),d?.(e),e.defaultPrevented||f?.())},g),P=function(e,t=globalThis?.document){let n=E(e),r=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!r.current&&C("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(u?.(e),d?.(e),e.defaultPrevented||f?.())},g);return!function(e,t=globalThis?.document){let n=E(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{O===v.layers.size-1&&(r?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},g),a.useEffect(()=>{if(m)return n&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(o=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(m)),v.layers.add(m),k(),()=>{n&&1===v.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=o)}},[m,g,n,v]),a.useEffect(()=>()=>{m&&(v.layers.delete(m),v.layersWithOutsidePointerEventsDisabled.delete(m),k())},[m,v]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,s.jsx)(y.div,{...p,ref:N,style:{pointerEvents:M?D?"auto":"none":void 0,...e.style},onFocusCapture:c(e.onFocusCapture,P.onFocusCapture),onBlurCapture:c(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:c(e.onPointerDownCapture,T.onPointerDownCapture)})});function k(){let e=new CustomEvent(b);document.dispatchEvent(e)}function C(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&o.addEventListener(e,t,{once:!0}),r)o&&h.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}x.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(w),r=a.useRef(null),o=(0,l.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(y.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var N="focusScope.autoFocusOnMount",L="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},S=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...u}=e,[c,d]=a.useState(null),f=E(o),p=E(i),v=a.useRef(null),m=(0,l.s)(t,e=>d(e)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(h.paused||!c)return;let t=e.target;c.contains(t)?v.current=t:D(v.current,{select:!0})},t=function(e){if(h.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||D(v.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&D(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,h.paused]),a.useEffect(()=>{if(c){T.add(h);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(N,R);c.addEventListener(N,f),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(D(r,{select:t}),document.activeElement!==n)return}(O(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&D(c))}return()=>{c.removeEventListener(N,f),setTimeout(()=>{let t=new CustomEvent(L,R);c.addEventListener(L,p),c.dispatchEvent(t),t.defaultPrevented||D(e??document.body,{select:!0}),c.removeEventListener(L,p),T.remove(h)},0)}}},[c,f,p,h]);let g=a.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=O(e);return[M(t,e),M(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&D(i,{select:!0})):(e.preventDefault(),n&&D(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,s.jsx)(y.div,{tabIndex:-1,...u,ref:m,onKeyDown:g})});function O(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function M(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function D(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}S.displayName="FocusScope";var T=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=P(e,t)).unshift(t)},remove(t){e=P(e,t),e[0]?.resume()}}}();function P(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var A=a.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=a.useState(!1);d(()=>i(!0),[]);let u=n||o&&globalThis?.document?.body;return u?h.createPortal((0,s.jsx)(y.div,{...r,ref:t}),u):null});A.displayName="Portal";var j=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=a.useState(),i=a.useRef(null),u=a.useRef(e),c=a.useRef("none"),[l,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>n[e][t]??e,t));return a.useEffect(()=>{let e=I(i.current);c.current="mounted"===l?e:"none"},[l]),d(()=>{let t=i.current,n=u.current;if(n!==e){let r=c.current,o=I(t);e?s("MOUNT"):"none"===o||t?.display==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),d(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,n=n=>{let o=I(i.current).includes(n.animationName);if(n.target===r&&o&&(s("ANIMATION_END"),!u.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},o=e=>{e.target===r&&(c.current=I(i.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:a.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),i=(0,l.s)(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?a.cloneElement(o,{ref:i}):null};function I(e){return e?.animationName||"none"}j.displayName="Presence";var W=0;function F(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var _=function(){return(_=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function B(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var $=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),U="width-before-scroll-bar";function H(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var z="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,K=new WeakMap;function Y(e){return e}var X=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=Y),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return i.options=_({async:!0,ssr:!1},e),i}(),q=function(){},Z=a.forwardRef(function(e,t){var n,r,o,i,u=a.useRef(null),c=a.useState({onScrollCapture:q,onWheelCapture:q,onTouchMoveCapture:q}),l=c[0],s=c[1],d=e.forwardProps,f=e.children,p=e.className,v=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,E=e.noIsolation,b=e.inert,w=e.allowPinchZoom,x=e.as,k=e.gapMode,C=B(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[u,t],r=function(e){return n.forEach(function(t){return H(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,z(function(){var e=K.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||H(e,null)}),r.forEach(function(e){t.has(e)||H(e,o)})}K.set(i,n)},[n]),i),L=_(_({},C),l);return a.createElement(a.Fragment,null,m&&a.createElement(g,{sideCar:X,removeScrollBar:v,shards:h,noRelative:y,noIsolation:E,inert:b,setCallbacks:s,allowPinchZoom:!!w,lockRef:u,gapMode:k}),d?a.cloneElement(a.Children.only(f),_(_({},L),{ref:N})):a.createElement(void 0===x?"div":x,_({},L,{className:p,ref:N}),f))});Z.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Z.classNames={fullWidth:U,zeroRight:$};var V=function(e){var t=e.sideCar,n=B(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,_({},n))};V.isSideCarExport=!0;var G=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},J=function(){var e=G();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Q=function(){var e=J();return function(t){return e(t.styles,t.dynamic),null}},ee={left:0,top:0,right:0,gap:0},et=function(e){return parseInt(e||"",10)||0},en=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[et(n),et(r),et(o)]},er=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ee;var t=en(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},eo=Q(),ei="data-scroll-locked",ea=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(ei,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat($," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(U," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat($," .").concat($," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(U," .").concat(U," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ei,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},eu=function(){var e=parseInt(document.body.getAttribute(ei)||"0",10);return isFinite(e)?e:0},ec=function(){a.useEffect(function(){return document.body.setAttribute(ei,(eu()+1).toString()),function(){var e=eu()-1;e<=0?document.body.removeAttribute(ei):document.body.setAttribute(ei,e.toString())}},[])},el=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;ec();var i=a.useMemo(function(){return er(o)},[o]);return a.createElement(eo,{styles:ea(i,!t,o,n?"":"!important")})},es=!1;if("undefined"!=typeof window)try{var ed=Object.defineProperty({},"passive",{get:function(){return es=!0,!0}});window.addEventListener("test",ed,ed),window.removeEventListener("test",ed,ed)}catch(e){es=!1}var ef=!!es&&{passive:!1},ep=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ev=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),em(e,r)){var o=eh(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},em=function(e,t){return"v"===e?ep(t,"overflowY"):ep(t,"overflowX")},eh=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eg=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=a*r,c=n.target,l=t.contains(c),s=!1,d=u>0,f=0,p=0;do{if(!c)break;var v=eh(e,c),m=v[0],h=v[1]-v[2]-a*m;(m||h)&&em(e,c)&&(f+=h,p+=m);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-u>p)&&(s=!0),s},ey=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eE=function(e){return[e.deltaX,e.deltaY]},eb=function(e){return e&&"current"in e?e.current:e},ew=0,ex=[];let ek=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(ew++)[0],i=a.useState(Q)[0],u=a.useRef(e);a.useEffect(function(){u.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eb),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,i=ey(e),a=n.current,c="deltaX"in e?e.deltaX:a[0]-i[0],l="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ev(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ev(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var p=r.current||o;return eg(p,t,e,"h"===p?c:l,!0)},[]),l=a.useCallback(function(e){if(ex.length&&ex[ex.length-1]===i){var n="deltaY"in e?eE(e):ey(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(eb).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=ey(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,eE(t),t.target,c(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,ey(t),t.target,c(t,e.lockRef.current))},[]);a.useEffect(function(){return ex.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,ef),document.addEventListener("touchmove",l,ef),document.addEventListener("touchstart",d,ef),function(){ex=ex.filter(function(e){return e!==i}),document.removeEventListener("wheel",l,ef),document.removeEventListener("touchmove",l,ef),document.removeEventListener("touchstart",d,ef)}},[]);var v=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?a.createElement(el,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},X.useMedium(r),V);var eC=a.forwardRef(function(e,t){return a.createElement(Z,_({},e,{ref:t,sideCar:ek}))});eC.classNames=Z.classNames;var eN=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eL=new WeakMap,eR=new WeakMap,eS={},eO=0,eM=function(e){return e&&(e.host||eM(e.parentNode))},eD=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eM(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eS[n]||(eS[n]=new WeakMap);var i=eS[n],a=[],u=new Set,c=new Set(o),l=function(e){!e||u.has(e)||(u.add(e),l(e.parentNode))};o.forEach(l);var s=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,c=(eL.get(e)||0)+1,l=(i.get(e)||0)+1;eL.set(e,c),i.set(e,l),a.push(e),1===c&&o&&eR.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),u.clear(),eO++,function(){a.forEach(function(e){var t=eL.get(e)-1,o=i.get(e)-1;eL.set(e,t),i.set(e,o),t||(eR.has(e)||e.removeAttribute(r),eR.delete(e)),o||e.removeAttribute(n)}),--eO||(eL=new WeakMap,eL=new WeakMap,eR=new WeakMap,eS={})}},eT=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||eN(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),eD(r,o,n,"aria-hidden")):function(){return null}},eP="Dialog",[eA,ej]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),i=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,c=n?.[e]?.[i]||o,l=a.useMemo(()=>u,Object.values(u));return(0,s.jsx)(c.Provider,{value:l,children:r})};return u.displayName=t+"Provider",[u,function(n,u){let c=u?.[e]?.[i]||o,l=a.useContext(c);if(l)return l;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eP),[eI,eW]=eA(eP),eF=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:u=!0}=e,c=a.useRef(null),l=a.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,u]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return m(()=>{i.current=t},[t]),a.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),c=void 0!==e,l=c?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[l,a.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else i(t)},[c,e,i,u])]}({prop:r,defaultProp:o??!1,onChange:i,caller:eP});return(0,s.jsx)(eI,{scope:t,triggerRef:c,contentRef:l,contentId:v(),titleId:v(),descriptionId:v(),open:d,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};eF.displayName=eP;var e_="DialogTrigger";a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eW(e_,n),i=(0,l.s)(t,o.triggerRef);return(0,s.jsx)(y.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e4(o.open),...r,ref:i,onClick:c(e.onClick,o.onOpenToggle)})}).displayName=e_;var eB="DialogPortal",[e$,eU]=eA(eB,{forceMount:void 0}),eH=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=eW(eB,t);return(0,s.jsx)(e$,{scope:t,forceMount:n,children:a.Children.map(r,e=>(0,s.jsx)(j,{present:n||i.open,children:(0,s.jsx)(A,{asChild:!0,container:o,children:e})}))})};eH.displayName=eB;var ez="DialogOverlay",eK=a.forwardRef((e,t)=>{let n=eU(ez,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eW(ez,e.__scopeDialog);return i.modal?(0,s.jsx)(j,{present:r||i.open,children:(0,s.jsx)(eX,{...o,ref:t})}):null});eK.displayName=ez;var eY=(0,g.TL)("DialogOverlay.RemoveScroll"),eX=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eW(ez,n);return(0,s.jsx)(eC,{as:eY,allowPinchZoom:!0,shards:[o.contentRef],children:(0,s.jsx)(y.div,{"data-state":e4(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eq="DialogContent",eZ=a.forwardRef((e,t)=>{let n=eU(eq,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eW(eq,e.__scopeDialog);return(0,s.jsx)(j,{present:r||i.open,children:i.modal?(0,s.jsx)(eV,{...o,ref:t}):(0,s.jsx)(eG,{...o,ref:t})})});eZ.displayName=eq;var eV=a.forwardRef((e,t)=>{let n=eW(eq,e.__scopeDialog),r=a.useRef(null),o=(0,l.s)(t,n.contentRef,r);return a.useEffect(()=>{let e=r.current;if(e)return eT(e)},[]),(0,s.jsx)(eJ,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:c(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:c(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:c(e.onFocusOutside,e=>e.preventDefault())})}),eG=a.forwardRef((e,t)=>{let n=eW(eq,e.__scopeDialog),r=a.useRef(!1),o=a.useRef(!1);return(0,s.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;n.triggerRef.current?.contains(i)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eJ=a.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...u}=e,c=eW(eq,n),d=a.useRef(null),f=(0,l.s)(t,d);return a.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??F()),document.body.insertAdjacentElement("beforeend",e[1]??F()),W++,()=>{1===W&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),W--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,s.jsx)(x,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":e4(c.open),...u,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(e7,{titleId:c.titleId}),(0,s.jsx)(e9,{contentRef:d,descriptionId:c.descriptionId})]})]})}),eQ="DialogTitle",e0=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eW(eQ,n);return(0,s.jsx)(y.h2,{id:o.titleId,...r,ref:t})});e0.displayName=eQ;var e1="DialogDescription";a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eW(e1,n);return(0,s.jsx)(y.p,{id:o.descriptionId,...r,ref:t})}).displayName=e1;var e2="DialogClose",e5=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eW(e2,n);return(0,s.jsx)(y.button,{type:"button",...r,ref:t,onClick:c(e.onClick,()=>o.onOpenChange(!1))})});function e4(e){return e?"open":"closed"}e5.displayName=e2;var e8="DialogTitleWarning",[e6,e3]=function(e,t){let n=a.createContext(t),r=e=>{let{children:t,...r}=e,o=a.useMemo(()=>r,Object.values(r));return(0,s.jsx)(n.Provider,{value:o,children:t})};return r.displayName=e+"Provider",[r,function(r){let o=a.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(e8,{contentName:eq,titleName:eQ,docsSlug:"dialog"}),e7=({titleId:e})=>{let t=e3(e8),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},e9=({contentRef:e,descriptionId:t})=>{let n=e3("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return a.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},te=eF,tt=eH,tn=eK,tr=eZ,to=e0,ti=e5}};