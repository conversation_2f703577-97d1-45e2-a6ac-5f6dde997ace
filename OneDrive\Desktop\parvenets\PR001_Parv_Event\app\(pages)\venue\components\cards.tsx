"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import Filter from './filter';
import BookingModal from './booking-modal';
import { VenueData, VenueType, LocationType } from '../venualldummydata/venue_dummydata';
import VenueCard from './VenueCard';
import SortingDropdown from './SortingDropdown';
import { venueApi, VenueBookingFormData, Venue } from '@/lib/api/venue/venueApi';
import { toast } from 'react-toastify';

const Cards = () => {
  // State declarations
  const [currentPage, setCurrentPage] = useState(1);
  const [sortOption, setSortOption] = useState<string>("default");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVenue, setSelectedVenue] = useState<VenueData | null>(null);
  const [currentFilters, setCurrentFilters] = useState({
    searchTerm: '',
    venueTypes: [] as VenueType[],
    locations: [] as string[]
  });

  // API state
  const [venues, setVenues] = useState<VenueData[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [error, setError] = useState<string | null>(null);
  
  // Maximum 9 cards per page
  const itemsPerPage = 9;

  // Fetch venues from API
  const fetchVenues = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await venueApi.getVenues({
        page: 1,
        limit: 100, // Get all venues for client-side filtering
        search: currentFilters.searchTerm || undefined,
        venueType: currentFilters.venueTypes.length > 0 ? currentFilters.venueTypes[0] : undefined,
      });

      if (response.success && response.data.venues) {
        // Convert API venues to VenueData format
        const convertedVenues: VenueData[] = response.data.venues.map((venue: Venue) => ({
          _id: venue._id,
          name: venue.name,
          image: venue.image, // This will be a URL from S3
          venueType: venue.venueType,
          location: venue.location,
          capacity: venue.capacity,
          seats: venue.seats,
          isActive: venue.isActive,
          sortOrder: venue.sortOrder,
          createdAt: venue.createdAt,
          updatedAt: venue.updatedAt,
          // Legacy fields for backward compatibility
          id: parseInt(venue._id.slice(-3), 16) || 1,
          price: Math.floor(Math.random() * 100000) + 50000, // Random price for demo
          type: venue.venueType,
          zone: venue.location.split(',')[0] || venue.location
        }));

        setVenues(convertedVenues);
      } else {
        throw new Error('Failed to fetch venues');
      }
    } catch (err: any) {
      console.error('Error fetching venues:', err);
      setError(err.message || 'Failed to fetch venues');
      toast.error('Failed to load venues. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  }, [currentFilters.searchTerm, currentFilters.venueTypes]);

  // Fetch venues on component mount and when filters change
  useEffect(() => {
    fetchVenues();
  }, [fetchVenues]);
  
  // Use useMemo to calculate filtered and sorted data
  const filteredData = useMemo(() => {
    // Use only API data
    let result = [...venues];
    
    // Apply filters
    if (currentFilters.searchTerm) {
      result = result.filter(venue =>
        venue.name.toLowerCase().includes(currentFilters.searchTerm.toLowerCase()) ||
        venue.location.toLowerCase().includes(currentFilters.searchTerm.toLowerCase())
      );
    }

    if (currentFilters.venueTypes.length > 0) {
      result = result.filter(venue => currentFilters.venueTypes.includes(venue.venueType));
    }

    if (currentFilters.locations.length > 0) {
      result = result.filter(venue =>
        currentFilters.locations.includes(venue.location)
      );
    }
    
    // Apply sorting
    switch(sortOption) {
      case "price-low-high":
        result.sort((a, b) => (a.price || 0) - (b.price || 0));
        break;
      case "price-high-low":
        result.sort((a, b) => (b.price || 0) - (a.price || 0));
        break;
      case "capacity-low-high":
        result.sort((a, b) => a.capacity - b.capacity);
        break;
      case "capacity-high-low":
        result.sort((a, b) => b.capacity - a.capacity);
        break;
      default:
        // Default sorting (by id or _id)
        result.sort((a, b) => (a.id || 0) - (b.id || 0));
    }
    
    return result;
  }, [sortOption, currentFilters.searchTerm, currentFilters.venueTypes, currentFilters.locations, venues]);

  // Extract unique venue types and locations from API data
  const availableVenueTypes = useMemo(() => {
    const types = [...new Set(venues.map(venue => venue.venueType))];
    return types.sort();
  }, [venues]);

  const availableLocations = useMemo(() => {
    const locations = [...new Set(venues.map(venue => venue.location))];
    return locations.sort();
  }, [venues]);

  // Calculate total pages based on filtered data
  const totalPages = Math.max(1, Math.ceil(filteredData.length / itemsPerPage));
  
  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [currentFilters, sortOption]);
  
  // Get current items for the current page
  const currentItems = useMemo(() => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredData.slice(indexOfFirstItem, indexOfLastItem);
  }, [currentPage, filteredData, itemsPerPage]);
  
  // Handle filter changes - use useCallback to prevent recreation on every render
  const handleFilterChange = useCallback((filters: {
    searchTerm: string;
    venueTypes: VenueType[];
    locations: string[];
  }) => {
    setCurrentFilters(filters);
  }, []);
  
  // Handle booking
  const handleBookNow = (venue: VenueData) => {
    setSelectedVenue(venue);
    setIsModalOpen(true);
  };
  
  // Handle booking submission
  const handleBookingSubmit = async (formData: VenueBookingFormData) => {
    try {
      console.log('📝 Submitting booking form:', formData);
      const response = await venueApi.submitBooking(formData);

      if (response.success) {
        toast.success('🎉 Thank you for your booking request! We\'ll get back to you soon.');
        setIsModalOpen(false);

        // Optional: Refresh venues list
        fetchVenues();
      } else {
        throw new Error(response.message || 'Booking submission failed');
      }
    } catch (error: any) {
      console.error('❌ Booking submission error:', error);

      // Show specific error message based on error type
      let errorMessage = 'Failed to submit booking request. Please try again.';

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;

        switch (status) {
          case 400:
            errorMessage = data?.message || 'Invalid booking data. Please check your information.';
            break;
          case 404:
            errorMessage = 'Booking service not found. Please contact support.';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later or contact support.';
            break;
          default:
            errorMessage = data?.message || `Server error (${status}). Please try again.`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message) {
        // Other error
        errorMessage = error.message;
      }

      toast.error(`❌ ${errorMessage}`);
    }
  };
  
  // Handle page change
  const handlePageChange = (page: number) => {
    // Scroll to the top of the cards section
    const cardsSection = document.getElementById('venue-cards-section');
    if (cardsSection) {
      cardsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    
    // Update current page
    setCurrentPage(page);
  };
  
  // Calculate display range for results
  const indexOfFirstItem = (currentPage - 1) * itemsPerPage + 1;
  const indexOfLastItem = Math.min(currentPage * itemsPerPage, filteredData.length);

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Filter Section */}
        <div className="w-full md:w-1/4">
          {isLoading ? (
            <div className="bg-[#FEF2EB] p-4 rounded-lg">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-300 rounded"></div>
                  <div className="h-3 bg-gray-300 rounded"></div>
                  <div className="h-3 bg-gray-300 rounded"></div>
                </div>
              </div>
            </div>
          ) : (
            <Filter
              onFilterChange={handleFilterChange}
              availableVenueTypes={availableVenueTypes}
              availableLocations={availableLocations}
            />
          )}
        </div>
        
        {/* Cards Section */}
        <div id="venue-cards-section" className="w-full md:w-3/4">
          {/* Results Header */}
          <div className="flex justify-between items-center mb-6">
            <p className="text-sm text-gray-600">
              {filteredData.length > 0 ? (
                `Showing ${indexOfFirstItem}-${indexOfLastItem} of ${filteredData.length} results`
              ) : (
                "No results found"
              )}
            </p>
            
            {/* Sorting Dropdown */}
            <SortingDropdown 
              sortOption={sortOption} 
              onSortChange={setSortOption} 
            />
          </div>
          
          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FE904B]"></div>
              <span className="ml-3 text-gray-600">Loading venues...</span>
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-lg font-medium">Failed to load venues</p>
                <p className="text-sm text-gray-600 mt-2">{error}</p>
              </div>
              <button
                onClick={fetchVenues}
                className="px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Cards Grid */}
          {!isLoading && !error && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentItems.map((venue) => (
                <VenueCard
                  key={venue._id || venue.id}
                  venue={venue}
                  onBookNow={handleBookNow}
                />
              ))}
            </div>
          )}

          {/* No Results */}
          {!isLoading && !error && filteredData.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <p className="text-lg font-medium">No venues found</p>
                <p className="text-sm text-gray-600 mt-2">Try adjusting your search criteria</p>
              </div>
            </div>
          )}
          
          {/* Pagination */}
          {!isLoading && !error && totalPages > 1 && filteredData.length > 0 && (
            <div className="mt-8">
              <Pagination>
                <PaginationContent>
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={() => handlePageChange(currentPage - 1)}
                        className="cursor-pointer"
                      />
                    </PaginationItem>
                  )}
                  
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationNext 
                        onClick={() => handlePageChange(currentPage + 1)}
                        className="cursor-pointer"
                      />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </div>
      
      {/* Booking Modal */}
      {isModalOpen && selectedVenue && (
        <BookingModal 
          venue={selectedVenue}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleBookingSubmit}
        />
      )}
    </div>
  );
};

export default Cards;
