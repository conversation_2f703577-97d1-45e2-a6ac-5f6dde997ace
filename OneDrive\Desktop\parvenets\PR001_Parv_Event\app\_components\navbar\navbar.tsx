'use client'
import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import img1 from "@/public/image/logo.png";
import { IoMdAdd, IoMdMenu, IoMdClose } from "react-icons/io";
import { FaInstagram, FaYoutube } from "react-icons/fa";
import { FaSquareFacebook } from "react-icons/fa6";
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import QuoteModal from '../quote-modal/quote-modal';

const Navbar = () => {
  const pathname = usePathname();
  const [menuOpen, setMenuOpen] = useState(false);
  const [quoteModalOpen, setQuoteModalOpen] = useState(false);
  const [isMobileOrTablet, setIsMobileOrTablet] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Check if the screen is mobile or tablet size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobileOrTablet(window.innerWidth < 1024);
    };
    
    // Initial check
    checkScreenSize();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkScreenSize);
    
    // Cleanuppppp
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Add scroll event listener to detect when page is scrolled
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const tabs = [
    { name: "Home", path: "/" },
    { name: "About Us", path: "/about" },
    { name: "Services", path: "/services" },
    { name: "Venue", path: "/venue" },
    { name: "Gallary", path: "/gallary" },
    { name: "Blog", path: "/blog" },
    { name: "Contact", path: "/contact" },
  ];



  return (
    <>
      {/* Spacer div to prevent content from jumping when navbar becomes fixed */}
      <div className="h-[76px]"></div>
      
      <header className={`w-full border-b border-gray-200 bg-white shadow-sm fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${
        scrolled ? 'shadow-md' : ''
      }`}>
        <div className="max-w-7xl mx-auto px-4 py-3 flex justify-between items-center z-50">
          {/* Logo */}
          <Link href="/">
            <Image src={img1} alt="logo" width={60} height={60} className="cursor-pointer" />
          </Link>

          {/* Desktop Nav - only visible on large screens */}
          <nav className="hidden lg:flex gap-6 items-center">
            <ul className="flex gap-6 font-urbanist text-sm tracking-wider">
              {tabs.map((item, index) => (
                <li key={index}>
                  <Link
                    href={item.path}
                    className={`uppercase transition-colors duration-200 hover:text-[#FE904B] ${
                      pathname === item.path ? 'text-[#FE904B] font-semibold' : 'text-[#0D0D0D]'
                    }`}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>

          {/* Social & Button - only visible on large screens */}
          <div className="hidden lg:flex items-center gap-5">
            <div className="flex gap-3 text-lg">
              <FaSquareFacebook className="cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200" />
              <FaInstagram className="cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200" />
              <FaYoutube className="cursor-pointer text-[#0D0D0D] hover:text-[#FE904B] transition-colors duration-200" />
            </div>
            <button 
              onClick={() => setQuoteModalOpen(true)} 
              className="flex items-center py-2 px-4 border border-transparent hover:border-[#FE904B] hover:text-[#FE904B] text-sm transition"
            >
              GET A QUOTE <IoMdAdd className="ml-1" />
            </button>
          </div>

          {/* Mobile/Tablet Menu Toggle Button */}
          <button
            className="lg:hidden text-2xl text-[#0D0D0D]"
            onClick={() => setMenuOpen(!menuOpen)}
            aria-label="Toggle Menu"
          >
            {menuOpen ? <IoMdClose /> : <IoMdMenu />}
          </button>
        </div>

        {/* Mobile/Tablet Menu - slides in from the right */}
        <div 
          className={`fixed top-0 right-0 h-full w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${
            menuOpen ? 'translate-x-0' : 'translate-x-full'
          } lg:hidden`}
        >
          <div className="p-4 border-b flex justify-between items-center">
            <h3 className="font-medium">Menu</h3>
            <button 
              onClick={() => setMenuOpen(false)}
              className="text-2xl text-[#0D0D0D]"
              aria-label="Close menu"
            >
              <IoMdClose />
            </button>
          </div>
          
          <ul className="flex flex-col gap-4 p-4 text-sm font-urbanist">
            {tabs.map((item, index) => (
              <li key={index}>
                <Link
                  href={item.path}
                  className={`block py-2 uppercase transition-colors duration-200 hover:text-[#FE904B] ${
                    pathname === item.path ? 'text-[#FE904B] font-semibold' : 'text-[#0D0D0D]'
                  }`}
                  onClick={() => setMenuOpen(false)}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
          
          <div className="p-4 border-t">
            <div className="flex gap-3 text-lg mb-4 justify-center">
              <FaSquareFacebook className="cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]" />
              <FaInstagram className="cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]" />
              <FaYoutube className="cursor-pointer text-[#0D0D0D] hover:text-[#FE904B]" />
            </div>
            <button 
              onClick={() => {
                setQuoteModalOpen(true);
                setMenuOpen(false);
              }} 
              className="w-full flex items-center justify-center py-2 px-4 border border-[#FE904B] text-[#FE904B] hover:bg-[#FE904B] hover:text-white text-sm transition"
            >
              GET A QUOTE <IoMdAdd className="ml-1" />
            </button>
          </div>
        </div>
        
        {/* Overlay for mobile/tablet menu */}
        {menuOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setMenuOpen(false)}
          ></div>
        )}
      </header>

      {/* Quote Modal Component */}
      <QuoteModal
        isOpen={quoteModalOpen}
        onClose={() => setQuoteModalOpen(false)}
      />
    </>
  );
};

export default Navbar;
