(()=>{var e={};e.id=803,e.ids=[803],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5979:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(60687),a=r(43210),o=r(72837),i=r(30474),n=r(16189),l=r(17257),d=r(69587),c=r(81244);function p({blog:e}){let t=(0,n.useRouter)(),[r,p]=(0,a.useState)(!1);console.log("BlogCard - blog data:",{id:e._id,title:e.title,createdAt:e.createdAt,createdAtType:typeof e.createdAt,formattedDate:e.createdAt?(0,o._n)(e.createdAt):"No createdAt"});let x=e=>{e.stopPropagation(),p(!0)};return(0,s.jsxs)("div",{onClick:()=>{let r=e.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");t.push(`/blog/${e._id}/${r}`)},className:"bg-white border border-gray-200 shadow-lg hover:shadow-xl rounded-[16px] md:rounded-[20px] cursor-pointer p-0 flex flex-col relative overflow-hidden transition-all duration-300 group hover:scale-[1.02] md:hover:scale-105",children:[(0,s.jsxs)("div",{className:"relative w-full h-[200px] sm:h-[250px] md:h-[300px] rounded-t-[16px] md:rounded-t-[20px] overflow-hidden",children:[e.imageUrl?(0,s.jsx)(i.default,{src:e.imageUrl,alt:e.title,width:500,height:300,className:"object-cover w-full h-full",onError:e=>{e.target.style.display="none"}}):(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-orange-500 to-orange-700 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-white text-center p-4",children:[(0,s.jsx)("div",{className:"text-2xl md:text-3xl font-bold mb-2",children:"\uD83D\uDCDD"}),(0,s.jsx)("div",{className:"text-sm md:text-base font-medium",children:"Blog Post"})]})}),(0,s.jsx)("div",{className:"absolute top-3 left-3 md:top-4 md:left-4 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 md:px-3 shadow-md",children:(0,s.jsx)("span",{className:"text-gray-900 text-xs md:text-sm font-medium",children:e.createdAt?(0,o._n)(e.createdAt):"No date"})}),(0,s.jsx)("div",{className:"absolute top-3 right-3 md:top-4 md:right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,s.jsx)("div",{className:"bg-white/20 backdrop-blur-sm rounded-full p-1 md:p-2",children:(0,s.jsx)(l.J1z,{className:"w-4 h-4 md:w-5 md:h-5 text-white"})})})]}),(0,s.jsxs)("div",{className:"p-4 md:p-6 flex flex-col gap-3 md:gap-4 flex-1",children:[(0,s.jsxs)("div",{className:"relative group/category",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"bg-orange-500 text-white px-2 py-1 md:px-3 md:py-1 rounded-full text-xs md:text-sm",children:e.category[0]}),e.category.length>1&&(0,s.jsxs)("span",{className:"text-white/50 text-xs md:text-sm cursor-pointer",children:["+",e.category.length-1," more"]})]}),e.category.length>1&&(0,s.jsx)("div",{className:"absolute top-full left-0 mt-1 md:mt-2 bg-zinc-800 rounded-lg p-2 md:p-3 opacity-0 group-hover/category:opacity-100 transition-opacity duration-300 z-10 min-w-[150px] md:min-w-[200px]",children:(0,s.jsx)("div",{className:"flex flex-wrap gap-1 md:gap-2",children:e.category.slice(1).map((e,t)=>(0,s.jsx)("span",{className:"bg-orange-500/80 text-white px-1 py-0.5 md:px-2 md:py-1 rounded-full text-[10px] md:text-xs",children:e},t))})})]}),(0,s.jsx)("h3",{className:"text-[16px] md:text-[20px] font-bold text-gray-900 leading-tight line-clamp-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-xs md:text-sm line-clamp-3",children:e.description}),(0,s.jsxs)("div",{className:"flex justify-between items-end mt-auto pt-3 md:pt-4",children:[(0,s.jsx)("div",{className:"flex flex-wrap gap-1 md:gap-2",children:e.keywords.slice(0,2).map((e,t)=>(0,s.jsx)("span",{className:"bg-gray-100 text-gray-700 px-1.5 py-0.5 md:px-2 md:py-1 rounded text-[10px] md:text-xs",children:e.replace(/[\[\]"]/g,"")},t))}),(0,s.jsxs)("div",{className:"flex gap-1 md:gap-2",children:[(0,s.jsx)("button",{onClick:x,className:"p-1.5 md:p-2 bg-gray-100 hover:bg-blue-600 rounded-full transition-colors group",children:(0,s.jsx)(d.iYk,{className:"w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white"})}),(0,s.jsx)("button",{onClick:x,className:"p-1.5 md:p-2 bg-gray-100 hover:bg-pink-600 rounded-full transition-colors group",children:(0,s.jsx)(d.ao$,{className:"w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white"})}),(0,s.jsx)("button",{onClick:x,className:"p-1.5 md:p-2 bg-gray-100 hover:bg-blue-700 rounded-full transition-colors group",children:(0,s.jsx)(d.QEs,{className:"w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white"})})]})]})]}),(0,s.jsx)(c.A,{isOpen:r,onClose:()=>p(!1),blogId:e._id,blogTitle:e.title})]})}var x=r(83230);function u({currentPage:e,totalPages:t,onPageChange:r}){return t<=1?null:(0,s.jsxs)("div",{className:"flex justify-center items-center gap-2 md:gap-4 mt-6 md:mt-8",children:[e>1&&(0,s.jsx)("button",{onClick:()=>r(e-1),className:"px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700 text-sm md:text-base",children:"Previous"}),(()=>{let r=[],s=window.innerWidth<768?3:5;if(t<=s)return Array.from({length:t},(e,t)=>t+1);let a=Math.max(1,e-Math.floor(s/2)),o=a+s-1;o>t&&(a=Math.max(1,(o=t)-s+1)),a>1&&(r.push(1),a>2&&r.push("..."));for(let e=a;e<=o;e++)r.push(e);return o<t&&(o<t-1&&r.push("..."),r.push(t)),r})().map((t,a)=>(0,s.jsx)("button",{onClick:()=>"number"==typeof t?r(t):null,className:`w-8 h-8 md:w-12 md:h-12 rounded-lg transition-all duration-300 text-sm md:text-base ${e===t?"bg-orange-500 text-white font-bold":"number"==typeof t?"bg-zinc-800 text-white hover:bg-zinc-700":"bg-transparent text-white cursor-default"}`,disabled:"..."===t,children:t},a)),e<t&&(0,s.jsx)("button",{onClick:()=>r(e+1),className:"px-3 py-1.5 md:px-4 md:py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700 text-sm md:text-base",children:"Next"})]})}function m(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[n,l]=(0,a.useState)([]),[d,c]=(0,a.useState)(!0),[m,h]=(0,a.useState)(""),[g,v]=(0,a.useState)(""),[b,f]=(0,a.useState)(1),[y,j]=(0,a.useState)(1),[w,N]=(0,a.useState)(!1),P=async()=>{try{c(!0);let e=await (0,o.f_)({page:b,pageSize:6,search:m||void 0,category:g||void 0});e&&e.status&&e.data?(t(e.data.data||[]),j(e.data.totalPages||1)):(t([]),j(1))}catch(e){console.error("Error fetching blogs:",e),t([]),j(1)}finally{c(!1)}},_=e=>{e.preventDefault(),f(1),P()},C=e=>{h(e)},k=e=>{v(e===g?"":e),f(1)},A=e=>{h(e.replace(/[\[\]"]/g,"")),f(1),P()},q=e.flatMap(e=>e.keywords).filter((e,t,r)=>r.indexOf(e)===t);return(0,s.jsx)("div",{className:"text-gray-900 w-full flex justify-center py-10 md:py-20 bg-white",children:(0,s.jsxs)("div",{className:"w-[95%] max-w-[1400px] flex flex-col lg:flex-row gap-8",children:[(0,s.jsxs)("div",{className:"lg:hidden flex justify-between items-center mb-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Blog Posts"}),(0,s.jsx)("button",{onClick:()=>N(!w),className:"bg-orange-500 text-white px-4 py-2 rounded-lg",children:w?"Hide Filters":"Show Filters"})]}),w&&(0,s.jsx)("div",{className:"lg:hidden w-full mb-8",children:(0,s.jsx)(x.A,{categories:r,recentBlogs:n,allKeywords:q,searchQuery:m,selectedCategory:g,onSearchChange:C,onSearchSubmit:_,onCategoryFilter:k,onKeywordClick:A})}),(0,s.jsx)("div",{className:"flex-1",children:d?(0,s.jsx)("div",{className:"text-center py-20",children:(0,s.jsx)("div",{className:"text-gray-900",children:"Loading blogs..."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 md:gap-8 mb-8 md:mb-16",children:e.map(e=>(0,s.jsx)(p,{blog:e},e._id))}),e.length>0&&(0,s.jsx)(u,{currentPage:b,totalPages:y,onPageChange:e=>{f(e)}})]})}),(0,s.jsx)("div",{className:"hidden lg:block w-[350px]",children:(0,s.jsx)(x.A,{categories:r,recentBlogs:n,allKeywords:q,searchQuery:m,selectedCategory:g,onSearchChange:C,onSearchSubmit:_,onCategoryFilter:k,onKeywordClick:A})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48812:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(37413);r(61120);var a=r(93945);function o(){return(0,s.jsx)("div",{className:"min-h-screen bg-white",children:(0,s.jsx)(a.default,{})})}},54316:(e,t,r)=>{Promise.resolve().then(r.bind(r,93945))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68887:(e,t,r)=>{Promise.resolve().then(r.bind(r,5979))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["(pages)",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48812)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\blog\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,48754)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\blog\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(pages)/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93945:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\(pages)\\\\blog\\\\componets\\\\blogcards.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\blog\\componets\\blogcards.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,814,950,579,30,443,143],()=>r(88636));module.exports=s})();