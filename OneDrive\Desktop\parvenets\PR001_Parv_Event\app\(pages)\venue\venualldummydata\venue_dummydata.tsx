import { StaticImageData } from "next/image";
import img1 from "@/public/image/assests/venue/1.png";
import img2 from "@/public/image/assests/venue/2.png";
import img3 from "@/public/image/assests/venue/3.png";
import img4 from "@/public/image/assests/venue/4.png";
import img5 from "@/public/image/assests/venue/5.png";
import img6 from "@/public/image/assests/venue/6.png";
import img7 from "@/public/image/assests/venue/1.png";
import img8 from "@/public/image/assests/venue/2.png";
import img9 from "@/public/image/assests/venue/3.png";
import img10 from "@/public/image/assests/venue/4.png";
import img11 from "@/public/image/assests/venue/5.png";
import img12 from "@/public/image/assests/venue/6.png";

// Updated venue types to match backend schema
export type VenueType =
  | "banquet-hall"
  | "outdoor"
  | "resort"
  | "hotel"
  | "farmhouse"
  | "palace"
  | "garden"
  | "beach"
  | "other";

// Location types for filtering (can be expanded)
export type LocationType = "Mumbai" | "Delhi" | "Goa" | "Bangalore" | "Chennai" | "Kolkata" | "Pune" | "Hyderabad";

// Updated interface to match backend schema
export interface VenueData {
  _id: string;
  name: string;
  image: StaticImageData | string; // Support both static images and URLs
  venueType: VenueType;
  location: string;
  capacity: number;
  seats: number;
  isActive: boolean;
  sortOrder: number;
  createdAt?: string;
  updatedAt?: string;
  // Legacy fields for backward compatibility
  id?: number;
  price?: number;
  type?: string;
  zone?: string;
}

export const venueData: VenueData[] = [
  {
    _id: "venue_001",
    name: "Precious Moments Banquet",
    image: img1,
    venueType: "banquet-hall",
    location: "North Delhi, India",
    capacity: 300,
    seats: 250,
    isActive: true,
    sortOrder: 1,
    // Legacy fields for backward compatibility
    id: 1,
    price: 75000,
    type: "Banquet Hall",
    zone: "North Delhi"
  },
  {
    _id: "venue_002",
    name: "Grand Celebration Banquet",
    image: img2,
    venueType: "banquet-hall",
    location: "South Delhi, India",
    capacity: 400,
    seats: 350,
    isActive: true,
    sortOrder: 2,
    // Legacy fields for backward compatibility
    id: 2,
    price: 75000,
    type: "Banquet Hall",
    zone: "South Delhi"
  },
  {
    _id: "venue_003",
    name: "Elite Banquet Hall",
    image: img3,
    venueType: "banquet-hall",
    location: "West Delhi, India",
    capacity: 300,
    seats: 250,
    isActive: true,
    sortOrder: 3,
    // Legacy fields for backward compatibility
    id: 3,
    price: 70000,
    type: "Banquet Hall",
    zone: "West Delhi"
  },
  {
    _id: "venue_004",
    name: "Royal Garden Farm House",
    image: img4,
    venueType: "farmhouse",
    location: "Gurgaon, Haryana",
    capacity: 500,
    seats: 450,
    isActive: true,
    sortOrder: 4,
    // Legacy fields for backward compatibility
    id: 4,
    price: 90000,
    type: "Farm House",
    zone: "North Delhi"
  },
  {
    _id: "venue_005",
    name: "Luxury Palace Hotel",
    image: img5,
    venueType: "hotel",
    location: "Mumbai, Maharashtra",
    capacity: 600,
    seats: 550,
    isActive: true,
    sortOrder: 5,
    // Legacy fields for backward compatibility
    id: 5,
    price: 120000,
    type: "Hotel",
    zone: "South Delhi"
  },
  {
    _id: "venue_006",
    name: "Garden Paradise Resort",
    image: img6,
    venueType: "garden",
    location: "Bangalore, Karnataka",
    capacity: 200,
    seats: 180,
    isActive: true,
    sortOrder: 6,
    // Legacy fields for backward compatibility
    id: 6,
    price: 65000,
    type: "Restaurant",
    zone: "East Delhi"
  },
  {
    _id: "venue_007",
    name: "Majestic Palace Venue",
    image: img7,
    venueType: "palace",
    location: "Jaipur, Rajasthan",
    capacity: 350,
    seats: 300,
    isActive: true,
    sortOrder: 7,
    // Legacy fields for backward compatibility
    id: 7,
    price: 85000,
    type: "Banquet Hall",
    zone: "West Delhi"
  },
  {
    _id: "venue_008",
    name: "Serene Valley Farm House",
    image: img8,
    venueType: "farmhouse",
    location: "Noida, Uttar Pradesh",
    capacity: 450,
    seats: 400,
    isActive: true,
    sortOrder: 8,
    // Legacy fields for backward compatibility
    id: 8,
    price: 95000,
    type: "Farm House",
    zone: "South Delhi"
  },
  {
    _id: "venue_009",
    name: "Beachside Resort",
    image: img9,
    venueType: "beach",
    location: "Goa, India",
    capacity: 550,
    seats: 500,
    isActive: true,
    sortOrder: 9,
    // Legacy fields for backward compatibility
    id: 9,
    price: 110000,
    type: "Hotel",
    zone: "North Delhi"
  },
  {
    _id: "venue_010",
    name: "Outdoor Garden Venue",
    image: img10,
    venueType: "outdoor",
    location: "Pune, Maharashtra",
    capacity: 250,
    seats: 220,
    isActive: true,
    sortOrder: 10,
    // Legacy fields for backward compatibility
    id: 10,
    price: 70000,
    type: "Restaurant",
    zone: "West Delhi"
  },
  {
    _id: "venue_011",
    name: "Grand Resort & Spa",
    image: img11,
    venueType: "resort",
    location: "Udaipur, Rajasthan",
    capacity: 320,
    seats: 280,
    isActive: true,
    sortOrder: 11,
    // Legacy fields for backward compatibility
    id: 11,
    price: 80000,
    type: "Banquet Hall",
    zone: "East Delhi"
  },
  {
    _id: "venue_012",
    name: "Lakeside Retreat",
    image: img12,
    venueType: "other",
    location: "Shimla, Himachal Pradesh",
    capacity: 480,
    seats: 420,
    isActive: true,
    sortOrder: 12,
    // Legacy fields for backward compatibility
    id: 12,
    price: 100000,
    type: "Farm House",
    zone: "North Delhi"
  }
]
