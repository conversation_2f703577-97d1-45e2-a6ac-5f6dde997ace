import Image from 'next/image'
import React from 'react'
import flowertable from "@/public/image/assests/flowertable.png"
import flower from "@/public/image/assests/backgroundimageflower.png";
import love from "@/public/image/assests/love.png";
import profile from "@/public/image/assests/profile.svg";
import vectorprofile from "@/public/image/assests/client.svg";
import vector from "@/public/image/assests/Vector.png";

const WhyChooseusData = [
  {
    icons: love,
    title: 'Personalized Planning',
    description: 'Customized plans that reflect your unique love story.'
  },
  {
    icons: profile,
    title: 'Experienced Team',
    description: 'Seasoned professionals with a passion for perfection.'
  },
  {
    icons: vectorprofile,
    title: 'Stress-Free Process',
    description: 'We handle the details, so you don’t have to worry.'
  }
]

const Couple = () => {
  return (
    <div className='bg-[#FFEDE3] flex lg:flex-row flex-col items-center lg:items-stretch w-full pb-8 pt-6'>

         {/* Left Section */}
         <div className='relative lg:w-[50%] w-full flex flex-col items-center justify-center gap-y-10 px-6 sm:px-8 lg:px-10 mb-8 lg:mb-0'>
        <div className='relative w-full flex flex-col md:justify-center sm:justify-start md:items-center sm:items-start  items-end justify-end gap-y-6 text-left'>
          <h1 className='text-2xl sm:text-3xl md:text-4xl font-normal font-urbanist flex text-[#13031F] flex-col items-start sm:items-center md:items-start text-left w-full lg:w-[69%]'>
            <span>Why Couples</span>
            <span>Trust Us</span>
          </h1>
          <Image
            src={flower}
            alt='flower background'
            className='absolute -bottom-20 md:-left-8 sm:-left-8 -left-8   z-0 w-[120px] sm:w-[180px] lg:w-[263px] h-auto'
          />
          <Image
            src={flowertable}
            alt='flower table'
            className='relative z-10 w-[360px] sm:w-[340px] lg:w-[440px] h-auto'
          />
        </div>
      </div>

      {/* Middle Section */}
      <div className='flex flex-col lg:w-[30%] w-full justify-center gap-6 py-6 px-6 sm:px-8 lg:pl-8'>
        <div className='flex flex-col gap-2 w-full sm:w-[18rem]'>
          <h1 className='text-base sm:text-lg md:text-xl font-semibold font-urbanist flex flex-row items-center gap-x-3'>Why Choose Us <span className='border border-[#cacaca] w-[6rem] h-0'></span></h1>
          <p className='font-urbanist text-gray-700 text-sm sm:text-base'>
            Our commitment to excellence and personalized service makes us the preferred choice for discerning couples.
          </p>
        </div>

        <div className='flex flex-col gap-8 mt-4'>
          {WhyChooseusData.map((item, index) => (
            <div key={index} className='rounded-xl flex flex-col items-start text-left'>
              <Image
                src={item.icons}
                alt={item.title}
                width={40}
                height={40}
                className='bg-white w-10 h-10 p-2 rounded-full'
              />
              <h3 className='text-base sm:text-lg md:text-xl font-semibold mt-3'>{item.title}</h3>
              <p className='text-sm sm:text-base text-gray-600 mt-2'>{item.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Right Section */}
      <div className='lg:flex hidden flex-row justify-end items-start w-[20%] '>
        <Image src={vector} alt='vector graphic' className='relative z-10 w-[80%] h-auto' />
      </div>
    </div>
  )
}

export default Couple
