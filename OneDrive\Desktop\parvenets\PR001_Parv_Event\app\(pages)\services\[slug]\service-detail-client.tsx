"use client";
import React, { useState, useEffect } from "react";
import Page<PERSON>ero from "@/app/_components/page_hero/page_hero";
import Image from "next/image";
import { serviceApi, Service } from '@/lib/api/service/serviceApi';
import { toast } from 'react-toastify';
import imageservice from "@/public/image/icons/services/servicesdetails.png";

// Client component props
type ServiceDetailClientProps = {
  params: { slug: string };
  searchParams?: { [key: string]: string | string[] | undefined };
};

const ServiceDetailClient = ({ params, searchParams }: ServiceDetailClientProps) => {
  const [service, setService] = useState<Service | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Add safety check to prevent errors
  if (!params || !params.slug) {
    return (
      <div className="py-20 text-center">
        <p>Invalid service URL.</p>
      </div>
    );
  }

  // Fetch service by ID
  const fetchService = async () => {
    try {
      setIsLoading(true);
      setError(null);

       const response = await serviceApi.getServiceById(params.slug);


      if (response.success && response.data) {
        setService(response.data);
      } else {
        throw new Error('Service not found');
      }
    } catch (err: any) {
      console.error('Error fetching service:', err);
      setError(err.message || 'Failed to load service');
      toast.error('Failed to load service details.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchService();
  }, [params.slug]);

  // Loading state
  if (isLoading) {
    return (
      <>
        <PageHero title="LOADING..." breadcrumbs={[
          { label: "HOME", href: "/" },
          { label: "SERVICES", href: "/services" }
        ]} />
        <div className="py-20 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FE904B] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading service details...</p>
        </div>
      </>
    );
  }

  // Error state
  if (error || !service) {
    return (
      <>
        <PageHero title="SERVICE NOT FOUND" breadcrumbs={[
          { label: "HOME", href: "/" },
          { label: "SERVICES", href: "/services" }
        ]} />
        <div className="py-20 text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-medium">Service not found</p>
            <p className="text-sm text-gray-600 mt-2">{error}</p>
          </div>
          <button
            onClick={fetchService}
            className="px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors mr-4"
          >
            Try Again
          </button>
          <a
            href="/services"
            className="px-4 py-2 border border-[#FE904B] text-[#FE904B] rounded-md hover:bg-[#FE904B] hover:text-white transition-colors"
          >
            Back to Services
          </a>
        </div>
      </>
    );
  }
  // Render service details
  const breadcrumbs = [
    { label: "HOME", href: "/" },
    { label: "SERVICE DETAILS", href: "/services" },

  ];

  return (
    <>
      <PageHero title='SERVICE DETAILS' breadcrumbs={breadcrumbs} />

      {/* Hero Section */}
      <div className="bg-gradient-to-b from-gray-50 to-white py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-xs sm:text-sm text-[#BC7B77] uppercase tracking-wider mb-3 sm:mb-4 font-medium">LATEST SERVICE</h1>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 uppercase text-gray-900 leading-tight">
            {service.title}
          </h2>

          <div className="max-w-[68rem] mx-auto space-y-4 sm:space-y-6">
            <p className="text-gray-700 text-sm sm:text-base lg:text-lg leading-relaxed text-center sm:text-left px-4 sm:px-0">
              {service.description}
            </p>

            {service.description2 && (
              <p className="text-gray-600 text-xs sm:text-sm lg:text-base leading-relaxed text-center sm:text-left px-4 sm:px-0">
                {service.description2}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Image Section */}
      <div className="py-6 sm:py-8 lg:py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative h-[250px] sm:h-[350px] md:h-[450px] lg:h-[500px] xl:h-[550px] rounded-xl sm:rounded-2xl overflow-hidden shadow-xl">
            <Image
              src={service.image}
              alt={service.title}
              fill
              className="object-cover hover:scale-105 transition-transform duration-700"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
        </div>
      </div>

      {/* How We Do It Section */}
      <div className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-orange-50">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-gray-900 mb-4 sm:mb-6 uppercase tracking-wide">
              How We Do It
            </h2>
           
          </div>

          {/* Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10">
            {service.howWeDoIt && service.howWeDoIt.length > 0 ? (
              service.howWeDoIt.slice(0, 3).map((step, index) => (
                <div
                  key={index}
                  className="group relative bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 border "style={{ borderColor: "rgba(229, 218, 207, 1)" }}
                >
                  {/* Card Number */}
                  {/* <div className="absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg ">
                    <span className="text-white font-bold text-lg sm:text-xl">{index + 1}</span>
                  </div> */}

                  {/* Icon */}
                  {/* <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 group-hover:scale-105 transition-transform duration-300">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div> */}

                  {/* Content */}
                  <div className="space-y-4">
                    <h3 className="text-lg sm:text-xl lg:text-2xl  text-center  font-semibold text-gray-900 leading-tight ">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 text-center text-sm sm:text-base leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Decorative Element */}
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 "></div>
                </div>
              ))
            ) : (
              // Default steps if no howWeDoIt data
              <>
                <div className="group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 ">
                  {/* <div className="absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg ">
                    <span className="text-white font-bold text-lg sm:text-xl">1</span>
                  </div> */}
                  {/* <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 ">
                    { <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div> }
                  </div> */}
                  <div className="space-y-4">
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ">
                      Professional Planning
                    </h3>
                    <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                      Our experienced team creates detailed plans tailored to your specific needs and vision.
                    </p>
                  </div>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                </div>

                <div className="group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-orange-200 hover:-translate-y-2">
                  {/* <div className="absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-lg sm:text-xl">2</span>
                  </div> */}
                  {/* <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 group-hover:scale-105 transition-transform duration-300">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div> */}
                  <div className="space-y-4">
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ">
                      Quality Execution
                    </h3>
                    <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                      We ensure every detail is executed flawlessly with attention to quality and precision.
                    </p>
                  </div>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                </div>

                <div className="group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 ">
                  {/* <div className="absolute -top-4 -right-4 w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-lg sm:text-xl">3</span>
                  </div> */}
                  {/* <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mb-6 sm:mb-8 ">
                    { <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#FE904B] to-orange-600 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </div> }
                  </div> */}
                  <div className="space-y-4">
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ">
                      Memorable Experience
                    </h3>
                    <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                      Creating unforgettable moments that exceed your expectations and delight your guests.
                    </p>
                  </div>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 "></div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ServiceDetailClient;
