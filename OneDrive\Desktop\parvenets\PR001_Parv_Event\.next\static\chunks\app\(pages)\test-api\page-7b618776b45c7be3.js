(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[242],{837:(e,t,s)=>{Promise.resolve().then(s.bind(s,1263))},1263:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(5155),a=s(2115),n=s(7693),o=s(1966);let l={testConnection:async()=>{try{console.log("\uD83D\uDD0D Testing connection to:",o.JW.BASE_URL);let e=await n.u.get((0,o.c$)("/health"));return console.log("✅ Connection test successful:",e.data),e.data}catch(s){var e,t;return console.error("❌ Connection test failed:",s),{success:!1,message:s.message||"Connection failed",timestamp:new Date().toISOString(),data:{error:s.code,url:null==(e=s.config)?void 0:e.url,status:null==(t=s.response)?void 0:t.status}}}},testApiEndpoint:async()=>{try{console.log("\uD83D\uDD0D Testing API endpoint:",(0,o.KB)("/test"));let e=await n.u.get((0,o.KB)("/test"));return console.log("✅ API test successful:",e.data),e.data}catch(s){var e,t;return console.error("❌ API test failed:",s),{success:!1,message:s.message||"API test failed",timestamp:new Date().toISOString(),data:{error:s.code,url:null==(e=s.config)?void 0:e.url,status:null==(t=s.response)?void 0:t.status}}}},testMultipleEndpoints:async()=>{let e=[{name:"Health Check",url:(0,o.c$)("/health")},{name:"API Health",url:(0,o.KB)("/health")},{name:"API Test",url:(0,o.KB)("/test")},{name:"Events",url:(0,o.KB)("/events")},{name:"Venues",url:(0,o.c$)("/venues")},{name:"Blogs",url:(0,o.c$)("/blogs")}],t=[];for(let r of e)try{console.log("\uD83D\uDD0D Testing ".concat(r.name,":"),r.url);let e=await n.u.get(r.url);t.push({success:!0,message:"".concat(r.name," - Success"),timestamp:new Date().toISOString(),data:{url:r.url,status:e.status,responseSize:JSON.stringify(e.data).length}}),console.log("✅ ".concat(r.name," successful"))}catch(e){var s;t.push({success:!1,message:"".concat(r.name," - Failed: ").concat(e.message),timestamp:new Date().toISOString(),data:{url:r.url,error:e.code,status:null==(s=e.response)?void 0:s.status}}),console.log("❌ ".concat(r.name," failed:"),e.message)}return t},debugConfig:()=>({baseUrl:o.JW.BASE_URL,apiVersion:o.JW.API_VERSION,timeout:o.JW.TIMEOUT,sampleUrls:{buildUrl:(0,o.c$)("/test"),buildApiUrl:(0,o.KB)("/test")},environment:{nodeEnv:"production",nextPublicBaseUrl:"https://parevent-new-backend.onrender.com"}})};function c(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null);(0,a.useEffect)(()=>{c(l.debugConfig())},[]);let i=async()=>{n(!0);try{let e=await l.testConnection();t(t=>[...t,{type:"Connection Test",...e}])}catch(e){console.error("Test failed:",e)}n(!1)},d=async()=>{n(!0);try{let e=await l.testApiEndpoint();t(t=>[...t,{type:"API Test",...e}])}catch(e){console.error("API test failed:",e)}n(!1)},u=async()=>{n(!0);try{let e=await l.testMultipleEndpoints();t(t=>[...t,...e.map(e=>({type:"Multiple Tests",...e}))])}catch(e){console.error("Multiple tests failed:",e)}n(!1)};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"API Network Test"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Configuration Debug"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-x-auto",children:JSON.stringify(o,null,2)})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Network Tests"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("button",{onClick:i,disabled:s,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50",children:"Test Connection"}),(0,r.jsx)("button",{onClick:d,disabled:s,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50",children:"Test API Endpoint"}),(0,r.jsx)("button",{onClick:u,disabled:s,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50",children:"Test Multiple Endpoints"}),(0,r.jsx)("button",{onClick:()=>{t([])},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded",children:"Clear Results"})]}),s&&(0,r.jsx)("div",{className:"mt-4 text-blue-600",children:"Running tests..."})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),0===e.length?(0,r.jsx)("p",{className:"text-gray-500",children:"No test results yet. Run a test to see results."}):(0,r.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"p-4 rounded border-l-4 ".concat(e.success?"border-green-500 bg-green-50":"border-red-500 bg-red-50"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:e.type}),(0,r.jsx)("span",{className:"px-2 py-1 rounded text-sm ".concat(e.success?"bg-green-200 text-green-800":"bg-red-200 text-red-800"),children:e.success?"SUCCESS":"FAILED"})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:e.message}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:e.timestamp}),e.data&&(0,r.jsxs)("details",{className:"mt-2",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-sm text-gray-600",children:"View Details"}),(0,r.jsx)("pre",{className:"mt-2 bg-gray-100 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})]})]})})}},1966:(e,t,s)=>{"use strict";s.d(t,{JW:()=>r,KB:()=>n,c$:()=>a});let r={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},a=e=>{let t=r.BASE_URL,s=e.startsWith("/")?e:"/".concat(e);return"".concat(t,"/api").concat(s)},n=a},7693:(e,t,s)=>{"use strict";s.d(t,{u:()=>o});var r=s(3464),a=s(1966);let n=r.A.create({timeout:a.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});n.interceptors.request.use(e=>{{let t=localStorage.getItem("authToken");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>(console.error("❌ Request Error:",e),Promise.reject(e))),n.interceptors.response.use(e=>e,e=>{if(e.response){let{status:t,data:s}=e.response;switch(t){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",s)}}else if(e.request){var t,s,r;console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:null==(t=e.config)?void 0:t.url,method:null==(s=e.config)?void 0:s.method,baseURL:null==(r=e.config)?void 0:r.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."}else console.error("❌ Error:",e.message);return Promise.reject(e)});let o={get:(e,t)=>n.get(e,t),post:(e,t,s)=>n.post(e,t,s),put:(e,t,s)=>n.put(e,t,s),patch:(e,t,s)=>n.patch(e,t,s),delete:(e,t)=>n.delete(e,t)}}},e=>{var t=t=>e(e.s=t);e.O(0,[83,441,684,358],()=>t(837)),_N_E=e.O()}]);