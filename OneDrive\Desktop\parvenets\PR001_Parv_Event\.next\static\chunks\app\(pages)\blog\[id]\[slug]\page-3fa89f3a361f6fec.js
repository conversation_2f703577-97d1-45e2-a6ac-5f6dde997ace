(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[701],{4190:(e,t,s)=>{Promise.resolve().then(s.bind(s,5205))},5205:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(5155),r=s(2115),m=s(6766),n=s(6874),l=s.n(n),d=s(5695),i=s(5305),o=s(351),c=s(1235),x=s(312),g=s(7693),h=s(1966);let u={createComment:async e=>(await g.u.post((0,h.c$)("/comments"),e)).data,getApprovedComments:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),e.limit&&t.append("limit",e.limit.toString()),e.blogId&&t.append("blogId",e.blogId.toString());let s="/comments/approved".concat(t.toString()?"?".concat(t.toString()):"");return(await g.u.get((0,h.c$)(s))).data},getCommentsByBlogId:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=new URLSearchParams;t.page&&s.append("page",t.page.toString()),t.limit&&s.append("limit",t.limit.toString()),t.sortBy&&s.append("sortBy",t.sortBy),t.sortOrder&&s.append("sortOrder",t.sortOrder),t.status&&s.append("status",t.status),void 0!==t.isVisible&&s.append("isVisible",t.isVisible.toString());let a="/comments/blog/".concat(e).concat(s.toString()?"?".concat(s.toString()):"");return(await g.u.get((0,h.c$)(a))).data},getCommentCountByBlogId:async e=>(await g.u.get((0,h.c$)("/comments/blog/".concat(e,"/count")))).data,getAllComments:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.status&&t.append("status",e.status),void 0!==e.isVisible&&t.append("isVisible",e.isVisible.toString()),e.blogId&&t.append("blogId",e.blogId.toString()),e.search&&t.append("search",e.search),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder);let s="/comments".concat(t.toString()?"?".concat(t.toString()):"");return(await g.u.get((0,h.c$)(s))).data},getCommentById:async e=>(await g.u.get((0,h.c$)("/comments/".concat(e)))).data,updateComment:async(e,t)=>(await g.u.put((0,h.c$)("/comments/".concat(e)),t)).data,deleteComment:async e=>(await g.u.delete((0,h.c$)("/comments/".concat(e)))).data,getCommentStatistics:async()=>(await g.u.get((0,h.c$)("/comments/statistics"))).data,approveComment:async e=>u.updateComment(e,{status:"approved",isVisible:!0}),rejectComment:async e=>u.updateComment(e,{status:"rejected",isVisible:!1}),markAsSpam:async e=>u.updateComment(e,{status:"spam",isVisible:!1}),getPendingComments:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return u.getAllComments({...e,status:"pending"})}},p=e=>{try{let t=new Date(e),s=new Date().getTime()-t.getTime(),a=Math.floor(s/864e5),r=Math.floor(s/36e5),m=Math.floor(s/6e4);if(m<1)return"Just now";if(m<60)return"".concat(m," minutes ago");if(r<24)return"".concat(r," hours ago");if(1===a)return"Yesterday";if(a<7)return"".concat(a," days ago");return t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(e){return"Unknown"}},b=async e=>u.createComment(e),y=async e=>u.getApprovedComments(e),j=e=>{let{blogId:t,blogTitle:s}=e,[m,n]=(0,r.useState)([]),[l,d]=(0,r.useState)(!0),[i,c]=(0,r.useState)(!1),[x,g]=(0,r.useState)(!1),[h,u]=(0,r.useState)(null),[j,f]=(0,r.useState)({name:"",email:"",comment:""}),[w,N]=(0,r.useState)({});(0,r.useEffect)(()=>{t&&v()},[t]);let v=async()=>{if(t)try{d(!0);let e=await y({limit:50,blogId:t});e.status&&e.data?n(Array.isArray(e.data)?e.data:[]):n([])}catch(e){console.error("Error fetching comments:",e),u("Failed to load comments"),n([])}finally{d(!1)}},S=()=>{let e={};return j.name.trim()?j.name.trim().length<2&&(e.name="Name must be at least 2 characters"):e.name="Name is required",j.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(j.email)||(e.email="Please enter a valid email address"):e.email="Email is required",j.comment.trim()?j.comment.trim().length<10?e.comment="Comment must be at least 10 characters":j.comment.trim().length>1e3&&(e.comment="Comment must be less than 1000 characters"):e.comment="Comment is required",N(e),0===Object.keys(e).length},C=e=>{let{name:t,value:s}=e.target;f(e=>({...e,[t]:s})),w[t]&&N(e=>({...e,[t]:void 0}))},k=async e=>{if(e.preventDefault(),S()&&t)try{c(!0),u(null);let e=await b({name:j.name.trim(),email:j.email.trim(),comment:j.comment.trim(),blogId:t});e.status?(f({name:"",email:"",comment:""}),g(!0),v(),setTimeout(()=>g(!1),5e3)):u(e.message||"Failed to submit comment. Please try again.")}catch(t){var s,a;console.error("Error submitting comment:",t);let e="Failed to submit comment. Please try again.";(null==(a=t.response)||null==(s=a.data)?void 0:s.message)?e=t.response.data.message:t.message?e=t.message:"string"==typeof t&&(e=t),(e.includes("ENOTFOUND")||e.includes("mongodb.net"))&&(e="Database connection error. Please try again later or contact support."),u(e)}finally{c(!1)}};return(0,a.jsx)("div",{className:"w-full text-gray-900 py-8 md:py-12",children:(0,a.jsxs)("div",{className:"w-full px-0 md:px-4",children:[(0,a.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,a.jsxs)("h2",{className:"text-xl md:text-2xl font-bold text-gray-900 mb-2 flex items-center",children:[(0,a.jsx)(o.mEP,{className:"mr-3 h-5 w-5 md:h-6 md:w-6 text-orange-500"}),"Comments (",m.length,")"]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm md:text-base",children:"Share your thoughts about this article. Your comment will be reviewed before publishing."})]}),x&&(0,a.jsx)("div",{className:"mb-4 md:mb-6 bg-green-50 border border-green-200 rounded-lg p-3 md:p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A3x,{className:"h-4 w-4 md:h-5 md:w-5 text-green-600 mr-2 md:mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xs md:text-sm font-medium text-green-800",children:"Comment Submitted Successfully!"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-green-700 mt-1",children:"Thank you for your comment. It will be reviewed and published soon."})]})]})}),h&&(0,a.jsx)("div",{className:"mb-4 md:mb-6 bg-red-50 border border-red-200 rounded-lg p-3 md:p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.y3G,{className:"h-4 w-4 md:h-5 md:w-5 text-red-600 mr-2 md:mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xs md:text-sm font-medium text-red-800",children:"Error"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-red-700 mt-1",children:h})]})]})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-lg p-4 md:p-6 mb-6 md:mb-8",children:[(0,a.jsx)("h3",{className:"text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4",children:"Leave a Comment"}),(0,a.jsxs)("form",{onSubmit:k,className:"space-y-3 md:space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2",children:[(0,a.jsx)(o.JXP,{className:"inline mr-2 h-3 w-3 md:h-4 md:w-4"}),"Name *"]}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:j.name,onChange:C,className:"w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ".concat(w.name?"border-red-500":"border-gray-300"),placeholder:"Your full name",disabled:i}),w.name&&(0,a.jsx)("p",{className:"mt-1 text-xs md:text-sm text-red-400",children:w.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"email",className:"block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2",children:[(0,a.jsx)(o.pHD,{className:"inline mr-2 h-3 w-3 md:h-4 md:w-4"}),"Email *"]}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:j.email,onChange:C,className:"w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ".concat(w.email?"border-red-500":"border-gray-300"),placeholder:"<EMAIL>",disabled:i}),w.email&&(0,a.jsx)("p",{className:"mt-1 text-xs md:text-sm text-red-400",children:w.email})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"comment",className:"block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2",children:[(0,a.jsx)(o.mEP,{className:"inline mr-2 h-3 w-3 md:h-4 md:w-4"}),"Comment *"]}),(0,a.jsx)("textarea",{id:"comment",name:"comment",rows:4,value:j.comment,onChange:C,className:"w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ".concat(w.comment?"border-red-500":"border-gray-300"),placeholder:"Share your thoughts about this article...",disabled:i}),(0,a.jsx)("div",{className:"flex justify-between items-center mt-1",children:w.comment?(0,a.jsx)("p",{className:"text-xs md:text-sm text-red-400",children:w.comment}):(0,a.jsxs)("p",{className:"text-xs md:text-sm text-gray-500",children:[j.comment.length,"/1000 characters"]})})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{type:"submit",disabled:i,className:"inline-flex items-center px-4 py-2 md:px-6 md:py-3 border border-transparent text-xs md:text-sm font-medium rounded-lg text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.kGk,{className:"mr-2 h-3 w-3 md:h-4 md:w-4"}),"Submit Comment"]})})})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 md:mb-6",children:m.length>0?"Recent Comments":"No Comments Yet"}),l?(0,a.jsx)("div",{className:"flex items-center justify-center py-6 md:py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 md:h-8 md:w-8 border-b-2 border-orange-500"})}):0===m.length?(0,a.jsxs)("div",{className:"text-center py-6 md:py-8 text-gray-400",children:[(0,a.jsx)(o.mEP,{className:"mx-auto h-8 w-8 md:h-12 md:w-12 text-gray-600 mb-3 md:mb-4"}),(0,a.jsx)("p",{className:"text-sm md:text-base",children:"Be the first to leave a comment!"})]}):(0,a.jsx)("div",{className:"space-y-4 md:space-y-6",children:m.map(e=>(0,a.jsxs)("div",{className:"bg-white border border-gray-200 shadow-sm rounded-lg p-4 md:p-6",children:[(0,a.jsx)("div",{className:"flex items-start justify-between mb-2 md:mb-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 md:w-10 md:h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold",children:e.name.charAt(0).toUpperCase()}),(0,a.jsxs)("div",{className:"ml-2 md:ml-3",children:[(0,a.jsx)("h4",{className:"text-gray-900 font-medium text-sm md:text-base",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center text-xs md:text-sm text-gray-500",children:[(0,a.jsx)(o.Ohp,{className:"mr-1 h-2 w-2 md:h-3 md:w-3"}),p(e.createdAt)]})]})]})}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed text-sm md:text-base",children:e.comment})]},e.id))})]})]})})};var f=s(1661);function w(){let e=(0,d.useParams)(),t=(0,d.useRouter)(),[s,n]=(0,r.useState)(null),[g,h]=(0,r.useState)([]),[u,p]=(0,r.useState)([]),[b,y]=(0,r.useState)(!0),[w,N]=(0,r.useState)(!1),[v,S]=(0,r.useState)(""),[C,k]=(0,r.useState)(!1),[B,A]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e.id&&(E(e.id),I(),P())},[e.id]);let E=async e=>{try{y(!0);let t=await (0,c.dZ)(e);t.status&&n(t.data)}catch(e){console.error("Error fetching blog detail:",e)}finally{y(!1)}},I=async()=>{try{let e=await (0,c.tz)();e.status&&h(e.data)}catch(e){console.error("Error fetching categories:",e)}},P=async()=>{try{let e=await (0,c.z$)(3);e.status&&p(e.data.data)}catch(e){console.error("Error fetching recent blogs:",e)}},O=e=>{e.preventDefault(),v.trim()&&t.push("/blog?search=".concat(encodeURIComponent(v)))},D=e=>{S(e)},U=e=>{t.push("/blog?category=".concat(encodeURIComponent(e)))},F=e=>{t.push("/blog?search=".concat(encodeURIComponent(e)))};return b?(0,a.jsx)("div",{className:"min-h-screen bg-white text-gray-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("div",{className:"text-gray-900 text-xl",children:"Loading blog..."})})}):s?(0,a.jsxs)("div",{className:"bg-white text-gray-900 py-10 md:py-16 lg:py-20 overflow-x-hidden",children:[(0,a.jsxs)("div",{className:"w-[95%] max-w-[1400px] mx-auto flex flex-col lg:flex-row gap-6 lg:gap-8",children:[(0,a.jsxs)("div",{className:"lg:hidden flex justify-between items-center mb-4",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors",children:[(0,a.jsx)(i.atu,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm",children:"Back"})]}),(0,a.jsx)("button",{onClick:()=>A(!B),className:"bg-orange-500 text-white px-4 py-2 rounded-lg text-sm",children:B?"Hide Sidebar":"Show Sidebar"})]}),B&&(0,a.jsx)("div",{className:"lg:hidden w-full mb-8",children:(0,a.jsx)(x.A,{categories:g,recentBlogs:u,allKeywords:(null==s?void 0:s.keywords)||[],searchQuery:v,selectedCategory:"",onSearchChange:D,onSearchSubmit:O,onCategoryFilter:U,onKeywordClick:F})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"hidden lg:flex items-center gap-2 text-gray-600 hover:text-orange-500 mb-6 transition-colors",children:[(0,a.jsx)(i.atu,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back to Blogs"})]}),(0,a.jsxs)("div",{className:"mb-6 md:mb-8",children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-3 md:mb-4",children:s.category.map((e,t)=>(0,a.jsx)("span",{className:"bg-orange-500 text-white px-2 py-1 md:px-3 rounded-full text-xs md:text-sm",children:e},t))}),(0,a.jsx)("h1",{className:"text-2xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-3 md:mb-4",children:s.title}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 md:gap-4 text-gray-600 text-sm md:text-base mb-4 md:mb-6",children:[(0,a.jsxs)("span",{children:["Published: ",(0,c._n)(s.createdAt)]}),(0,a.jsx)("span",{className:"hidden md:inline",children:"•"}),(0,a.jsxs)("span",{children:["Views: ",s.views]})]}),(0,a.jsx)("div",{className:"relative w-full overflow-hidden h-[250px] sm:h-[350px] md:h-[450px] lg:h-[650px] rounded-[16px] md:rounded-[20px] mb-6 md:mb-8",children:s.imageUrl?(0,a.jsx)(m.default,{src:s.imageUrl,alt:s.title,fill:!0,className:"object-cover w-full h-full",priority:!0}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-orange-500 to-orange-700 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-white text-center p-8",children:[(0,a.jsx)("div",{className:"text-6xl md:text-8xl font-bold mb-4",children:"\uD83D\uDCDD"}),(0,a.jsx)("div",{className:"text-xl md:text-2xl font-medium",children:"Blog Post"})]})})})]}),(0,a.jsx)("div",{className:"prose prose-gray max-w-none overflow-hidden break-words mb-6 md:mb-8",children:(0,a.jsx)("div",{className:"text-gray-800 text-base md:text-lg leading-relaxed",dangerouslySetInnerHTML:{__html:s.description}})}),(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-6 md:mb-8",children:[(0,a.jsx)("h3",{className:"text-gray-900 text-lg md:text-xl font-bold mb-3 md:mb-4",children:"Keywords"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s.keywords.slice(0,w?void 0:5).map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-200 text-gray-700 px-2 py-1 md:px-3 rounded-full text-xs md:text-sm",children:e.replace(/[\[\]"]/g,"")},t)),s.keywords.length>5&&(0,a.jsx)("button",{onClick:()=>N(!w),className:"text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors",children:w?"Show Less":"More"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-8",children:[(0,a.jsx)("h3",{className:"text-gray-900 text-lg md:text-xl font-bold mb-3 md:mb-4",children:"Share this article"}),(0,a.jsxs)("button",{onClick:()=>k(!0),className:"flex items-center gap-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 md:px-6 md:py-3 rounded-lg transition-colors text-sm md:text-base",children:[(0,a.jsx)(o.Pum,{className:"w-4 h-4 md:w-5 md:h-5"}),(0,a.jsx)("span",{children:"Share Article"})]})]}),(0,a.jsx)(j,{blogId:Array.isArray(e.id)?e.id[0]:e.id||void 0,blogTitle:null==s?void 0:s.title})]}),(0,a.jsx)("div",{className:"hidden lg:block lg:w-[350px]",children:(0,a.jsx)(x.A,{categories:g,recentBlogs:u,allKeywords:(null==s?void 0:s.keywords)||[],searchQuery:v,selectedCategory:"",onSearchChange:D,onSearchSubmit:O,onCategoryFilter:U,onKeywordClick:F})})]}),(0,a.jsx)(f.A,{isOpen:C,onClose:()=>k(!1),blogId:(null==s?void 0:s._id)||"",blogTitle:(null==s?void 0:s.title)||"",blogUrl:window.location.href})]}):(0,a.jsx)("div",{className:"min-h-screen bg-white text-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-900 text-xl mb-4",children:"Blog not found"}),(0,a.jsx)(l(),{href:"/blog",className:"text-orange-500 hover:text-orange-400",children:"Back to Blogs"})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[711,87,844,83,854,380,586,196,441,684,358],()=>t(4190)),_N_E=e.O()}]);