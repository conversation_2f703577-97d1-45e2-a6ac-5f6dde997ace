import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { GLOBAL_CONFIG } from './globalurl';

// Check if we're on the client side
const isClient = typeof window !== 'undefined';

// Create custom axios instance
const customAxios: AxiosInstance = axios.create({
  baseURL: GLOBAL_CONFIG.BASE_URL,
  timeout: GLOBAL_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: false, // Set to true if you need cookies
});

// Request interceptor
customAxios.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add auth token if available (only on client side)
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    
    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
      });
    }
    
    return config;
  },
  (error: AxiosError) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
customAxios.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ API Response:', {
        status: response.status,
        url: response.config.url,
        data: response.data,
      });
    }
    
    return response;
  },
  (error: AxiosError) => {
    // Handle common errors
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Unauthorized - redirect to login or refresh token
          console.error('❌ Unauthorized access');
          // localStorage.removeItem('authToken');
          // window.location.href = '/login';
          break;
        case 403:
          console.error('❌ Forbidden access');
          break;
        case 404:
          console.error('❌ Resource not found');
          break;
        case 500:
          console.error('❌ Server error');
          break;
        default:
          console.error('❌ API Error:', data);
      }
    } else if (error.request) {
      // Network error - no response received
      console.error('❌ Network Error - No response from server:', {
        message: error.message,
        code: error.code,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
        }
      });

      // Add user-friendly error message
      error.message = 'Network error: Unable to connect to server. Please check your internet connection.';
    } else {
      console.error('❌ Error:', error.message);
    }

    return Promise.reject(error);
  }
);

export default customAxios;

// Helper functions for common HTTP methods
export const apiClient = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    customAxios.get(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    customAxios.post(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    customAxios.put(url, data, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    customAxios.patch(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    customAxios.delete(url, config),
};
