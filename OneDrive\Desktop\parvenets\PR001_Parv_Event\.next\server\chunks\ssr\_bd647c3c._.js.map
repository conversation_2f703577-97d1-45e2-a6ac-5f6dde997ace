{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/pinktree.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 256, height: 195, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAAtklEQVR42k2LXQ+BYACFX4WXO8zHbEyrVqhRSVspq1xUbBUX0QXNpZkb83Fj/HUVNs92trOd5wDwJRODxEk6zOXyAkkLr/3h8dtBvVSukq02nozuRJ/bkrKQuqwIaqVypZCHcKWZS5Hq8eqAly+b6N5pNDvp01cNh8Fw+hxsr/pw5LjKNGQxXEAQBE2FoxecbsHuGZpW1K43CJHucwxGUFkU/QgzTgzXph36muFZY9koQlgEf7wB2pkb9Zp1xhgAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0V,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/page_hero/page_hero.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport cherryBlossom from '@/public/image/assests/pinktree.png'; // Make sure this image exists\r\n\r\ninterface PageHeroProps {\r\n  title: string;\r\n  breadcrumbs: {\r\n    label: string;\r\n    href: string;\r\n  }[];\r\n}\r\n\r\nconst PageHero = ({ title, breadcrumbs }: PageHeroProps) => {\r\n  return (\r\n    <div className=\"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4\">\r\n      <div className=\"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center\">\r\n        {/* Left side - Page Title */}\r\n        <h1 className=\"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0\">\r\n          {title}\r\n        </h1>\r\n        \r\n        {/* Center - Cherry Blossom Image */}\r\n        <div className=\"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10\">\r\n          <Image \r\n            src={cherryBlossom} \r\n            alt=\"Cherry Blossom\" \r\n            width={130} \r\n            height={100} \r\n            style={{ width: 'auto', height: 'auto' }}\r\n            className=\"object-contain\"\r\n          />\r\n        </div>\r\n        \r\n        {/* Right side - Breadcrumbs */}\r\n        <div className=\"flex items-center space-x-2 text-sm z-20\">\r\n          {breadcrumbs.map((crumb, index) => (\r\n            <React.Fragment key={index}>\r\n              <Link \r\n                href={crumb.href} \r\n                className=\"hover:text-[#FE904B] transition-colors\"\r\n              >\r\n                {crumb.label}\r\n              </Link>\r\n              \r\n              {index < breadcrumbs.length - 1 && (\r\n                <span className=\"text-gray-400\">›</span>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageHero;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA,igBAAiE,8BAA8B;;;;;;AAU/F,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,WAAW,EAAiB;IACrD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;8BACX;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,4SAAA,CAAA,UAAa;wBAClB,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,OAAO;4BAAE,OAAO;4BAAQ,QAAQ;wBAAO;wBACvC,WAAU;;;;;;;;;;;8BAKd,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;;gCAGb,QAAQ,YAAY,MAAM,GAAG,mBAC5B,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;2BATf;;;;;;;;;;;;;;;;;;;;;AAiBjC;uCAEe", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/api/service/serviceApi.ts"], "sourcesContent": ["// Service API module\nimport { apiClient } from '../../customaxios';\nimport { buildUrl } from '../../globalurl';\n\n// Types for service API\nexport interface Service {\n  _id: string;\n  id: string;\n  title: string;\n  description: string;\n  description2?: string;\n  shortDescription: string;\n  slug: string;\n  category: string;\n  price: number;\n  duration: string;\n  features: string[];\n  images: string[];\n  image: string; // Primary image URL\n  howWeDoIt?: Array<{\n    title: string;\n    description: string;\n    icon?: string;\n  }>;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Gallery {\n  id: string;\n  title: string;\n  description: string;\n  imageUrl: string;\n  category: string;\n  tags: string[];\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface GalleryCategory {\n  id: string;\n  name: string;\n  slug: string;\n  description: string;\n  imageCount: number;\n}\n\nexport interface Review {\n  _id: string;\n  id: string;\n  name: string;\n  email: string;\n  rating: number;\n  comment: string;\n  review: string; // Alias for comment\n  relationship: string;\n  serviceId?: string;\n  isApproved: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ServiceQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  category?: string;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface GalleryQueryParams {\n  page?: number;\n  limit?: number;\n  category?: string;\n  tags?: string[];\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface ServicesResponse {\n  success: boolean;\n  message: string;\n  data: {\n    services: Service[];\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\nexport interface ServiceResponse {\n  success: boolean;\n  message: string;\n  data?: Service;\n}\n\nexport interface GalleryResponse {\n  gallery: Gallery[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface ReviewsResponse {\n  success: boolean;\n  message: string;\n  data: {\n    reviews: Review[];\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\n// Service API functions\nexport const serviceApi = {\n  // Get all services with pagination and filters\n  getServices: async (params: ServiceQueryParams = {}): Promise<ServicesResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ServicesResponse>(\n      buildUrl(`/services?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get single service by ID or slug\n  getServiceById: async (id: string): Promise<ServiceResponse> => {\n    const response = await apiClient.get<ServiceResponse>(\n      buildUrl(`/services/${id}`)\n    );\n    return response.data;\n  },\n\n  // Get service by slug\n  getServiceBySlug: async (slug: string): Promise<ServiceResponse> => {\n    const response = await apiClient.get<ServiceResponse>(\n      buildUrl(`/services/slug/${slug}`)\n    );\n    return response.data;\n  },\n\n  // Get service categories\n  getServiceCategories: async (): Promise<string[]> => {\n    const response = await apiClient.get<{ categories: string[] }>(\n      buildUrl('/services/categories')\n    );\n    return response.data.categories;\n  },\n};\n\n// Gallery API functions\nexport const galleryApi = {\n  // Get all gallery items with pagination and filters\n  getGallery: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryParams = new URLSearchParams();\n\n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        if (Array.isArray(value)) {\n          value.forEach(v => queryParams.append(key, v.toString()));\n        } else {\n          queryParams.append(key, value.toString());\n        }\n      }\n    });\n\n    const response = await apiClient.get<GalleryResponse>(\n      buildUrl(`/gallery?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Alias for getGallery - for compatibility\n  getGalleryItems: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    return galleryApi.getGallery(params);\n  },\n\n  // Get gallery categories\n  getGalleryCategories: async (): Promise<GalleryCategory[]> => {\n    const response = await apiClient.get<{ categories: GalleryCategory[] }>(\n      buildUrl('/gallery/categories')\n    );\n    return response.data.categories;\n  },\n\n  // Get gallery by category\n  getGalleryByCategory: async (category: string, params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<GalleryResponse>(\n      buildUrl(`/gallery/category/${category}?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n};\n\n// Reviews API functions\nexport const reviewsApi = {\n  // Get all reviews with pagination\n  getReviews: async (params: { page?: number; limit?: number; serviceId?: string; sortBy?: string; sortOrder?: 'asc' | 'desc' } = {}): Promise<ReviewsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ReviewsResponse>(\n      buildUrl(`/reviews?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get reviews for a specific service\n  getServiceReviews: async (serviceId: string, params: { page?: number; limit?: number } = {}): Promise<ReviewsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ReviewsResponse>(\n      buildUrl(`/reviews/service/${serviceId}?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n};\n\n// Helper functions\nexport const serviceHelpers = {\n  // Format price for display\n  formatPrice: (price: number): string => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0,\n    }).format(price);\n  },\n\n  // Create slug from title\n  createSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9 -]/g, '')\n      .replace(/\\s+/g, '-')\n      .replace(/-+/g, '-')\n      .trim();\n  },\n\n  // Truncate text\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + '...';\n  },\n\n  // Format rating for display\n  formatRating: (rating: number): string => {\n    return rating.toFixed(1);\n  },\n\n  // Get star rating array for display\n  getStarRating: (rating: number): { filled: number; half: boolean; empty: number } => {\n    const filled = Math.floor(rating);\n    const half = rating % 1 >= 0.5;\n    const empty = 5 - filled - (half ? 1 : 0);\n\n    return { filled, half, empty };\n  },\n\n  // Format relationship for display\n  formatRelationship: (relationship: string): string => {\n    return relationship\n      .split('-')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n  },\n\n  // Format card title for display\n  formatCardTitle: (title: string): string => {\n    return title\n      .split(' ')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n      .join(' ');\n  },\n\n  // Format card description for display\n  formatCardDescription: (description: string, maxLength: number = 150): string => {\n    if (!description) return '';\n    if (description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  },\n};\n\n// Export individual functions for easier imports\nexport const getServices = serviceApi.getServices;\nexport const getServiceById = serviceApi.getServiceById;\nexport const getServiceBySlug = serviceApi.getServiceBySlug;\nexport const getServiceCategories = serviceApi.getServiceCategories;\nexport const getGallery = galleryApi.getGallery;\nexport const getGalleryCategories = galleryApi.getGalleryCategories;\nexport const getGalleryByCategory = galleryApi.getGalleryByCategory;\nexport const getReviews = reviewsApi.getReviews;\nexport const getServiceReviews = reviewsApi.getServiceReviews;\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;;;;;;;;;;;;AACrB;AACA;;;AAuHO,MAAM,aAAa;IACxB,+CAA+C;IAC/C,aAAa,OAAO,SAA6B,CAAC,CAAC;QACjD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,UAAU,EAAE,YAAY,QAAQ,IAAI;QAEhD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,UAAU,EAAE,IAAI;QAE5B,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,eAAe,EAAE,MAAM;QAEnC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI,CAAC,UAAU;IACjC;AACF;AAGO,MAAM,aAAa;IACxB,oDAAoD;IACpD,YAAY,OAAO,SAA6B,CAAC,CAAC;QAChD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,YAAY,MAAM,CAAC,KAAK,EAAE,QAAQ;gBACvD,OAAO;oBACL,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACxC;YACF;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,SAAS,EAAE,YAAY,QAAQ,IAAI;QAE/C,OAAO,SAAS,IAAI;IACtB;IAEA,2CAA2C;IAC3C,iBAAiB,OAAO,SAA6B,CAAC,CAAC;QACrD,OAAO,WAAW,UAAU,CAAC;IAC/B;IAEA,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI,CAAC,UAAU;IACjC;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO,UAAkB,SAA6B,CAAC,CAAC;QAC5E,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAE,YAAY,QAAQ,IAAI;QAEpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,kCAAkC;IAClC,YAAY,OAAO,SAA6G,CAAC,CAAC;QAChI,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,SAAS,EAAE,YAAY,QAAQ,IAAI;QAE/C,OAAO,SAAS,IAAI;IACtB;IAEA,qCAAqC;IACrC,mBAAmB,OAAO,WAAmB,SAA4C,CAAC,CAAC;QACzF,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE,YAAY,QAAQ,IAAI;QAEpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,2BAA2B;IAC3B,aAAa,CAAC;QACZ,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,yBAAyB;IACzB,YAAY,CAAC;QACX,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;IACT;IAEA,gBAAgB;IAChB,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;IAC/C;IAEA,4BAA4B;IAC5B,cAAc,CAAC;QACb,OAAO,OAAO,OAAO,CAAC;IACxB;IAEA,oCAAoC;IACpC,eAAe,CAAC;QACd,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,MAAM,OAAO,SAAS,KAAK;QAC3B,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC;QAExC,OAAO;YAAE;YAAQ;YAAM;QAAM;IAC/B;IAEA,kCAAkC;IAClC,oBAAoB,CAAC;QACnB,OAAO,aACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,gCAAgC;IAChC,iBAAiB,CAAC;QAChB,OAAO,MACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACpE,IAAI,CAAC;IACV;IAEA,sCAAsC;IACtC,uBAAuB,CAAC,aAAqB,YAAoB,GAAG;QAClE,IAAI,CAAC,aAAa,OAAO;QACzB,IAAI,YAAY,MAAM,IAAI,WAAW,OAAO;QAC5C,OAAO,YAAY,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;IACtD;AACF;AAGO,MAAM,cAAc,WAAW,WAAW;AAC1C,MAAM,iBAAiB,WAAW,cAAc;AAChD,MAAM,mBAAmB,WAAW,gBAAgB;AACpD,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,aAAa,WAAW,UAAU;AACxC,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,aAAa,WAAW,UAAU;AACxC,MAAM,oBAAoB,WAAW,iBAAiB", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/icons/services/servicerighttopflower.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 426, height: 203, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAALklEQVR42l2MCQoAMAzCTPf/P+9AmG1AUAhKB6CcFUGGFHLfLtulzpfmXRjvcQMdygBpJo5rIQAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 4 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,sJAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsK,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/%28pages%29/services/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport PageH<PERSON> from \"@/app/_components/page_hero/page_hero\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { serviceApi, serviceHelpers, Service } from '@/lib/api/service/serviceApi';\r\nimport { toast } from 'react-toastify';\r\nimport servicerighttopflower from \"@/public/image/icons/services/servicerighttopflower.png\";\r\n\r\nconst ServicesPage = () => {\r\n  const [services, setServices] = useState<Service[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const breadcrumbs = [\r\n    { label: \"HOME\", href: \"/\" },\r\n    { label: \"SERVICES\", href: \"/services\" }\r\n  ];\r\n\r\n  // Fetch services from API\r\n  const fetchServices = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n\r\n\r\n      const response = await serviceApi.getServices({\r\n        page: 1,\r\n        limit: 20,\r\n        sortBy: 'sortOrder',\r\n        sortOrder: 'asc'\r\n      });\r\n\r\n      if (response.success && response.data.services) {\r\n        setServices(response.data.services);\r\n        console.log('✅ Services loaded:', response.data.services.length, 'services');\r\n      } else {\r\n        throw new Error('Failed to fetch services');\r\n      }\r\n    } catch (err: any) {\r\n      console.error('Error fetching services:', err);\r\n      setError(err.message || 'Failed to load services');\r\n      toast.error('Failed to load services. Please try again later.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchServices();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <PageHero title=\"SERVICES\" breadcrumbs={breadcrumbs} />\r\n      \r\n      <div className=\"relative py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8\">\r\n        {/* Decorative flower image */}\r\n        <div className=\"absolute top-10 right-0 -z-10 hidden lg:block opacity-30\">\r\n          <Image\r\n            src={servicerighttopflower}\r\n            alt=\"Decorative flower\"\r\n            width={300}\r\n            height={250}\r\n            className=\"object-contain\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Section Header */}\r\n          <div className=\"text-center mb-12 sm:mb-16 lg:mb-20\">\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 font-urbanist uppercase text-gray-900 leading-tight\">\r\n              Our Professional Services\r\n            </h2>\r\n            <div className=\"w-20 sm:w-24 h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 mx-auto rounded-full mb-4 sm:mb-6\"></div>\r\n            \r\n          </div>\r\n\r\n          {/* Loading State */}\r\n          {isLoading && (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6\">\r\n              {[...Array(6)].map((_, index) => (\r\n                <div key={index} className=\"bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] p-4 sm:p-6 animate-pulse\">\r\n                  <div className=\"flex justify-center mb-4 sm:mb-6\">\r\n                    <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gray-300 rounded-full\"></div>\r\n                  </div>\r\n                  <div className=\"h-4 sm:h-5 bg-gray-300 rounded mb-3\"></div>\r\n                  <div className=\"h-3 sm:h-4 bg-gray-300 rounded mb-2\"></div>\r\n                  <div className=\"h-3 sm:h-4 bg-gray-300 rounded mb-4 sm:mb-6\"></div>\r\n                  <div className=\"h-8 sm:h-10 bg-gray-300 rounded-lg\"></div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n\r\n          {/* Error State */}\r\n          {error && !isLoading && (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-red-500 mb-4\">\r\n                <svg className=\"w-16 h-16 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                </svg>\r\n                <p className=\"text-lg font-medium\">Failed to load services</p>\r\n                <p className=\"text-sm text-gray-600 mt-2\">{error}</p>\r\n              </div>\r\n              <button\r\n                onClick={fetchServices}\r\n                className=\"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          )}\r\n\r\n          {/* Services Grid */}\r\n          {!isLoading && !error && (\r\n            <>\r\n              {services.length === 0 ? (\r\n                <div className=\"text-center py-12\">\r\n                  <div className=\"text-gray-500\">\r\n                    <svg className=\"w-16 h-16 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\r\n                    </svg>\r\n                    <p className=\"text-lg font-medium\">No services available</p>\r\n                    <p className=\"text-sm text-gray-600 mt-2\">Please check back later</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6\">\r\n                  {services.map((service) => (\r\n                    <div\r\n                      key={service._id}\r\n                      className=\"bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] hover:shadow-lg transition-all duration-300 text-center p-4 sm:p-6 group hover:transform hover:-translate-y-1\"\r\n                    >\r\n                      {/* Service Icon Image */}\r\n                      <div className=\"flex justify-center mb-4 sm:mb-6\">\r\n                        <div className=\"w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center bg-orange-50 rounded-full group-hover:bg-orange-100 transition-colors duration-300 shadow-sm overflow-hidden\">\r\n                          {/* Backend Service Icon - PNG/JPG */}\r\n                          <img\r\n                            src={service.icons}\r\n                            alt={service.title}\r\n                            className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 rounded-full\"\r\n                            onLoad={() => {\r\n                              console.log('✅ Backend service icon loaded:', service.title);\r\n                            }}\r\n                            onError={() => {\r\n                              console.error('❌ Backend service icon failed:', service.title, service.icons);\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Service Title */}\r\n                      <h3 className=\"text-base sm:text-lg font-semibold uppercase mb-2 sm:mb-3 text-gray-800 group-hover:text-[#FE904D] transition-colors duration-300 leading-tight\">\r\n                        {serviceHelpers.formatCardTitle(service.title)}\r\n                      </h3>\r\n\r\n                      {/* Service Description */}\r\n                      <p className=\"text-gray-600 text-xs sm:text-sm mb-4 sm:mb-6 line-clamp-3 leading-relaxed px-2\">\r\n                        {serviceHelpers.formatCardDescription(service.description)}\r\n                      </p>\r\n\r\n                      {/* See More Button */}\r\n                      <Link href={`/services/${service._id}`}>\r\n                        <div className=\"inline-block border-2 border-[#FE904D] hover:bg-[#FE904D] hover:text-white text-[#FE904D] text-xs sm:text-sm font-medium uppercase tracking-wider py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-all duration-300 cursor-pointer group-hover:bg-[#FE904D] group-hover:text-white group-hover:shadow-md\">\r\n                          See More\r\n                        </div>\r\n                      </Link>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ServicesPage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AASA,MAAM,eAAe;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc;QAClB;YAAE,OAAO;YAAQ,MAAM;QAAI;QAC3B;YAAE,OAAO;YAAY,MAAM;QAAY;KACxC;IAED,0BAA0B;IAC1B,MAAM,gBAAgB;QACpB,IAAI;YACF,aAAa;YACb,SAAS;YAIT,MAAM,WAAW,MAAM,mIAAA,CAAA,aAAU,CAAC,WAAW,CAAC;gBAC5C,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;gBAC9C,YAAY,SAAS,IAAI,CAAC,QAAQ;gBAClC,QAAQ,GAAG,CAAC,sBAAsB,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACnE,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,IAAI,OAAO,IAAI;YACxB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE;;0BACE,8OAAC,6IAAA,CAAA,UAAQ;gBAAC,OAAM;gBAAW,aAAa;;;;;;0BAExC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,0VAAA,CAAA,UAAqB;4BAC1B,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0H;;;;;;kDAGxI,8OAAC;wCAAI,WAAU;;;;;;;;;;;;4BAKhB,2BACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;uCAPP;;;;;;;;;;4BAcf,SAAS,CAAC,2BACT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAE7C,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAOJ,CAAC,aAAa,CAAC,uBACd;0CACG,SAAS,MAAM,KAAK,kBACnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;yDAI9C,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAEC,WAAU;;8DAGV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAEb,cAAA,8OAAC;4DACC,KAAK,QAAQ,KAAK;4DAClB,KAAK,QAAQ,KAAK;4DAClB,WAAU;4DACV,QAAQ;gEACN,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,KAAK;4DAC7D;4DACA,SAAS;gEACP,QAAQ,KAAK,CAAC,kCAAkC,QAAQ,KAAK,EAAE,QAAQ,KAAK;4DAC9E;;;;;;;;;;;;;;;;8DAMN,8OAAC;oDAAG,WAAU;8DACX,mIAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,QAAQ,KAAK;;;;;;8DAI/C,8OAAC;oDAAE,WAAU;8DACV,mIAAA,CAAA,iBAAc,CAAC,qBAAqB,CAAC,QAAQ,WAAW;;;;;;8DAI3D,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,GAAG,EAAE;8DACpC,cAAA,8OAAC;wDAAI,WAAU;kEAA0S;;;;;;;;;;;;2CAjCtT,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;AA+CtC;uCAEe", "debugId": null}}]}