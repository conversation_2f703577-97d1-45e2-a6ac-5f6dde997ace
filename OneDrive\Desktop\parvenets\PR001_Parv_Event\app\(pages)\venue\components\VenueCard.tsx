"use client";

import React from 'react';
import Image from 'next/image';
import { VenueData } from '../venualldummydata/venue_dummydata';
import { venueHelpers } from '@/lib/api/venue/venueApi';

interface VenueCardProps {
  venue: VenueData;
  onBookNow: (venue: VenueData) => void;
}

const VenueCard = ({ venue, onBookNow }: VenueCardProps) => {
  // Format venue type for display
  const formatVenueType = (type: string) => {
    return venueHelpers.formatVenueType(type);
  };

  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
      {/* Card Image */}
      <div className="relative h-48">
        <Image
          src={venue.image}
          alt={venue.name}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover"
        />
        {/* Price badge - show if legacy price exists */}
        {venue.price && (
          <div className="absolute bottom-0 left-0 bg-white px-3 py-1 text-sm font-medium">
            From ₹ After Discuss
          </div>
        )}
        {/* Venue type badge */}
        <div className="absolute top-2 right-2 bg-[#FE904B] text-white px-2 py-1 text-xs rounded">
          {formatVenueType(venue.venueType)}
        </div>
      </div>

      {/* Card Content */}
      <div className="p-4">
        <h3 className="text-lg font-semibold mb-2 text-gray-900">{venue.name}</h3>

        {/* Location */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <div className='flex flex-row'>
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>{venue.location}</span>
          </div>
            <span>Capacity: {venue.capacity}</span>
        </div>

        {/* Capacity and Seats */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span>Guests: {venue.capacity}</span>
          </div>
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0A2.704 2.704 0 003 15.546V6.454c.523 0 1.046-.151 1.5-.454a2.704 2.704 0 013 0 2.704 2.704 0 003 0 2.704 2.704 0 013 0 2.704 2.704 0 003 0 2.704 2.704 0 013 0c.454.303.977.454 1.5.454v9.092z" />
            </svg>
            <span>Seats: {venue.seats}</span>
          </div>
        </div>

        <button
          onClick={() => onBookNow(venue)}
          className="w-full border border-[#FE904B] text-[#FE904B] hover:bg-[#FE904B] hover:text-white py-2 rounded-md transition-colors duration-200 font-medium"
        >
          Book Now
        </button>
      </div>
    </div>
  );
};

export default VenueCard;