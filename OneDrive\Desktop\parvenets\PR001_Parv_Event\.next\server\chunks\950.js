exports.id=950,exports.ids=[950],exports.modules={1933:(e,t)=>{"use strict";function a(e){var t;let{config:a,src:n,width:i,quality:o}=e,r=o||(null==(t=a.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return a.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+r+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),a.__next_img_default=!0;let n=a},2015:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return c}});let n=a(46143),i=a(71437),o=a(53293),r=a(72887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function c(e){let t=e.match(s);return t?l(t[2]):l(e)}function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let a=e.startsWith("...");return a&&(e=e.slice(3)),{key:e,repeat:a,optional:t}}function p(e,t,a){let n={},c=1,p=[];for(let u of(0,r.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>u.startsWith(e)),r=u.match(s);if(e&&r&&r[2]){let{key:t,optional:a,repeat:i}=l(r[2]);n[t]={pos:c++,repeat:i,optional:a},p.push("/"+(0,o.escapeStringRegexp)(e)+"([^/]+?)")}else if(r&&r[2]){let{key:e,repeat:t,optional:i}=l(r[2]);n[e]={pos:c++,repeat:t,optional:i},a&&r[1]&&p.push("/"+(0,o.escapeStringRegexp)(r[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";a&&r[1]&&(s=s.substring(1)),p.push(s)}else p.push("/"+(0,o.escapeStringRegexp)(u));t&&r&&r[3]&&p.push((0,o.escapeStringRegexp)(r[3]))}return{parameterizedRoute:p.join(""),groups:n}}function u(e,t){let{includeSuffix:a=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:o,groups:r}=p(e,a,n),s=o;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:r}}function d(e){let t,{interceptionMarker:a,getSafeRouteKey:n,segment:i,routeKeys:r,keyPrefix:s,backreferenceDuplicateKeys:c}=e,{key:p,optional:u,repeat:d}=l(i),m=p.replace(/\W/g,"");s&&(m=""+s+m);let f=!1;(0===m.length||m.length>30)&&(f=!0),isNaN(parseInt(m.slice(0,1)))||(f=!0),f&&(m=n());let h=m in r;s?r[m]=""+s+p:r[m]=p;let x=a?(0,o.escapeStringRegexp)(a):"";return t=h&&c?"\\k<"+m+">":d?"(?<"+m+">.+?)":"(?<"+m+">[^/]+?)",u?"(?:/"+x+t+")?":"/"+x+t}function m(e,t,a,c,l){let p,u=(p=0,()=>{let e="",t=++p;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),m={},f=[];for(let p of(0,r.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>p.startsWith(e)),r=p.match(s);if(e&&r&&r[2])f.push(d({getSafeRouteKey:u,interceptionMarker:r[1],segment:r[2],routeKeys:m,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:l}));else if(r&&r[2]){c&&r[1]&&f.push("/"+(0,o.escapeStringRegexp)(r[1]));let e=d({getSafeRouteKey:u,segment:r[2],routeKeys:m,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:l});c&&r[1]&&(e=e.substring(1)),f.push(e)}else f.push("/"+(0,o.escapeStringRegexp)(p));a&&r&&r[3]&&f.push((0,o.escapeStringRegexp)(r[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:m}}function f(e,t){var a,n,i;let o=m(e,t.prefixRouteKeys,null!=(a=t.includeSuffix)&&a,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),r=o.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(r+="(?:/)?"),{...u(e,t),namedRegex:"^"+r+"$",routeKeys:o.routeKeys}}function h(e,t){let{parameterizedRoute:a}=p(e,!1,!1),{catchAll:n=!0}=t;if("/"===a)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=m(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,a){let n=t[0],i=a[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!a[4];if(a[4])return!0;let o=Object.values(t[1])[0],r=Object.values(a[1])[0];return!o||!r||e(o,r)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=a(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:a}=(0,n.parsePath)(e);return a===t||a.startsWith(t+"/")}},3361:e=>{"use strict";e.exports=Object},5144:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return l}});let n=a(51499),i=a(38919);var o=i._("_maxConcurrency"),r=i._("_runningCount"),s=i._("_queue"),c=i._("_processNext");class l{enqueue(e){let t,a,i=new Promise((e,n)=>{t=e,a=n}),o=async()=>{try{n._(this,r)[r]++;let a=await e();t(a)}catch(e){a(e)}finally{n._(this,r)[r]--,n._(this,c)[c]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,c)[c](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,c)[c](!0)}}constructor(e=5){Object.defineProperty(this,c,{value:p}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,r,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,r)[r]=0,n._(this,s)[s]=[]}}function p(e){if(void 0===e&&(e=!1),(n._(this,r)[r]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return m},createSeededPrefetchCacheEntry:function(){return l},getOrCreatePrefetchCacheEntry:function(){return c},prunePrefetchCache:function(){return u}});let n=a(59008),i=a(59154),o=a(75076);function r(e,t,a){let n=e.pathname;return(t&&(n+=e.search),a)?""+a+"%"+n:n}function s(e,t,a){return r(e,t===i.PrefetchKind.FULL,a)}function c(e){let{url:t,nextUrl:a,tree:n,prefetchCache:o,kind:s,allowAliasing:c=!0}=e,l=function(e,t,a,n,o){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[a,null])){let a=r(e,!0,s),c=r(e,!1,s),l=e.search?a:c,p=n.get(l);if(p&&o){if(p.url.pathname===e.pathname&&p.url.search!==e.search)return{...p,aliased:!0};return p}let u=n.get(c);if(o&&e.search&&t!==i.PrefetchKind.FULL&&u&&!u.key.includes("%"))return{...u,aliased:!0}}if(t!==i.PrefetchKind.FULL&&o){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,a,o,c);return l?(l.status=f(l),l.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&l.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return p({tree:n,url:t,nextUrl:a,prefetchCache:o,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&l.kind===i.PrefetchKind.TEMPORARY&&(l.kind=s),l):p({tree:n,url:t,nextUrl:a,prefetchCache:o,kind:s||i.PrefetchKind.TEMPORARY})}function l(e){let{nextUrl:t,tree:a,prefetchCache:n,url:o,data:r,kind:c}=e,l=r.couldBeIntercepted?s(o,c,t):s(o,c),p={treeAtTimeOfPrefetch:a,data:Promise.resolve(r),kind:c,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:l,status:i.PrefetchCacheEntryStatus.fresh,url:o};return n.set(l,p),p}function p(e){let{url:t,kind:a,tree:r,nextUrl:c,prefetchCache:l}=e,p=s(t,a),u=o.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:r,nextUrl:c,prefetchKind:a}).then(e=>{let a;if(e.couldBeIntercepted&&(a=function(e){let{url:t,nextUrl:a,prefetchCache:n,existingCacheKey:i}=e,o=n.get(i);if(!o)return;let r=s(t,o.kind,a);return n.set(r,{...o,key:r}),n.delete(i),r}({url:t,existingCacheKey:p,nextUrl:c,prefetchCache:l})),e.prerendered){let t=l.get(null!=a?a:p);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:r,data:u,kind:a,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:p,status:i.PrefetchCacheEntryStatus.fresh,url:t};return l.set(p,d),d}function u(e){for(let[t,a]of e)f(a)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),m=1e3*Number("300");function f(e){let{kind:t,prefetchTime:a,lastUsedTime:n,staleTime:o}=e;return -1!==o?Date.now()<a+o?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:a)+d?n?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<a+m?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<a+m?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6341:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getPreviouslyRevalidatedTags:function(){return v},getUtils:function(){return x},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return m}});let n=a(79551),i=a(11959),o=a(12437),r=a(2015),s=a(78034),c=a(15526),l=a(72887),p=a(74722),u=a(46143),d=a(47912);function m(e,t,a){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX),o=e!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(n||o||t.includes(e)||a&&Object.keys(a.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function f(e,t,a){if(!a)return e;for(let n of Object.keys(a.groups)){let i,{optional:o,repeat:r}=a.groups[n],s=`[${r?"...":""}${n}]`;o&&(s=`[${s}]`);let c=t[n];i=Array.isArray(c)?c.map(e=>e&&encodeURIComponent(e)).join("/"):c?encodeURIComponent(c):"",e=e.replaceAll(s,i)}return e}function h(e,t,a,n){let i={};for(let o of Object.keys(t.groups)){let r=e[o];"string"==typeof r?r=(0,p.normalizeRscURL)(r):Array.isArray(r)&&(r=r.map(p.normalizeRscURL));let s=a[o],c=t.groups[o].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(r)?r.some(t=>t.includes(e)):null==r?void 0:r.includes(e)):null==r?void 0:r.includes(s))||void 0===r&&!(c&&n))return{params:{},hasValidParams:!1};c&&(!r||Array.isArray(r)&&1===r.length&&("index"===r[0]||r[0]===`[[...${o}]]`))&&(r=void 0,delete e[o]),r&&"string"==typeof r&&t.groups[o].repeat&&(r=r.split("/")),r&&(i[o]=r)}return{params:i,hasValidParams:!0}}function x({page:e,i18n:t,basePath:a,rewrites:n,pageIsDynamic:p,trailingSlash:u,caseSensitive:x}){let v,b,g;return p&&(v=(0,r.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),g=(b=(0,s.getRouteMatcher)(v))(e)),{handleRewrites:function(r,s){let d={},m=s.pathname,f=n=>{let l=(0,o.getPathMatch)(n.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!x});if(!s.pathname)return!1;let f=l(s.pathname);if((n.has||n.missing)&&f){let e=(0,c.matchHas)(r,s.query,n.has,n.missing);e?Object.assign(f,e):f=!1}if(f){let{parsedDestination:o,destQuery:r}=(0,c.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:f,query:s.query});if(o.protocol)return!0;if(Object.assign(d,r,f),Object.assign(s.query,o.query),delete o.query,Object.assign(s,o),!(m=s.pathname))return!1;if(a&&(m=m.replace(RegExp(`^${a}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(m,t.locales);m=e.pathname,s.query.nextInternalLocale=e.detectedLocale||f.nextInternalLocale}if(m===e)return!0;if(p&&b){let e=b(m);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])f(e);if(m!==e){let t=!1;for(let e of n.afterFiles||[])if(t=f(e))break;if(!t&&!(()=>{let t=(0,l.removeTrailingSlash)(m||"");return t===(0,l.removeTrailingSlash)(e)||(null==b?void 0:b(t))})()){for(let e of n.fallback||[])if(t=f(e))break}}return d},defaultRouteRegex:v,dynamicRouteMatcher:b,defaultRouteMatches:g,getParamsFromRouteMatches:function(e){if(!v)return null;let{groups:t,routeKeys:a}=v,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let a=(0,d.normalizeNextQueryParam)(e);a&&(n[a]=t,delete n[e])}let i={};for(let e of Object.keys(a)){let o=a[e];if(!o)continue;let r=t[o],s=n[e];if(!r.optional&&!s)return null;i[r.pos]=s}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>v&&g?h(e,v,g,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>m(e,t,v),interpolateDynamicPath:(e,t)=>f(e,t,v)}}function v(e,t){return"string"==typeof e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let n=a(96127);function i(e,t){if(e.startsWith(".")){let a=t.origin+t.pathname;return new URL((a.endsWith("/")?a:a+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6582:(e,t,a)=>{"use strict";var n="undefined"!=typeof Symbol&&Symbol,i=a(54544);e.exports=function(){return"function"==typeof n&&"function"==typeof Symbol&&"symbol"==typeof n("foo")&&"symbol"==typeof Symbol("bar")&&i()}},7315:e=>{"use strict";e.exports=RangeError},7932:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(t.bind(e)),e.jobs={}};function t(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},8304:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return r},getExtensionRegexString:function(){return c},isMetadataPage:function(){return u},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return l},isStaticMetadataRoute:function(){return p}});let n=a(12958),i=a(74722),o=a(70554),r={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],c=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function l(e,t,a){let i=(a?"":"?")+"$",o=`\\d?${a?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${c(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${c(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${c(["xml"],t)}${i}`),RegExp(`[\\\\/]${r.icon.filename}${o}${c(r.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${r.apple.filename}${o}${c(r.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${r.openGraph.filename}${o}${c(r.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${r.twitter.filename}${o}${c(r.twitter.extensions,t)}${i}`)],l=(0,n.normalizePathSep)(e);return s.some(e=>e.test(l))}function p(e){let t=e.replace(/\/route$/,"");return(0,o.isAppRouteRoute)(e)&&l(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function u(e){return!(0,o.isAppRouteRoute)(e)&&l(e,[],!1)}function d(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,o.isAppRouteRoute)(e)&&l(t,[],!1)}},8830:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),a(59154),a(25232),a(29651),a(28627),a(78866),a(75076),a(97936),a(35429);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9181:(e,t,a)=>{"use strict";var n=a(62427),i=a(81285),o=a(23471);e.exports=n?function(e){return n(e)}:i?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return i(e)}:o?function(e){return o(e)}:null},9707:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{addSearchParamsToPageSegments:function(){return u},handleAliasedPrefetchEntry:function(){return p}});let n=a(83913),i=a(89752),o=a(86770),r=a(57391),s=a(33123),c=a(33898),l=a(59435);function p(e,t,a,p,d){let m,f=t.tree,h=t.cache,x=(0,r.createHrefFromUrl)(p);if("string"==typeof a)return!1;for(let t of a){if(!function e(t){if(!t)return!1;let a=t[2];if(t[3])return!0;for(let t in a)if(e(a[t]))return!0;return!1}(t.seedData))continue;let a=t.tree;a=u(a,Object.fromEntries(p.searchParams));let{seedData:r,isRootRender:l,pathToSegment:d}=t,v=["",...d];a=u(a,Object.fromEntries(p.searchParams));let b=(0,o.applyRouterStatePatchToTree)(v,f,a,x),g=(0,i.createEmptyCacheNode)();if(l&&r){let t=r[1];g.loading=r[3],g.rsc=t,function e(t,a,i,o,r){if(0!==Object.keys(o[1]).length)for(let c in o[1]){let l,p=o[1][c],u=p[0],d=(0,s.createRouterCacheKey)(u),m=null!==r&&void 0!==r[2][c]?r[2][c]:null;if(null!==m){let e=m[1],a=m[3];l={lazyData:null,rsc:u.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:a,navigatedAt:t}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let f=a.parallelRoutes.get(c);f?f.set(d,l):a.parallelRoutes.set(c,new Map([[d,l]])),e(t,l,i,p,m)}}(e,g,h,a,r)}else g.rsc=h.rsc,g.prefetchRsc=h.prefetchRsc,g.loading=h.loading,g.parallelRoutes=new Map(h.parallelRoutes),(0,c.fillCacheWithNewSubTreeDataButOnlyLoading)(e,g,h,t);b&&(f=b,h=g,m=!0)}return!!m&&(d.patchedTree=f,d.cache=h,d.canonicalUrl=x,d.hashFragment=p.hash,(0,l.handleMutable)(t,d))}function u(e,t){let[a,i,...o]=e;if(a.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(a,t),i,...o];let r={};for(let[e,a]of Object.entries(i))r[e]=u(a,t);return[a,r,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10096:e=>{"use strict";e.exports=URIError},12437:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=a(35362);function i(e,t){let a=[],i=(0,n.pathToRegexp)(e,a,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,a);return(e,n)=>{if("string"!=typeof e)return!1;let i=o(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of a)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{VALID_LOADERS:function(){return a},imageConfigDefault:function(){return n}});let a=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},12958:(e,t)=>{"use strict";function a(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return a}})},14959:(e,t,a)=>{"use strict";e.exports=a(94041).vendored.contexts.AmpContext},15219:e=>{"use strict";e.exports=SyntaxError},15526:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{compileNonPath:function(){return u},matchHas:function(){return p},parseDestination:function(){return d},prepareDestination:function(){return m}});let n=a(35362),i=a(53293),o=a(76759),r=a(71437),s=a(9977),c=a(88212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function p(e,t,a,n){void 0===a&&(a=[]),void 0===n&&(n=[]);let i={},o=a=>{let n,o=a.key;switch(a.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[a.key]:(0,c.getCookieParser)(e.headers)()[a.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!a.value&&n)return i[function(e){let t="";for(let a=0;a<e.length;a++){let n=e.charCodeAt(a);(n>64&&n<91||n>96&&n<123)&&(t+=e[a])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+a.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===a.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!a.every(e=>o(e))||n.some(e=>o(e)))&&i}function u(e,t){if(!e.includes(":"))return e;for(let a of Object.keys(t))e.includes(":"+a)&&(e=e.replace(RegExp(":"+a+"\\*","g"),":"+a+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+a+"\\?","g"),":"+a+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+a+"\\+","g"),":"+a+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+a+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+a));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let a of Object.keys({...e.params,...e.query}))a&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(a),"g"),"__ESC_COLON_"+a));let a=(0,o.parseUrl)(t),n=a.pathname;n&&(n=l(n));let r=a.href;r&&(r=l(r));let s=a.hostname;s&&(s=l(s));let c=a.hash;return c&&(c=l(c)),{...a,pathname:n,hostname:s,href:r,hash:c}}function m(e){let t,a,i=Object.assign({},e.query);delete i[s.NEXT_RSC_UNION_QUERY];let o=d(e),{hostname:c,query:p}=o,m=o.pathname;o.hash&&(m=""+m+o.hash);let f=[],h=[];for(let e of((0,n.pathToRegexp)(m,h),h))f.push(e.name);if(c){let e=[];for(let t of((0,n.pathToRegexp)(c,e),e))f.push(t.name)}let x=(0,n.compile)(m,{validate:!1});for(let[a,i]of(c&&(t=(0,n.compile)(c,{validate:!1})),Object.entries(p)))Array.isArray(i)?p[a]=i.map(t=>u(l(t),e.params)):"string"==typeof i&&(p[a]=u(l(i),e.params));let v=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!v.some(e=>f.includes(e)))for(let t of v)t in p||(p[t]=e.params[t]);if((0,r.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let a=r.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(a){"(..)(..)"===a?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=a;break}}try{let[n,i]=(a=x(e.params)).split("#",2);t&&(o.hostname=t(e.params)),o.pathname=n,o.hash=(i?"#":"")+(i||""),delete o.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return o.query={...i,...o.query},{newUrl:a,destQuery:p,parsedDestination:o}}},16189:(e,t,a)=>{"use strict";var n=a(65773);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}})},17903:(e,t,a)=>{"use strict";e.exports=a(94041).vendored.contexts.ImageConfigContext},18468:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,a,o){let r=o.length<=2,[s,c]=o,l=(0,n.createRouterCacheKey)(c),p=a.parallelRoutes.get(s);if(!p)return;let u=t.parallelRoutes.get(s);if(u&&u!==p||(u=new Map(p),t.parallelRoutes.set(s,u)),r)return void u.delete(l);let d=p.get(l),m=u.get(l);m&&d&&(m===d&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes)},u.set(l,m)),e(m,d,(0,i.getNextFlightSegmentPath)(o)))}}});let n=a(33123),i=a(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{"use strict";function a(e){let t=e.indexOf("#"),a=e.indexOf("?"),n=a>-1&&(t<0||a<t);return n||t>-1?{pathname:e.substring(0,n?a:t),query:n?e.substring(a,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return a}})},19207:e=>{"use strict";e.exports=(e,t=process.argv)=>{let a=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(a+e),i=t.indexOf("--");return -1!==n&&(-1===i||n<i)}},19526:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},22308:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,a){let[n,i,,r]=t;for(let s in n.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==r&&(t[2]=a,t[3]="refresh"),i)e(i[s],a)}},refreshInactiveParallelSegments:function(){return r}});let n=a(56928),i=a(59008),o=a(83913);async function r(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:a,updatedTree:o,updatedCache:r,includeNextUrl:c,fetchedSegments:l,rootTree:p=o,canonicalUrl:u}=e,[,d,m,f]=o,h=[];if(m&&m!==u&&"refresh"===f&&!l.has(m)){l.add(m);let e=(0,i.fetchServerResponse)(new URL(m,location.origin),{flightRouterState:[p[0],p[1],p[2],"refetch"],nextUrl:c?a.nextUrl:null}).then(e=>{let{flightData:a}=e;if("string"!=typeof a)for(let e of a)(0,n.applyFlightData)(t,r,r,e)});h.push(e)}for(let e in d){let n=s({navigatedAt:t,state:a,updatedTree:d[e],updatedCache:r,includeNextUrl:c,fetchedSegments:l,rootTree:p,canonicalUrl:u});h.push(n)}await Promise.all(h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23471:(e,t,a)=>{"use strict";var n,i=a(70607),o=a(80036);try{n=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var r=!!n&&o&&o(Object.prototype,"__proto__"),s=Object,c=s.getPrototypeOf;e.exports=r&&"function"==typeof r.get?i([r.get]):"function"==typeof c&&function(e){return c(null==e?e:s(e))}},23736:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),a(44827);let n=a(42785);function i(e,t,a){void 0===a&&(a=!0);let i=new URL("http://n"),o=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:r,searchParams:s,search:c,hash:l,href:p,origin:u}=new URL(e,o);if(u!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:r,query:a?(0,n.searchParamsToUrlQuery)(s):void 0,search:c,hash:l,href:p.slice(u.length)}}},24642:(e,t)=>{"use strict";function a(e){let t=parseInt(e.slice(0,2),16),a=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=a>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let a=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(a[n]=e[n]);return a}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{extractInfoFromServerReferenceId:function(){return a},omitUnusedArgs:function(){return n}})},25232:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{handleExternalUrl:function(){return g},navigateReducer:function(){return function e(t,a){let{url:w,isExternalUrl:_,navigateType:R,shouldScroll:E,allowAliasing:j}=a,O={},{hash:P}=w,S=(0,i.createHrefFromUrl)(w),T="push"===R;if((0,x.prunePrefetchCache)(t.prefetchCache),O.preserveCustomHistoryState=!1,O.pendingPush=T,_)return g(t,O,w.toString(),T);if(document.getElementById("__next-page-redirect"))return g(t,O,S,T);let k=(0,x.getOrCreatePrefetchCacheEntry)({url:w,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:j}),{treeAtTimeOfPrefetch:A,data:C}=k;return d.prefetchQueue.bump(C),C.then(d=>{let{flightData:x,canonicalUrl:_,postponed:R}=d,j=Date.now(),C=!1;if(k.lastUsedTime||(k.lastUsedTime=j,C=!0),k.aliased){let n=(0,b.handleAliasedPrefetchEntry)(j,t,x,w,O);return!1===n?e(t,{...a,allowAliasing:!1}):n}if("string"==typeof x)return g(t,O,x,T);let M=_?(0,i.createHrefFromUrl)(_):S;if(P&&t.canonicalUrl.split("#",1)[0]===M.split("#",1)[0])return O.onlyHashChange=!0,O.canonicalUrl=M,O.shouldScroll=E,O.hashFragment=P,O.scrollableSegments=[],(0,p.handleMutable)(t,O);let N=t.tree,U=t.cache,L=[];for(let e of x){let{pathToSegment:a,seedData:i,head:p,isHeadPartial:d,isRootRender:x}=e,b=e.tree,_=["",...a],E=(0,r.applyRouterStatePatchToTree)(_,N,b,S);if(null===E&&(E=(0,r.applyRouterStatePatchToTree)(_,A,b,S)),null!==E){if(i&&x&&R){let e=(0,h.startPPRNavigation)(j,U,N,b,i,p,d,!1,L);if(null!==e){if(null===e.route)return g(t,O,S,T);E=e.route;let a=e.node;null!==a&&(O.cache=a);let i=e.dynamicRequestTree;if(null!==i){let a=(0,n.fetchServerResponse)(w,{flightRouterState:i,nextUrl:t.nextUrl});(0,h.listenForDynamicRequest)(e,a)}}else E=b}else{if((0,c.isNavigatingToNewRootLayout)(N,E))return g(t,O,S,T);let n=(0,m.createEmptyCacheNode)(),i=!1;for(let t of(k.status!==l.PrefetchCacheEntryStatus.stale||C?i=(0,u.applyFlightData)(j,U,n,e,k):(i=function(e,t,a,n){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),y(n).map(e=>[...a,...e])))(0,v.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(n,U,a,b),k.lastUsedTime=j),(0,s.shouldHardNavigate)(_,N)?(n.rsc=U.rsc,n.prefetchRsc=U.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(n,U,a),O.cache=n):i&&(O.cache=n,U=n),y(b))){let e=[...a,...t];e[e.length-1]!==f.DEFAULT_SEGMENT_KEY&&L.push(e)}}N=E}}return O.patchedTree=N,O.canonicalUrl=M,O.scrollableSegments=L,O.hashFragment=P,O.shouldScroll=E,(0,p.handleMutable)(t,O)},()=>t)}}});let n=a(59008),i=a(57391),o=a(18468),r=a(86770),s=a(65951),c=a(2030),l=a(59154),p=a(59435),u=a(56928),d=a(75076),m=a(89752),f=a(83913),h=a(65956),x=a(5334),v=a(97464),b=a(9707);function g(e,t,a,n){return t.mpaNavigation=!0,t.canonicalUrl=a,t.pendingPush=n,t.scrollableSegments=void 0,(0,p.handleMutable)(e,t)}function y(e){let t=[],[a,n]=e;if(0===Object.keys(n).length)return[[a]];for(let[e,i]of Object.entries(n))for(let n of y(i))""===a?t.push([e,...n]):t.push([a,e,...n]);return t}a(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,a)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),a(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,a){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),r=(a||{}).decode||e,s=0;s<o.length;s++){var c=o[s],l=c.indexOf("=");if(!(l<0)){var p=c.substr(0,l).trim(),u=c.substr(++l,c.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==i[p]&&(i[p]=function(e,t){try{return t(e)}catch(t){return e}}(u,r))}}return i},t.serialize=function(e,t,n){var o=n||{},r=o.encode||a;if("function"!=typeof r)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=r(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=o.maxAge){var l=o.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");c+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");c+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(c+="; HttpOnly"),o.secure&&(c+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,a=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},26736:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=a(2255);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=a(57391),i=a(70642);function o(e,t){var a;let{url:o,tree:r}=t,s=(0,n.createHrefFromUrl)(o),c=r||e.tree,l=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:l,prefetchCache:e.prefetchCache,tree:c,nextUrl:null!=(a=(0,i.extractPathFromFlightRouterState)(c))?a:o.pathname}}a(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return p}});let n=a(57391),i=a(86770),o=a(2030),r=a(25232),s=a(56928),c=a(59435),l=a(89752);function p(e,t){let{serverResponse:{flightData:a,canonicalUrl:p},navigatedAt:u}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof a)return(0,r.handleExternalUrl)(e,d,a,e.pushRef.pendingPush);let m=e.tree,f=e.cache;for(let t of a){let{segmentPath:a,tree:c}=t,h=(0,i.applyRouterStatePatchToTree)(["",...a],m,c,e.canonicalUrl);if(null===h)return e;if((0,o.isNavigatingToNewRootLayout)(m,h))return(0,r.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let x=p?(0,n.createHrefFromUrl)(p):void 0;x&&(d.canonicalUrl=x);let v=(0,l.createEmptyCacheNode)();(0,s.applyFlightData)(u,f,v,t),d.patchedTree=h,d.cache=v,f=v,m=h}return(0,c.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return r}});let n=a(84441)._(a(76715)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:a}=e,o=e.protocol||"",r=e.pathname||"",s=e.hash||"",c=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:a&&(l=t+(~a.indexOf(":")?"["+a+"]":a),e.port&&(l+=":"+e.port)),c&&"object"==typeof c&&(c=String(n.urlQueryToSearchParams(c)));let p=e.search||c&&"?"+c||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==l?(l="//"+(l||""),r&&"/"!==r[0]&&(r="/"+r)):l||(l=""),s&&"#"!==s[0]&&(s="#"+s),p&&"?"!==p[0]&&(p="?"+p),""+o+l+(r=r.replace(/[?#]/g,encodeURIComponent))+(p=p.replace("#","%23"))+s}let r=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},30461:e=>{"use strict";e.exports=Math.floor},30474:(e,t,a)=>{"use strict";a.d(t,{default:()=>i.a});var n=a(31261),i=a.n(n)},30512:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return h},defaultHead:function(){return u}});let n=a(59630),i=a(84441),o=a(60687),r=i._(a(43210)),s=n._(a(47755)),c=a(14959),l=a(89513),p=a(34604);function u(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===r.default.Fragment?e.concat(r.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}a(50148);let m=["name","httpEquiv","charSet","itemProp"];function f(e,t){let{inAmpMode:a}=t;return e.reduce(d,[]).reverse().concat(u(a).reverse()).filter(function(){let e=new Set,t=new Set,a=new Set,n={};return i=>{let o=!0,r=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){r=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(i.props.hasOwnProperty(t))if("charSet"===t)a.has(t)?o=!1:a.add(t);else{let e=i.props[t],a=n[t]||new Set;("name"!==t||!r)&&a.has(e)?o=!1:(a.add(e),n[t]=a)}}}return o}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!a&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,r.default.cloneElement(e,t)}return r.default.cloneElement(e,{key:n})})}let h=function(e){let{children:t}=e,a=(0,r.useContext)(c.AmpStateContext),n=(0,r.useContext)(l.HeadManagerContext);return(0,o.jsx)(s.default,{reduceComponentsToState:f,headManager:n,inAmpMode:(0,p.isInAmpMode)(a),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30660:(e,t)=>{"use strict";function a(e){let t=5381;for(let a=0;a<e.length;a++)t=(t<<5)+t+e.charCodeAt(a)|0;return t>>>0}function n(e){return a(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{djb2Hash:function(){return a},hexHash:function(){return n}})},30678:(e,t,a)=>{let n=a(83997),i=a(28354);t.init=function(e){e.inspectOpts={};let a=Object.keys(t.inspectOpts);for(let n=0;n<a.length;n++)e.inspectOpts[a[n]]=t.inspectOpts[a[n]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(a){let{namespace:n,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),o=`  ${i};1m${n} \u001B[0m`;a[0]=o+a[0].split("\n").join("\n"+o),a.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else a[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+a[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=a(39228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let a=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[a]=n,e},{}),e.exports=a(96211)(t);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},31261:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return c},getImageProps:function(){return s}});let n=a(59630),i=a(44953),o=a(46533),r=n._(a(1933));function s(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let c=o.Image},31658:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return m}});let n=a(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(a(78671)),o=a(6341),r=a(2015),s=a(30660),c=a(74722),l=a(12958),p=a(35499);function u(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let a="";return t.split("/").some(e=>(0,p.isGroupSegment)(e)||(0,p.isParallelRouteSegment)(e))&&(a=(0,s.djb2Hash)(t).toString(36).slice(0,6)),a}function d(e,t,a){let n=(0,c.normalizeAppPath)(e),s=(0,r.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),p=(0,o.interpolateDynamicPath)(n,t,s),{name:d,ext:m}=i.default.parse(a),f=u(i.default.posix.join(e,d)),h=f?`-${f}`:"";return(0,l.normalizePathSep)(i.default.join(p,`${d}${h}${m}`))}function m(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,a="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":a=u(e),!t.endsWith("/route")){let{dir:e,name:n,ext:o}=i.default.parse(t);t=i.default.posix.join(e,`${n}${a?`-${a}`:""}${o}`,"route")}return t}function f(e,t){let a=e.endsWith("/route"),n=a?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(a?"/route":"")}},32708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},33793:(e,t,a)=>{"use strict";a.d(t,{RvV:()=>i});var n=a(90296);function i(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64h98.2V334.2H109.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H255V480H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z"},child:[]}]})(e)}},33898:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{fillCacheWithNewSubTreeData:function(){return c},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return l}});let n=a(34400),i=a(41500),o=a(33123),r=a(83913);function s(e,t,a,s,c,l){let{segmentPath:p,seedData:u,tree:d,head:m}=s,f=t,h=a;for(let t=0;t<p.length;t+=2){let a=p[t],s=p[t+1],x=t===p.length-2,v=(0,o.createRouterCacheKey)(s),b=h.parallelRoutes.get(a);if(!b)continue;let g=f.parallelRoutes.get(a);g&&g!==b||(g=new Map(b),f.parallelRoutes.set(a,g));let y=b.get(v),w=g.get(v);if(x){if(u&&(!w||!w.lazyData||w===y)){let t=u[0],a=u[1],o=u[3];w={lazyData:null,rsc:l||t!==r.PAGE_SEGMENT_KEY?a:null,prefetchRsc:null,head:null,prefetchHead:null,loading:o,parallelRoutes:l&&y?new Map(y.parallelRoutes):new Map,navigatedAt:e},y&&l&&(0,n.invalidateCacheByRouterState)(w,y,d),l&&(0,i.fillLazyItemsTillLeafWithHead)(e,w,y,d,u,m,c),g.set(v,w)}continue}w&&y&&(w===y&&(w={lazyData:w.lazyData,rsc:w.rsc,prefetchRsc:w.prefetchRsc,head:w.head,prefetchHead:w.prefetchHead,parallelRoutes:new Map(w.parallelRoutes),loading:w.loading},g.set(v,w)),f=w,h=y)}}function c(e,t,a,n,i){s(e,t,a,n,i,!0)}function l(e,t,a,n,i){s(e,t,a,n,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=a(33123);function i(e,t,a){for(let i in a[1]){let o=a[1][i][0],r=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(r),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34604:(e,t)=>{"use strict";function a(e){let{ampFirst:t=!1,hybrid:a=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||a&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return a}})},35362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var a=function(e){for(var t=[],a=0;a<e.length;){var n=e[a];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:a,value:e[a++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:a++,value:e[a++]});continue}if("{"===n){t.push({type:"OPEN",index:a,value:e[a++]});continue}if("}"===n){t.push({type:"CLOSE",index:a,value:e[a++]});continue}if(":"===n){for(var i="",o=a+1;o<e.length;){var r=e.charCodeAt(o);if(r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||95===r){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+a);t.push({type:"NAME",index:a,value:i}),a=o;continue}if("("===n){var s=1,c="",o=a+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){c+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);c+=e[o++]}if(s)throw TypeError("Unbalanced pattern at "+a);if(!c)throw TypeError("Missing pattern at "+a);t.push({type:"PATTERN",index:a,value:c}),a=o;continue}t.push({type:"CHAR",index:a,value:e[a++]})}return t.push({type:"END",index:a,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,r="[^"+i(t.delimiter||"/#?")+"]+?",s=[],c=0,l=0,p="",u=function(e){if(l<a.length&&a[l].type===e)return a[l++].value},d=function(e){var t=u(e);if(void 0!==t)return t;var n=a[l];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},m=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};l<a.length;){var f=u("CHAR"),h=u("NAME"),x=u("PATTERN");if(h||x){var v=f||"";-1===o.indexOf(v)&&(p+=v,v=""),p&&(s.push(p),p=""),s.push({name:h||c++,prefix:v,suffix:"",pattern:x||r,modifier:u("MODIFIER")||""});continue}var b=f||u("ESCAPED_CHAR");if(b){p+=b;continue}if(p&&(s.push(p),p=""),u("OPEN")){var v=m(),g=u("NAME")||"",y=u("PATTERN")||"",w=m();d("CLOSE"),s.push({name:g||(y?c++:""),pattern:g&&!y?r:y,prefix:v,suffix:w,modifier:u("MODIFIER")||""});continue}d("END")}return s}function a(e,t){void 0===t&&(t={});var a=o(t),n=t.encode,i=void 0===n?function(e){return e}:n,r=t.validate,s=void 0===r||r,c=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",a)});return function(t){for(var a="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){a+=o;continue}var r=t?t[o.name]:void 0,l="?"===o.modifier||"*"===o.modifier,p="*"===o.modifier||"+"===o.modifier;if(Array.isArray(r)){if(!p)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===r.length){if(l)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var u=0;u<r.length;u++){var d=i(r[u],o);if(s&&!c[n].test(d))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+d+'"');a+=o.prefix+d+o.suffix}continue}if("string"==typeof r||"number"==typeof r){var d=i(String(r),o);if(s&&!c[n].test(d))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+d+'"');a+=o.prefix+d+o.suffix;continue}if(!l){var m=p?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+m)}}return a}}function n(e,t,a){void 0===a&&(a={});var n=a.decode,i=void 0===n?function(e){return e}:n;return function(a){var n=e.exec(a);if(!n)return!1;for(var o=n[0],r=n.index,s=Object.create(null),c=1;c<n.length;c++)!function(e){if(void 0!==n[e]){var a=t[e-1];"*"===a.modifier||"+"===a.modifier?s[a.name]=n[e].split(a.prefix+a.suffix).map(function(e){return i(e,a)}):s[a.name]=i(n[e],a)}}(c);return{path:o,index:r,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function r(e,t,a){void 0===a&&(a={});for(var n=a.strict,r=void 0!==n&&n,s=a.start,c=a.end,l=a.encode,p=void 0===l?function(e){return e}:l,u="["+i(a.endsWith||"")+"]|$",d="["+i(a.delimiter||"/#?")+"]",m=void 0===s||s?"^":"",f=0;f<e.length;f++){var h=e[f];if("string"==typeof h)m+=i(p(h));else{var x=i(p(h.prefix)),v=i(p(h.suffix));if(h.pattern)if(t&&t.push(h),x||v)if("+"===h.modifier||"*"===h.modifier){var b="*"===h.modifier?"?":"";m+="(?:"+x+"((?:"+h.pattern+")(?:"+v+x+"(?:"+h.pattern+"))*)"+v+")"+b}else m+="(?:"+x+"("+h.pattern+")"+v+")"+h.modifier;else m+="("+h.pattern+")"+h.modifier;else m+="(?:"+x+v+")"+h.modifier}}if(void 0===c||c)r||(m+=d+"?"),m+=a.endsWith?"(?="+u+")":"$";else{var g=e[e.length-1],y="string"==typeof g?d.indexOf(g[g.length-1])>-1:void 0===g;r||(m+="(?:"+d+"(?="+u+"))?"),y||(m+="(?="+d+"|"+u+")")}return new RegExp(m,o(a))}function s(t,a,n){if(t instanceof RegExp){if(!a)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var c=0;c<i.length;c++)a.push({name:c,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,a,n).source}).join("|")+")",o(n)):r(e(t,n),a,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return a(e(t,n),n)},t.tokensToFunction=a,t.match=function(e,t){var a=[];return n(s(e,a,t),a,t)},t.regexpToFunction=n,t.tokensToRegexp=r,t.pathToRegexp=s})(),e.exports=t})()},35416:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return c},isBot:function(){return s}});let n=a(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=n.HTML_LIMITED_BOT_UA_RE.source;function r(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||r(e)}function c(e){return i.test(e)?"dom":r(e)?"html":void 0}},35429:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return T}});let n=a(11264),i=a(11448),o=a(91563),r=a(59154),s=a(6361),c=a(57391),l=a(25232),p=a(86770),u=a(2030),d=a(59435),m=a(41500),f=a(89752),h=a(68214),x=a(96493),v=a(22308),b=a(74007),g=a(36875),y=a(97860),w=a(5334),_=a(25942),R=a(26736),E=a(24642);a(50593);let{createFromFetch:j,createTemporaryReferenceSet:O,encodeReply:P}=a(19357);async function S(e,t,a){let r,c,{actionId:l,actionArgs:p}=a,u=O(),d=(0,E.extractInfoFromServerReferenceId)(l),m="use-cache"===d.type?(0,E.omitUnusedArgs)(p,d):p,f=await P(m,{temporaryReferences:u}),h=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:l,[o.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[o.NEXT_URL]:t}:{}},body:f}),x=h.headers.get("x-action-redirect"),[v,g]=(null==x?void 0:x.split(";"))||[];switch(g){case"push":r=y.RedirectType.push;break;case"replace":r=y.RedirectType.replace;break;default:r=void 0}let w=!!h.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(h.headers.get("x-action-revalidated")||"[[],0,0]");c={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){c={paths:[],tag:!1,cookie:!1}}let _=v?(0,s.assignLocation)(v,new URL(e.canonicalUrl,window.location.href)):void 0,R=h.headers.get("content-type");if(null==R?void 0:R.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await j(Promise.resolve(h),{callServer:n.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:u});return v?{actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:_,redirectType:r,revalidatedParts:c,isPrerender:w}:{actionResult:e.a,actionFlightData:(0,b.normalizeFlightData)(e.f),redirectLocation:_,redirectType:r,revalidatedParts:c,isPrerender:w}}if(h.status>=400)throw Object.defineProperty(Error("text/plain"===R?await h.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:_,redirectType:r,revalidatedParts:c,isPrerender:w}}function T(e,t){let{resolve:a,reject:n}=t,i={},o=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,h.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,b=Date.now();return S(e,s,t).then(async h=>{let E,{actionResult:j,actionFlightData:O,redirectLocation:P,redirectType:S,isPrerender:T,revalidatedParts:k}=h;if(P&&(S===y.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=E=(0,c.createHrefFromUrl)(P,!1)),!O)return(a(j),P)?(0,l.handleExternalUrl)(e,i,P.href,e.pushRef.pendingPush):e;if("string"==typeof O)return a(j),(0,l.handleExternalUrl)(e,i,O,e.pushRef.pendingPush);let A=k.paths.length>0||k.tag||k.cookie;for(let n of O){let{tree:r,seedData:c,head:d,isRootRender:h}=n;if(!h)return console.log("SERVER ACTION APPLY FAILED"),a(j),e;let g=(0,p.applyRouterStatePatchToTree)([""],o,r,E||e.canonicalUrl);if(null===g)return a(j),(0,x.handleSegmentMismatch)(e,t,r);if((0,u.isNavigatingToNewRootLayout)(o,g))return a(j),(0,l.handleExternalUrl)(e,i,E||e.canonicalUrl,e.pushRef.pendingPush);if(null!==c){let t=c[1],a=(0,f.createEmptyCacheNode)();a.rsc=t,a.prefetchRsc=null,a.loading=c[3],(0,m.fillLazyItemsTillLeafWithHead)(b,a,void 0,r,c,d,void 0),i.cache=a,i.prefetchCache=new Map,A&&await (0,v.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:g,updatedCache:a,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=g,o=g}return P&&E?(A||((0,w.createSeededPrefetchCacheEntry)({url:P,data:{flightData:O,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:T?r.PrefetchKind.FULL:r.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),n((0,g.getRedirectError)((0,R.hasBasePath)(E)?(0,_.removeBasePath)(E):E,S||y.RedirectType.push))):a(j),(0,d.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35836:(e,t,a)=>{"use strict";var n=a(83644),i=a(28354),o=a(33873),r=a(81630),s=a(55591),c=a(79551).parse,l=a(29021),p=a(27910).Stream,u=a(95930),d=a(85026),m=a(78002),f=a(56786),h=a(41425);function x(e){if(!(this instanceof x))return new x(e);for(var t in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[t]=e[t]}i.inherits(x,n),x.LINE_BREAK="\r\n",x.DEFAULT_CONTENT_TYPE="application/octet-stream",x.prototype.append=function(e,t,a){"string"==typeof(a=a||{})&&(a={filename:a});var i=n.prototype.append.bind(this);if(("number"==typeof t||null==t)&&(t=String(t)),Array.isArray(t))return void this._error(Error("Arrays are not supported."));var o=this._multiPartHeader(e,t,a),r=this._multiPartFooter();i(o),i(t),i(r),this._trackLength(o,t,a)},x.prototype._trackLength=function(e,t,a){var n=0;null!=a.knownLength?n+=Number(a.knownLength):Buffer.isBuffer(t)?n=t.length:"string"==typeof t&&(n=Buffer.byteLength(t)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+x.LINE_BREAK.length,t&&(t.path||t.readable&&f(t,"httpVersion")||t instanceof p)&&(a.knownLength||this._valuesToMeasure.push(t))},x.prototype._lengthRetriever=function(e,t){f(e,"fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?t(null,e.end+1-(e.start?e.start:0)):l.stat(e.path,function(a,n){if(a)return void t(a);t(null,n.size-(e.start?e.start:0))}):f(e,"httpVersion")?t(null,Number(e.headers["content-length"])):f(e,"httpModule")?(e.on("response",function(a){e.pause(),t(null,Number(a.headers["content-length"]))}),e.resume()):t("Unknown stream")},x.prototype._multiPartHeader=function(e,t,a){if("string"==typeof a.header)return a.header;var n,i=this._getContentDisposition(t,a),o=this._getContentType(t,a),r="",s={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof a.header&&h(s,a.header),s)if(f(s,c)){if(null==(n=s[c]))continue;Array.isArray(n)||(n=[n]),n.length&&(r+=c+": "+n.join("; ")+x.LINE_BREAK)}return"--"+this.getBoundary()+x.LINE_BREAK+r+x.LINE_BREAK},x.prototype._getContentDisposition=function(e,t){var a;if("string"==typeof t.filepath?a=o.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e&&(e.name||e.path)?a=o.basename(t.filename||e&&(e.name||e.path)):e&&e.readable&&f(e,"httpVersion")&&(a=o.basename(e.client._httpMessage.path||"")),a)return'filename="'+a+'"'},x.prototype._getContentType=function(e,t){var a=t.contentType;return!a&&e&&e.name&&(a=u.lookup(e.name)),!a&&e&&e.path&&(a=u.lookup(e.path)),!a&&e&&e.readable&&f(e,"httpVersion")&&(a=e.headers["content-type"]),!a&&(t.filepath||t.filename)&&(a=u.lookup(t.filepath||t.filename)),!a&&e&&"object"==typeof e&&(a=x.DEFAULT_CONTENT_TYPE),a},x.prototype._multiPartFooter=function(){return(function(e){var t=x.LINE_BREAK;0===this._streams.length&&(t+=this._lastBoundary()),e(t)}).bind(this)},x.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+x.LINE_BREAK},x.prototype.getHeaders=function(e){var t,a={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)f(e,t)&&(a[t.toLowerCase()]=e[t]);return a},x.prototype.setBoundary=function(e){if("string"!=typeof e)throw TypeError("FormData boundary must be a string");this._boundary=e},x.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},x.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),t=this.getBoundary(),a=0,n=this._streams.length;a<n;a++)"function"!=typeof this._streams[a]&&(e=Buffer.isBuffer(this._streams[a])?Buffer.concat([e,this._streams[a]]):Buffer.concat([e,Buffer.from(this._streams[a])]),("string"!=typeof this._streams[a]||this._streams[a].substring(2,t.length+2)!==t)&&(e=Buffer.concat([e,Buffer.from(x.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},x.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},x.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},x.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},x.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length)return void process.nextTick(e.bind(this,null,t));d.parallel(this._valuesToMeasure,this._lengthRetriever,function(a,n){if(a)return void e(a);n.forEach(function(e){t+=e}),e(null,t)})},x.prototype.submit=function(e,t){var a,n,i={method:"post"};return"string"==typeof e?n=h({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},i):(n=h(e,i)).port||(n.port="https:"===n.protocol?443:80),n.headers=this.getHeaders(e.headers),a="https:"===n.protocol?s.request(n):r.request(n),this.getLength((function(e,n){if(e&&"Unknown stream"!==e)return void this._error(e);if(n&&a.setHeader("Content-Length",n),this.pipe(a),t){var i,o=function(e,n){return a.removeListener("error",o),a.removeListener("response",i),t.call(this,e,n)};i=o.bind(this,null),a.on("error",o),a.on("response",i)}}).bind(this)),a},x.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},x.prototype.toString=function(){return"[object FormData]"},m(x,"FormData"),e.exports=x},36632:(e,t,a)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let a="color: "+this.color;t.splice(1,0,a,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,a)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=a(96211)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},38919:(e,t,a)=>{"use strict";a.r(t),a.d(t,{_:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},39228:(e,t,a)=>{"use strict";let n,i=a(21820),o=a(83997),r=a(19207),{env:s}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function l(e,t){if(0===n)return 0;if(r("color=16m")||r("color=full")||r("color=truecolor"))return 3;if(r("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let a=n||0;if("dumb"===s.TERM)return a;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in s)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in s)||"codeship"===s.CI_NAME?1:a;if("TEAMCITY_VERSION"in s)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(s.TEAMCITY_VERSION);if("truecolor"===s.COLORTERM)return 3;if("TERM_PROGRAM"in s){let e=parseInt((s.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(s.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(s.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(s.TERM)||"COLORTERM"in s?1:a}r("no-color")||r("no-colors")||r("color=false")||r("color=never")?n=0:(r("color")||r("colors")||r("color=true")||r("color=always"))&&(n=1),"FORCE_COLOR"in s&&(n="true"===s.FORCE_COLOR?1:"false"===s.FORCE_COLOR?0:0===s.FORCE_COLOR.length?1:Math.min(parseInt(s.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(l(e,e&&e.isTTY))},stdout:c(l(!0,o.isatty(1))),stderr:c(l(!0,o.isatty(2)))}},39491:(e,t,a)=>{var n=a(79551),i=n.URL,o=a(81630),r=a(55591),s=a(27910).Writable,c=a(12412),l=a(92296);!function(){var e="undefined"!=typeof process,t="undefined"!=typeof window&&"undefined"!=typeof document,a=k(Error.captureStackTrace);e||!t&&a||console.warn("The follow-redirects package should be excluded from browser builds.")}();var p=!1;try{c(new i(""))}catch(e){p="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(t,a,n){this._redirectable.emit(e,t,a,n)}});var f=P("ERR_INVALID_URL","Invalid URL",TypeError),h=P("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),x=P("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",h),v=P("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=P("ERR_STREAM_WRITE_AFTER_END","write after end"),g=s.prototype.destroy||_;function y(e,t){s.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var a=this;this._onNativeResponse=function(e){try{a._processResponse(e)}catch(e){a.emit("error",e instanceof h?e:new h({cause:e}))}},this._performRequest()}function w(e){var t={maxRedirects:21,maxBodyLength:0xa00000},a={};return Object.keys(e).forEach(function(n){var o=n+":",r=a[o]=e[n],s=t[n]=Object.create(r);Object.defineProperties(s,{request:{value:function(e,n,r){var s;return(s=e,i&&s instanceof i)?e=j(e):T(e)?e=j(R(e)):(r=n,n=E(e),e={protocol:o}),k(n)&&(r=n,n=null),(n=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e,n)).nativeProtocols=a,T(n.host)||T(n.hostname)||(n.hostname="::1"),c.equal(n.protocol,o,"protocol mismatch"),l("options",n),new y(n,r)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,t,a){var n=s.request(e,t,a);return n.end(),n},configurable:!0,enumerable:!0,writable:!0}})}),t}function _(){}function R(e){var t;if(p)t=new i(e);else if(!T((t=E(n.parse(e))).protocol))throw new f({input:e});return t}function E(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function j(e,t){var a=t||{};for(var n of u)a[n]=e[n];return a.hostname.startsWith("[")&&(a.hostname=a.hostname.slice(1,-1)),""!==a.port&&(a.port=Number(a.port)),a.path=a.search?a.pathname+a.search:a.pathname,a}function O(e,t){var a;for(var n in t)e.test(n)&&(a=t[n],delete t[n]);return null==a?void 0:String(a).trim()}function P(e,t,a){function n(a){k(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,a||{}),this.code=e,this.message=this.cause?t+": "+this.cause.message:t}return n.prototype=new(a||Error),Object.defineProperties(n.prototype,{constructor:{value:n,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),n}function S(e,t){for(var a of d)e.removeListener(a,m[a]);e.on("error",_),e.destroy(t)}function T(e){return"string"==typeof e||e instanceof String}function k(e){return"function"==typeof e}y.prototype=Object.create(s.prototype),y.prototype.abort=function(){S(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return S(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,t,a){var n;if(this._ending)throw new b;if(!T(e)&&!("object"==typeof(n=e)&&"length"in n))throw TypeError("data should be a string, Buffer or Uint8Array");if(k(t)&&(a=t,t=null),0===e.length){a&&a();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,a)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,t,a){if(k(e)?(a=e,e=t=null):k(t)&&(a=t,t=null),e){var n=this,i=this._currentRequest;this.write(e,t,function(){n._ended=!0,i.end(null,null,a)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,a)},y.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,t){var a=this;function n(t){t.setTimeout(e),t.removeListener("timeout",t.destroy),t.addListener("timeout",t.destroy)}function i(t){a._timeout&&clearTimeout(a._timeout),a._timeout=setTimeout(function(){a.emit("timeout"),o()},e),n(t)}function o(){a._timeout&&(clearTimeout(a._timeout),a._timeout=null),a.removeListener("abort",o),a.removeListener("error",o),a.removeListener("response",o),a.removeListener("close",o),t&&a.removeListener("timeout",t),a.socket||a._currentRequest.removeListener("socket",i)}return t&&this.on("timeout",t),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",n),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(t,a){return this._currentRequest[e](t,a)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var t=e.path.indexOf("?");t<0?e.pathname=e.path:(e.pathname=e.path.substring(0,t),e.search=e.path.substring(t))}},y.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(!t)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var a=e.slice(0,-1);this._options.agent=this._options.agents[a]}var i=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var o of(i._redirectable=this,d))i.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?n.format(this._options):this._options.path,this._isRedirect){var r=0,s=this,c=this._requestBodyBuffers;!function e(t){if(i===s._currentRequest)if(t)s.emit("error",t);else if(r<c.length){var a=c[r++];i.finished||i.write(a.data,a.encoding,e)}else s._ended&&i.end()}()}},y.prototype._processResponse=function(e){var t,a,o,r,s,u,d=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:d});var m=e.headers.location;if(!m||!1===this._options.followRedirects||d<300||d>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(S(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new x;var f=this._options.beforeRedirect;f&&(u=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var h=this._options.method;(301!==d&&302!==d||"POST"!==this._options.method)&&(303!==d||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],O(/^content-/i,this._options.headers));var v=O(/^host$/i,this._options.headers),b=R(this._currentUrl),g=v||b.host,y=/^\w+:/.test(m)?this._currentUrl:n.format(Object.assign(b,{host:g})),w=(t=m,a=y,p?new i(t,a):R(n.resolve(a,t)));if(l("redirecting to",w.href),this._isRedirect=!0,j(w,this._options),(w.protocol===b.protocol||"https:"===w.protocol)&&(w.host===g||(o=w.host,r=g,c(T(o)&&T(r)),(s=o.length-r.length-1)>0&&"."===o[s]&&o.endsWith(r)))||O(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),k(f)){var _={headers:e.headers,statusCode:d},E={url:y,method:h,headers:u};f(this._options,_,E),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:r}),e.exports.wrap=w},41425:e=>{"use strict";e.exports=function(e,t){return Object.keys(t).forEach(function(a){e[a]=e[a]||t[a]}),e}},41480:(e,t)=>{"use strict";function a(e){let{widthInt:t,heightInt:a,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:r}=e,s=n?40*n:t,c=i?40*i:a,l=s&&c?"viewBox='0 0 "+s+" "+c+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+l+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(l?"none":"contain"===r?"xMidYMid":"cover"===r?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return a}})},41500:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,a,o,r,s,c,l){if(0===Object.keys(r[1]).length){a.head=c;return}for(let p in r[1]){let u,d=r[1][p],m=d[0],f=(0,n.createRouterCacheKey)(m),h=null!==s&&void 0!==s[2][p]?s[2][p]:null;if(o){let n=o.parallelRoutes.get(p);if(n){let o,r=(null==l?void 0:l.kind)==="auto"&&l.status===i.PrefetchCacheEntryStatus.reusable,s=new Map(n),u=s.get(f);o=null!==h?{lazyData:null,rsc:h[1],prefetchRsc:null,head:null,prefetchHead:null,loading:h[3],parallelRoutes:new Map(null==u?void 0:u.parallelRoutes),navigatedAt:t}:r&&u?{lazyData:u.lazyData,rsc:u.rsc,prefetchRsc:u.prefetchRsc,head:u.head,prefetchHead:u.prefetchHead,parallelRoutes:new Map(u.parallelRoutes),loading:u.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==u?void 0:u.parallelRoutes),loading:null,navigatedAt:t},s.set(f,o),e(t,o,u,d,h||null,c,l),a.parallelRoutes.set(p,s);continue}}if(null!==h){let e=h[1],a=h[3];u={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:a,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let x=a.parallelRoutes.get(p);x?x.set(f,u):a.parallelRoutes.set(p,new Map([[f,u]])),e(t,u,void 0,d,h,c,l)}}}});let n=a(33123),i=a(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41536:(e,t,a)=>{var n=a(94458),i=a(7932);e.exports=function(e,t,a,o){var r,s,c,l,p,u=a.keyedList?a.keyedList[a.index]:a.index;a.jobs[u]=(r=t,s=u,c=e[u],l=function(e,t){u in a.jobs&&(delete a.jobs[u],e?i(a):a.results[u]=t,o(e,a.results))},2==r.length?r(c,n(l)):r(c,s,n(l)))}},42785:(e,t)=>{"use strict";function a(e){let t={};for(let[a,n]of e.entries()){let e=t[a];void 0===e?t[a]=n:Array.isArray(e)?e.push(n):t[a]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[a,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(a,n(e));else t.set(a,n(i));return t}function o(e){for(var t=arguments.length,a=Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];for(let t of a){for(let a of t.keys())e.delete(a);for(let[a,n]of t.entries())e.append(a,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return i}})},44397:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=a(33123);function i(e,t){return function e(t,a,i){if(0===Object.keys(a).length)return[t,i];if(a.children){let[o,r]=a.children,s=t.parallelRoutes.get("children");if(s){let t=(0,n.createRouterCacheKey)(o),a=s.get(t);if(a){let n=e(a,r,i+"/"+t);if(n)return n}}}for(let o in a){if("children"===o)continue;let[r,s]=a[o],c=t.parallelRoutes.get(o);if(!c)continue;let l=(0,n.createRouterCacheKey)(r),p=c.get(l);if(!p)continue;let u=e(p,s,i+"/"+l);if(u)return u}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return h},PageNotFoundError:function(){return x},SP:function(){return d},ST:function(){return m},WEB_VITALS:function(){return a},execOnce:function(){return n},getDisplayName:function(){return c},getLocationOrigin:function(){return r},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return l},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return p},stringifyError:function(){return g}});let a=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,a=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return a||(a=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function r(){let{protocol:e,hostname:t,port:a}=window.location;return e+"//"+t+(a?":"+a:"")}function s(){let{href:e}=window.location,t=r();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function p(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let a=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(a&&l(a))return n;if(!n)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,m=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class h extends Error{}class x extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function g(e){return JSON.stringify({message:e.message,stack:e.stack})}},44953:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return c}}),a(50148);let n=a(41480),i=a(12756),o=["-moz-initial","fill","none","scale-down",void 0];function r(e){return void 0!==e.default}function s(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function c(e,t){var a,c;let l,p,u,{src:d,sizes:m,unoptimized:f=!1,priority:h=!1,loading:x,className:v,quality:b,width:g,height:y,fill:w=!1,style:_,overrideSrc:R,onLoad:E,onLoadingComplete:j,placeholder:O="empty",blurDataURL:P,fetchPriority:S,decoding:T="async",layout:k,objectFit:A,objectPosition:C,lazyBoundary:M,lazyRoot:N,...U}=e,{imgConf:L,showAltText:F,blurComplete:z,defaultLoader:I}=t,D=L||i.imageConfigDefault;if("allSizes"in D)l=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t),n=null==(a=D.qualities)?void 0:a.sort((e,t)=>e-t);l={...D,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===I)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=U.loader||I;delete U.loader,delete U.srcSet;let q="__next_img_default"in B;if(q){if("custom"===l.loader)throw Object.defineProperty(Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:a,...n}=t;return e(n)}}if(k){"fill"===k&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[k];e&&(_={..._,...e});let t={responsive:"100vw",fill:"100vw"}[k];t&&!m&&(m=t)}let H="",$=s(g),W=s(y);if((c=d)&&"object"==typeof c&&(r(c)||void 0!==c.src)){let e=r(d)?d.default:d;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(p=e.blurWidth,u=e.blurHeight,P=P||e.blurDataURL,H=e.src,!w)if($||W){if($&&!W){let t=$/e.width;W=Math.round(e.height*t)}else if(!$&&W){let t=W/e.height;$=Math.round(e.width*t)}}else $=e.width,W=e.height}let V=!h&&("lazy"===x||void 0===x);(!(d="string"==typeof d?d:H)||d.startsWith("data:")||d.startsWith("blob:"))&&(f=!0,V=!1),l.unoptimized&&(f=!0),q&&!l.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(f=!0);let K=s(b),G=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:C}:{},F?{}:{color:"transparent"},_),X=z||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:$,heightInt:W,blurWidth:p,blurHeight:u,blurDataURL:P||"",objectFit:G.objectFit})+'")':'url("'+O+'")',J=o.includes(G.objectFit)?"fill"===G.objectFit?"100% 100%":"cover":G.objectFit,Y=X?{backgroundSize:J,backgroundPosition:G.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},Q=function(e){let{config:t,src:a,unoptimized:n,width:i,quality:o,sizes:r,loader:s}=e;if(n)return{src:a,srcSet:void 0,sizes:void 0};let{widths:c,kind:l}=function(e,t,a){let{deviceSizes:n,allSizes:i}=e;if(a){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(a);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,r),p=c.length-1;return{sizes:r||"w"!==l?r:"100vw",srcSet:c.map((e,n)=>s({config:t,src:a,quality:o,width:e})+" "+("w"===l?e:n+1)+l).join(", "),src:s({config:t,src:a,quality:o,width:c[p]})}}({config:l,src:d,unoptimized:f,width:$,quality:K,sizes:m,loader:B});return{props:{...U,loading:V?"lazy":x,fetchPriority:S,width:$,height:W,decoding:T,className:v,style:{...G,...Y},sizes:Q.sizes,srcSet:Q.srcSet,src:R||Q.src},meta:{unoptimized:f,priority:h,placeholder:O,fill:w}}}},45793:(e,t,a)=>{var n=a(7932),i=a(94458);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,n(this),i(e)(null,this.results))}},46533:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let n=a(59630),i=a(84441),o=a(60687),r=i._(a(43210)),s=n._(a(51215)),c=n._(a(30512)),l=a(44953),p=a(12756),u=a(17903);a(50148);let d=a(69148),m=n._(a(1933)),f=a(53038),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function x(e,t,a,n,i,o,r){let s=null==e?void 0:e.src;e&&e["data-loaded-src"]!==s&&(e["data-loaded-src"]=s,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==a?void 0:a.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;a.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function v(e){return r.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,r.forwardRef)((e,t)=>{let{src:a,srcSet:n,sizes:i,height:s,width:c,decoding:l,className:p,style:u,fetchPriority:d,placeholder:m,loading:h,unoptimized:b,fill:g,onLoadRef:y,onLoadingCompleteRef:w,setBlurComplete:_,setShowAltText:R,sizesInput:E,onLoad:j,onError:O,...P}=e,S=(0,r.useCallback)(e=>{e&&(O&&(e.src=e.src),e.complete&&x(e,m,y,w,_,b,E))},[a,m,y,w,_,O,b,E]),T=(0,f.useMergedRef)(t,S);return(0,o.jsx)("img",{...P,...v(d),loading:h,width:c,height:s,decoding:l,"data-nimg":g?"fill":"1",className:p,style:u,sizes:i,srcSet:n,src:a,ref:T,onLoad:e=>{x(e.currentTarget,m,y,w,_,b,E)},onError:e=>{R(!0),"empty"!==m&&_(!0),O&&O(e)}})});function g(e){let{isAppRouter:t,imgAttributes:a}=e,n={as:"image",imageSrcSet:a.srcSet,imageSizes:a.sizes,crossOrigin:a.crossOrigin,referrerPolicy:a.referrerPolicy,...v(a.fetchPriority)};return t&&s.default.preload?(s.default.preload(a.src,n),null):(0,o.jsx)(c.default,{children:(0,o.jsx)("link",{rel:"preload",href:a.srcSet?void 0:a.src,...n},"__nimg-"+a.src+a.srcSet+a.sizes)})}let y=(0,r.forwardRef)((e,t)=>{let a=(0,r.useContext)(d.RouterContext),n=(0,r.useContext)(u.ImageConfigContext),i=(0,r.useMemo)(()=>{var e;let t=h||n||p.imageConfigDefault,a=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:a,deviceSizes:i,qualities:o}},[n]),{onLoad:s,onLoadingComplete:c}=e,f=(0,r.useRef)(s);(0,r.useEffect)(()=>{f.current=s},[s]);let x=(0,r.useRef)(c);(0,r.useEffect)(()=>{x.current=c},[c]);let[v,y]=(0,r.useState)(!1),[w,_]=(0,r.useState)(!1),{props:R,meta:E}=(0,l.getImgProps)(e,{defaultLoader:m.default,imgConf:i,blurComplete:v,showAltText:w});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(b,{...R,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:f,onLoadingCompleteRef:x,setBlurComplete:y,setShowAltText:_,sizesInput:e.sizes,ref:t}),E.priority?(0,o.jsx)(g,{isAppRouter:!a,imgAttributes:R}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47530:e=>{"use strict";var t=Object.prototype.toString,a=Math.max,n=function(e,t){for(var a=[],n=0;n<e.length;n+=1)a[n]=e[n];for(var i=0;i<t.length;i+=1)a[i+e.length]=t[i];return a},i=function(e,t){for(var a=[],n=t||0,i=0;n<e.length;n+=1,i+=1)a[i]=e[n];return a},o=function(e,t){for(var a="",n=0;n<e.length;n+=1)a+=e[n],n+1<e.length&&(a+=t);return a};e.exports=function(e){var r,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var c=i(arguments,1),l=a(0,s.length-c.length),p=[],u=0;u<l;u++)p[u]="$"+u;if(r=Function("binder","return function ("+o(p,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof r){var t=s.apply(this,n(c,arguments));return Object(t)===t?t:this}return s.apply(e,n(c,arguments))}),s.prototype){var d=function(){};d.prototype=s.prototype,r.prototype=new d,d.prototype=null}return r}},47755:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}});let n=a(43210),i=()=>{},o=()=>{};function r(e){var t;let{headManager:a,reduceComponentsToState:r}=e;function s(){if(a&&a.mountedInstances){let t=n.Children.toArray(Array.from(a.mountedInstances).filter(Boolean));a.updateHead(r(t,e))}}return null==a||null==(t=a.mountedInstances)||t.add(e.children),s(),i(()=>{var t;return null==a||null==(t=a.mountedInstances)||t.add(e.children),()=>{var t;null==a||null==(t=a.mountedInstances)||t.delete(e.children)}}),i(()=>(a&&(a._pendingUpdate=s),()=>{a&&(a._pendingUpdate=s)})),o(()=>(a&&a._pendingUpdate&&(a._pendingUpdate(),a._pendingUpdate=null),()=>{a&&a._pendingUpdate&&(a._pendingUpdate(),a._pendingUpdate=null)})),null}},48720:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},49088:e=>{"use strict";e.exports=TypeError},49243:(e,t,a)=>{"use strict";var n=a(79551).parse,i={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function r(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}t.getProxyForUrl=function(e){var t,a,s,c="string"==typeof e?n(e):e||{},l=c.protocol,p=c.host,u=c.port;if("string"!=typeof p||!p||"string"!=typeof l)return"";if(l=l.split(":",1)[0],t=p=p.replace(/:\d*$/,""),a=u=parseInt(u)||i[l]||0,!(!(s=(r("npm_config_no_proxy")||r("no_proxy")).toLowerCase())||"*"!==s&&s.split(/[,\s]/).every(function(e){if(!e)return!0;var n=e.match(/^(.+):(\d+)$/),i=n?n[1]:e,r=n?parseInt(n[2]):0;return!!r&&r!==a||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!o.call(t,i)):t!==i)})))return"";var d=r("npm_config_"+l+"_proxy")||r(l+"_proxy")||r("npm_config_proxy")||r("all_proxy");return d&&-1===d.indexOf("://")&&(d=l+"://"+d),d}},50593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{NavigationResultTag:function(){return u},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return c},createCacheKey:function(){return p},getCurrentCacheVersion:function(){return r},navigate:function(){return i},prefetch:function(){return n},reschedulePrefetchTask:function(){return l},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return s}});let a=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=a,i=a,o=a,r=a,s=a,c=a,l=a,p=a;var u=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51060:(e,t,a)=>{"use strict";let n;a.d(t,{A:()=>tV});var i,o,r,s={};function c(e,t){return function(){return e.apply(t,arguments)}}a.r(s),a.d(s,{hasBrowserEnv:()=>ef,hasStandardBrowserEnv:()=>ex,hasStandardBrowserWebWorkerEnv:()=>ev,navigator:()=>eh,origin:()=>eb});let{toString:l}=Object.prototype,{getPrototypeOf:p}=Object,{iterator:u,toStringTag:d}=Symbol,m=(e=>t=>{let a=l.call(t);return e[a]||(e[a]=a.slice(8,-1).toLowerCase())})(Object.create(null)),f=e=>(e=e.toLowerCase(),t=>m(t)===e),h=e=>t=>typeof t===e,{isArray:x}=Array,v=h("undefined"),b=f("ArrayBuffer"),g=h("string"),y=h("function"),w=h("number"),_=e=>null!==e&&"object"==typeof e,R=e=>{if("object"!==m(e))return!1;let t=p(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(d in e)&&!(u in e)},E=f("Date"),j=f("File"),O=f("Blob"),P=f("FileList"),S=f("URLSearchParams"),[T,k,A,C]=["ReadableStream","Request","Response","Headers"].map(f);function M(e,t,{allOwnKeys:a=!1}={}){let n,i;if(null!=e)if("object"!=typeof e&&(e=[e]),x(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let i,o=a?Object.getOwnPropertyNames(e):Object.keys(e),r=o.length;for(n=0;n<r;n++)i=o[n],t.call(null,e[i],i,e)}}function N(e,t){let a;t=t.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(t===(a=n[i]).toLowerCase())return a;return null}let U="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,L=e=>!v(e)&&e!==U,F=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&p(Uint8Array)),z=f("HTMLFormElement"),I=(({hasOwnProperty:e})=>(t,a)=>e.call(t,a))(Object.prototype),D=f("RegExp"),B=(e,t)=>{let a=Object.getOwnPropertyDescriptors(e),n={};M(a,(a,i)=>{let o;!1!==(o=t(a,i,e))&&(n[i]=o||a)}),Object.defineProperties(e,n)},q=f("AsyncFunction"),H=(i="function"==typeof setImmediate,o=y(U.postMessage),i?setImmediate:o?((e,t)=>(U.addEventListener("message",({source:a,data:n})=>{a===U&&n===e&&t.length&&t.shift()()},!1),a=>{t.push(a),U.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),$="undefined"!=typeof queueMicrotask?queueMicrotask.bind(U):"undefined"!=typeof process&&process.nextTick||H,W={isArray:x,isArrayBuffer:b,isBuffer:function(e){return null!==e&&!v(e)&&null!==e.constructor&&!v(e.constructor)&&y(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||y(e.append)&&("formdata"===(t=m(e))||"object"===t&&y(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&b(e.buffer)},isString:g,isNumber:w,isBoolean:e=>!0===e||!1===e,isObject:_,isPlainObject:R,isReadableStream:T,isRequest:k,isResponse:A,isHeaders:C,isUndefined:v,isDate:E,isFile:j,isBlob:O,isRegExp:D,isFunction:y,isStream:e=>_(e)&&y(e.pipe),isURLSearchParams:S,isTypedArray:F,isFileList:P,forEach:M,merge:function e(){let{caseless:t}=L(this)&&this||{},a={},n=(n,i)=>{let o=t&&N(a,i)||i;R(a[o])&&R(n)?a[o]=e(a[o],n):R(n)?a[o]=e({},n):x(n)?a[o]=n.slice():a[o]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&M(arguments[e],n);return a},extend:(e,t,a,{allOwnKeys:n}={})=>(M(t,(t,n)=>{a&&y(t)?e[n]=c(t,a):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,a,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),a&&Object.assign(e.prototype,a)},toFlatObject:(e,t,a,n)=>{let i,o,r,s={};if(t=t||{},null==e)return t;do{for(o=(i=Object.getOwnPropertyNames(e)).length;o-- >0;)r=i[o],(!n||n(r,e,t))&&!s[r]&&(t[r]=e[r],s[r]=!0);e=!1!==a&&p(e)}while(e&&(!a||a(e,t))&&e!==Object.prototype);return t},kindOf:m,kindOfTest:f,endsWith:(e,t,a)=>{e=String(e),(void 0===a||a>e.length)&&(a=e.length),a-=t.length;let n=e.indexOf(t,a);return -1!==n&&n===a},toArray:e=>{if(!e)return null;if(x(e))return e;let t=e.length;if(!w(t))return null;let a=Array(t);for(;t-- >0;)a[t]=e[t];return a},forEachEntry:(e,t)=>{let a,n=(e&&e[u]).call(e);for(;(a=n.next())&&!a.done;){let n=a.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let a,n=[];for(;null!==(a=e.exec(t));)n.push(a);return n},isHTMLForm:z,hasOwnProperty:I,hasOwnProp:I,reduceDescriptors:B,freezeMethods:e=>{B(e,(t,a)=>{if(y(e)&&-1!==["arguments","caller","callee"].indexOf(a))return!1;if(y(e[a])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+a+"'")})}})},toObjectSet:(e,t)=>{let a={};return(x(e)?e:String(e).split(t)).forEach(e=>{a[e]=!0}),a},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,a){return t.toUpperCase()+a}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e*=1)?e:t,findKey:N,global:U,isContextDefined:L,isSpecCompliantForm:function(e){return!!(e&&y(e.append)&&"FormData"===e[d]&&e[u])},toJSONObject:e=>{let t=Array(10),a=(e,n)=>{if(_(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let i=x(e)?[]:{};return M(e,(e,t)=>{let o=a(e,n+1);v(o)||(i[t]=o)}),t[n]=void 0,i}}return e};return a(e,0)},isAsyncFn:q,isThenable:e=>e&&(_(e)||y(e))&&y(e.then)&&y(e.catch),setImmediate:H,asap:$,isIterable:e=>null!=e&&y(e[u])};function V(e,t,a,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),a&&(this.config=a),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}W.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:W.toJSONObject(this.config),code:this.code,status:this.status}}});let K=V.prototype,G={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{G[e]={value:e}}),Object.defineProperties(V,G),Object.defineProperty(K,"isAxiosError",{value:!0}),V.from=(e,t,a,n,i,o)=>{let r=Object.create(K);return W.toFlatObject(e,r,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),V.call(r,e.message,t,a,n,i),r.cause=e,r.name=e.name,o&&Object.assign(r,o),r};var X=a(35836);function J(e){return W.isPlainObject(e)||W.isArray(e)}function Y(e){return W.endsWith(e,"[]")?e.slice(0,-2):e}function Q(e,t,a){return e?e.concat(t).map(function(e,t){return e=Y(e),!a&&t?"["+e+"]":e}).join(a?".":""):t}let Z=W.toFlatObject(W,{},null,function(e){return/^is[A-Z]/.test(e)}),ee=function(e,t,a){if(!W.isObject(e))throw TypeError("target must be an object");t=t||new(X||FormData);let n=(a=W.toFlatObject(a,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!W.isUndefined(t[e])})).metaTokens,i=a.visitor||l,o=a.dots,r=a.indexes,s=(a.Blob||"undefined"!=typeof Blob&&Blob)&&W.isSpecCompliantForm(t);if(!W.isFunction(i))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(W.isDate(e))return e.toISOString();if(W.isBoolean(e))return e.toString();if(!s&&W.isBlob(e))throw new V("Blob is not supported. Use a Buffer instead.");return W.isArrayBuffer(e)||W.isTypedArray(e)?s&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,a,i){let s=e;if(e&&!i&&"object"==typeof e)if(W.endsWith(a,"{}"))a=n?a:a.slice(0,-2),e=JSON.stringify(e);else{var l;if(W.isArray(e)&&(l=e,W.isArray(l)&&!l.some(J))||(W.isFileList(e)||W.endsWith(a,"[]"))&&(s=W.toArray(e)))return a=Y(a),s.forEach(function(e,n){W.isUndefined(e)||null===e||t.append(!0===r?Q([a],n,o):null===r?a:a+"[]",c(e))}),!1}return!!J(e)||(t.append(Q(i,a,o),c(e)),!1)}let p=[],u=Object.assign(Z,{defaultVisitor:l,convertValue:c,isVisitable:J});if(!W.isObject(e))throw TypeError("data must be an object");return!function e(a,n){if(!W.isUndefined(a)){if(-1!==p.indexOf(a))throw Error("Circular reference detected in "+n.join("."));p.push(a),W.forEach(a,function(a,o){!0===(!(W.isUndefined(a)||null===a)&&i.call(t,a,W.isString(o)?o.trim():o,n,u))&&e(a,n?n.concat(o):[o])}),p.pop()}}(e),t};function et(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ea(e,t){this._pairs=[],e&&ee(e,this,t)}let en=ea.prototype;function ei(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eo(e,t,a){let n;if(!t)return e;let i=a&&a.encode||ei;W.isFunction(a)&&(a={serialize:a});let o=a&&a.serialize;if(n=o?o(t,a):W.isURLSearchParams(t)?t.toString():new ea(t,a).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}en.append=function(e,t){this._pairs.push([e,t])},en.toString=function(e){let t=e?function(t){return e.call(this,t,et)}:et;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class er{constructor(){this.handlers=[]}use(e,t,a){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!a&&a.synchronous,runWhen:a?a.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){W.forEach(this.handlers,function(t){null!==t&&e(t)})}}let es={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ec=a(55511);let el=a(79551).URLSearchParams,ep="abcdefghijklmnopqrstuvwxyz",eu="0123456789",ed={DIGIT:eu,ALPHA:ep,ALPHA_DIGIT:ep+ep.toUpperCase()+eu},em={isNode:!0,classes:{URLSearchParams:el,FormData:X,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:ed,generateString:(e=16,t=ed.ALPHA_DIGIT)=>{let a="",{length:n}=t,i=new Uint32Array(e);ec.randomFillSync(i);for(let o=0;o<e;o++)a+=t[i[o]%n];return a},protocols:["http","https","file","data"]},ef="undefined"!=typeof window&&"undefined"!=typeof document,eh="object"==typeof navigator&&navigator||void 0,ex=ef&&(!eh||0>["ReactNative","NativeScript","NS"].indexOf(eh.product)),ev="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eb=ef&&window.location.href||"http://localhost",eg={...s,...em},ey=function(e){if(W.isFormData(e)&&W.isFunction(e.entries)){let t={};return W.forEachEntry(e,(e,a)=>{!function e(t,a,n,i){let o=t[i++];if("__proto__"===o)return!0;let r=Number.isFinite(+o),s=i>=t.length;return(o=!o&&W.isArray(n)?n.length:o,s)?W.hasOwnProp(n,o)?n[o]=[n[o],a]:n[o]=a:(n[o]&&W.isObject(n[o])||(n[o]=[]),e(t,a,n[o],i)&&W.isArray(n[o])&&(n[o]=function(e){let t,a,n={},i=Object.keys(e),o=i.length;for(t=0;t<o;t++)n[a=i[t]]=e[a];return n}(n[o]))),!r}(W.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),a,t,0)}),t}return null},ew={transitional:es,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let a,n=t.getContentType()||"",i=n.indexOf("application/json")>-1,o=W.isObject(e);if(o&&W.isHTMLForm(e)&&(e=new FormData(e)),W.isFormData(e))return i?JSON.stringify(ey(e)):e;if(W.isArrayBuffer(e)||W.isBuffer(e)||W.isStream(e)||W.isFile(e)||W.isBlob(e)||W.isReadableStream(e))return e;if(W.isArrayBufferView(e))return e.buffer;if(W.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1){var r,s;return(r=e,s=this.formSerializer,ee(r,new eg.classes.URLSearchParams,Object.assign({visitor:function(e,t,a,n){return eg.isNode&&W.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},s))).toString()}if((a=W.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return ee(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}if(o||i){t.setContentType("application/json",!1);var c=e;if(W.isString(c))try{return(0,JSON.parse)(c),W.trim(c)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(c)}return e}],transformResponse:[function(e){let t=this.transitional||ew.transitional,a=t&&t.forcedJSONParsing,n="json"===this.responseType;if(W.isResponse(e)||W.isReadableStream(e))return e;if(e&&W.isString(e)&&(a&&!this.responseType||n)){let a=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!a&&n){if("SyntaxError"===e.name)throw V.from(e,V.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eg.classes.FormData,Blob:eg.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};W.forEach(["delete","get","head","post","put","patch"],e=>{ew.headers[e]={}});let e_=W.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eR=e=>{let t,a,n,i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),a=e.substring(n+1).trim(),!t||i[t]&&e_[t]||("set-cookie"===t?i[t]?i[t].push(a):i[t]=[a]:i[t]=i[t]?i[t]+", "+a:a)}),i},eE=Symbol("internals");function ej(e){return e&&String(e).trim().toLowerCase()}function eO(e){return!1===e||null==e?e:W.isArray(e)?e.map(eO):String(e)}let eP=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eS(e,t,a,n,i){if(W.isFunction(n))return n.call(this,t,a);if(i&&(t=a),W.isString(t)){if(W.isString(n))return -1!==t.indexOf(n);if(W.isRegExp(n))return n.test(t)}}class eT{constructor(e){e&&this.set(e)}set(e,t,a){let n=this;function i(e,t,a){let i=ej(t);if(!i)throw Error("header name must be a non-empty string");let o=W.findKey(n,i);o&&void 0!==n[o]&&!0!==a&&(void 0!==a||!1===n[o])||(n[o||t]=eO(e))}let o=(e,t)=>W.forEach(e,(e,a)=>i(e,a,t));if(W.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(W.isString(e)&&(e=e.trim())&&!eP(e))o(eR(e),t);else if(W.isObject(e)&&W.isIterable(e)){let a={},n,i;for(let t of e){if(!W.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[i=t[0]]=(n=a[i])?W.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&i(t,e,a);return this}get(e,t){if(e=ej(e)){let a=W.findKey(this,e);if(a){let e=this[a];if(!t)return e;if(!0===t){let t,a=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)a[t[1]]=t[2];return a}if(W.isFunction(t))return t.call(this,e,a);if(W.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ej(e)){let a=W.findKey(this,e);return!!(a&&void 0!==this[a]&&(!t||eS(this,this[a],a,t)))}return!1}delete(e,t){let a=this,n=!1;function i(e){if(e=ej(e)){let i=W.findKey(a,e);i&&(!t||eS(a,a[i],i,t))&&(delete a[i],n=!0)}}return W.isArray(e)?e.forEach(i):i(e),n}clear(e){let t=Object.keys(this),a=t.length,n=!1;for(;a--;){let i=t[a];(!e||eS(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let t=this,a={};return W.forEach(this,(n,i)=>{let o=W.findKey(a,i);if(o){t[o]=eO(n),delete t[i];return}let r=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,a)=>t.toUpperCase()+a):String(i).trim();r!==i&&delete t[i],t[r]=eO(n),a[r]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return W.forEach(this,(a,n)=>{null!=a&&!1!==a&&(t[n]=e&&W.isArray(a)?a.join(", "):a)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let a=new this(e);return t.forEach(e=>a.set(e)),a}static accessor(e){let t=(this[eE]=this[eE]={accessors:{}}).accessors,a=this.prototype;function n(e){let n=ej(e);if(!t[n]){let i=W.toCamelCase(" "+e);["get","set","has"].forEach(t=>{Object.defineProperty(a,t+i,{value:function(a,n,i){return this[t].call(this,e,a,n,i)},configurable:!0})}),t[n]=!0}}return W.isArray(e)?e.forEach(n):n(e),this}}function ek(e,t){let a=this||ew,n=t||a,i=eT.from(n.headers),o=n.data;return W.forEach(e,function(e){o=e.call(a,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function eA(e){return!!(e&&e.__CANCEL__)}function eC(e,t,a){V.call(this,null==e?"canceled":e,V.ERR_CANCELED,t,a),this.name="CanceledError"}function eM(e,t,a){let n=a.config.validateStatus;!a.status||!n||n(a.status)?e(a):t(new V("Request failed with status code "+a.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(a.status/100)-4],a.config,a.request,a))}function eN(e,t,a){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(n||!1==a)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}eT.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),W.reduceDescriptors(eT.prototype,({value:e},t)=>{let a=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[a]=e}}}),W.freezeMethods(eT),W.inherits(eC,V,{__CANCEL__:!0});var eU=a(49243),eL=a(81630),eF=a(55591),ez=a(28354),eI=a(39491),eD=a(74075);let eB="1.10.0";function eq(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}let eH=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var e$=a(27910);let eW=Symbol("internals");class eV extends e$.Transform{constructor(e){super({readableHighWaterMark:(e=W.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,t)=>!W.isUndefined(t[e]))).chunkSize});let t=this[eW]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||t.isCaptured||(t.isCaptured=!0)})}_read(e){let t=this[eW];return t.onReadCallback&&t.onReadCallback(),super._read(e)}_transform(e,t,a){let n=this[eW],i=n.maxRate,o=this.readableHighWaterMark,r=n.timeWindow,s=i/(1e3/r),c=!1!==n.minChunkSize?Math.max(n.minChunkSize,.01*s):0,l=(e,t)=>{let a=Buffer.byteLength(e);n.bytesSeen+=a,n.bytes+=a,n.isCaptured&&this.emit("progress",n.bytesSeen),this.push(e)?process.nextTick(t):n.onReadCallback=()=>{n.onReadCallback=null,process.nextTick(t)}},p=(e,t)=>{let a,p=Buffer.byteLength(e),u=null,d=o,m=0;if(i){let e=Date.now();(!n.ts||(m=e-n.ts)>=r)&&(n.ts=e,a=s-n.bytes,n.bytes=a<0?-a:0,m=0),a=s-n.bytes}if(i){if(a<=0)return setTimeout(()=>{t(null,e)},r-m);a<d&&(d=a)}d&&p>d&&p-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),l(e,u?()=>{process.nextTick(t,null,u)}:t)};p(e,function e(t,n){if(t)return a(t);n?p(n,e):a(null)})}}var eK=a(94735);let{asyncIterator:eG}=Symbol,eX=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[eG]?yield*e[eG]():yield e},eJ=eg.ALPHABET.ALPHA_DIGIT+"-_",eY="function"==typeof TextEncoder?new TextEncoder:new ez.TextEncoder,eQ=eY.encode("\r\n");class eZ{constructor(e,t){let{escapeName:a}=this.constructor,n=W.isString(t),i=`Content-Disposition: form-data; name="${a(e)}"${!n&&t.name?`; filename="${a(t.name)}"`:""}\r
`;n?t=eY.encode(String(t).replace(/\r?\n|\r\n?/g,"\r\n")):i+=`Content-Type: ${t.type||"application/octet-stream"}\r
`,this.headers=eY.encode(i+"\r\n"),this.contentLength=n?t.byteLength:t.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=t}async *encode(){yield this.headers;let{value:e}=this;W.isTypedArray(e)?yield e:yield*eX(e),yield eQ}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e0=(e,t,a)=>{let{tag:n="form-data-boundary",size:i=25,boundary:o=n+"-"+eg.generateString(i,eJ)}=a||{};if(!W.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let r=eY.encode("--"+o+"\r\n"),s=eY.encode("--"+o+"--\r\n"),c=s.byteLength,l=Array.from(e.entries()).map(([e,t])=>{let a=new eZ(e,t);return c+=a.size,a});c+=r.byteLength*l.length;let p={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c=W.toFiniteNumber(c))&&(p["Content-Length"]=c),t&&t(p),e$.Readable.from(async function*(){for(let e of l)yield r,yield*e.encode();yield s}())};class e1 extends e$.Transform{__transform(e,t,a){this.push(e),a()}_transform(e,t,a){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,t)}this.__transform(e,t,a)}}let e2=(e,t)=>W.isAsyncFn(e)?function(...a){let n=a.pop();e.apply(this,a).then(e=>{try{t?n(null,...t(e)):n(null,e)}catch(e){n(e)}},n)}:e,e3=function(e,t){let a,n=Array(e=e||10),i=Array(e),o=0,r=0;return t=void 0!==t?t:1e3,function(s){let c=Date.now(),l=i[r];a||(a=c),n[o]=s,i[o]=c;let p=r,u=0;for(;p!==o;)u+=n[p++],p%=e;if((o=(o+1)%e)===r&&(r=(r+1)%e),c-a<t)return;let d=l&&c-l;return d?Math.round(1e3*u/d):void 0}},e4=function(e,t){let a,n,i=0,o=1e3/t,r=(t,o=Date.now())=>{i=o,a=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),s=t-i;s>=o?r(e,t):(a=e,n||(n=setTimeout(()=>{n=null,r(a)},o-s)))},()=>a&&r(a)]},e6=(e,t,a=3)=>{let n=0,i=e3(50,250);return e4(a=>{let o=a.loaded,r=a.lengthComputable?a.total:void 0,s=o-n,c=i(s);n=o,e({loaded:o,total:r,progress:r?o/r:void 0,bytes:s,rate:c||void 0,estimated:c&&r&&o<=r?(r-o)/c:void 0,event:a,lengthComputable:null!=r,[t?"download":"upload"]:!0})},a)},e5=(e,t)=>{let a=null!=e;return[n=>t[0]({lengthComputable:a,total:e,loaded:n}),t[1]]},e9=e=>(...t)=>W.asap(()=>e(...t)),e8={flush:eD.constants.Z_SYNC_FLUSH,finishFlush:eD.constants.Z_SYNC_FLUSH},e7={flush:eD.constants.BROTLI_OPERATION_FLUSH,finishFlush:eD.constants.BROTLI_OPERATION_FLUSH},te=W.isFunction(eD.createBrotliDecompress),{http:tt,https:ta}=eI,tn=/https:?/,ti=eg.protocols.map(e=>e+":"),to=(e,[t,a])=>(e.on("end",a).on("error",a),t);function tr(e,t){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,t)}let ts="undefined"!=typeof process&&"process"===W.kindOf(process),tc=e=>new Promise((t,a)=>{let n,i,o=(e,t)=>{!i&&(i=!0,n&&n(e,t))},r=e=>{o(e,!0),a(e)};e(e=>{o(e),t(e)},r,e=>n=e).catch(r)}),tl=({address:e,family:t})=>{if(!W.isString(e))throw TypeError("address must be a string");return{address:e,family:t||(0>e.indexOf(".")?6:4)}},tp=(e,t)=>tl(W.isObject(e)?e:{address:e,family:t}),tu=ts&&function(e){return tc(async function(t,a,n){let i,o,r,s,c,l,p,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:h}=e,x=e.method.toUpperCase(),v=!1;if(d){let e=e2(d,e=>W.isArray(e)?e:[e]);d=(t,a,n)=>{e(t,a,(e,t,i)=>{if(e)return n(e);let o=W.isArray(t)?t.map(e=>tp(e)):[tp(t,i)];a.all?n(e,o):n(e,o[0].address,o[0].family)})}}let b=new eK.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(t){b.emit("abort",!t||t.type?new eC(null,e,c):t)}n((e,t)=>{s=!0,t&&(v=!0,g())}),b.once("abort",a),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eN(e.baseURL,e.url,e.allowAbsoluteUrls),eg.hasBrowserEnv?eg.origin:void 0),_=w.protocol||ti[0];if("data:"===_){let n;if("GET"!==x)return eM(t,a,{status:405,statusText:"method not allowed",headers:{},config:e});try{n=function(e,t,a){let n=a&&a.Blob||eg.classes.Blob,i=eq(e);if(void 0===t&&n&&(t=!0),"data"===i){e=i.length?e.slice(i.length+1):e;let a=eH.exec(e);if(!a)throw new V("Invalid URL",V.ERR_INVALID_URL);let o=a[1],r=a[2],s=a[3],c=Buffer.from(decodeURIComponent(s),r?"base64":"utf8");if(t){if(!n)throw new V("Blob is not supported",V.ERR_NOT_SUPPORT);return new n([c],{type:o})}return c}throw new V("Unsupported protocol "+i,V.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(t){throw V.from(t,V.ERR_BAD_REQUEST,e)}return"text"===f?(n=n.toString(h),h&&"utf8"!==h||(n=W.stripBOM(n))):"stream"===f&&(n=e$.Readable.from(n)),eM(t,a,{data:n,status:200,statusText:"OK",headers:new eT,config:e})}if(-1===ti.indexOf(_))return a(new V("Unsupported protocol "+_,V.ERR_BAD_REQUEST,e));let R=eT.from(e.headers).normalize();R.set("User-Agent","axios/"+eB,!1);let{onUploadProgress:E,onDownloadProgress:j}=e,O=e.maxRate;if(W.isSpecCompliantForm(u)){let e=R.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e0(u,e=>{R.set(e)},{tag:`axios-${eB}-boundary`,boundary:e&&e[1]||void 0})}else if(W.isFormData(u)&&W.isFunction(u.getHeaders)){if(R.set(u.getHeaders()),!R.hasContentLength())try{let e=await ez.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&R.setContentLength(e)}catch(e){}}else if(W.isBlob(u)||W.isFile(u))u.size&&R.setContentType(u.type||"application/octet-stream"),R.setContentLength(u.size||0),u=e$.Readable.from(eX(u));else if(u&&!W.isStream(u)){if(Buffer.isBuffer(u));else if(W.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!W.isString(u))return a(new V("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",V.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(R.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return a(new V("Request body larger than maxBodyLength limit",V.ERR_BAD_REQUEST,e))}let P=W.toFiniteNumber(R.getContentLength());W.isArray(O)?(i=O[0],o=O[1]):i=o=O,u&&(E||i)&&(W.isStream(u)||(u=e$.Readable.from(u,{objectMode:!1})),u=e$.pipeline([u,new eV({maxRate:W.toFiniteNumber(i)})],W.noop),E&&u.on("progress",to(u,e5(P,e6(e9(E),!1,3))))),e.auth&&(r=(e.auth.username||"")+":"+(e.auth.password||"")),!r&&w.username&&(r=w.username+":"+w.password),r&&R.delete("authorization");try{l=eo(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(n){let t=Error(n.message);return t.config=e,t.url=e.url,t.exists=!0,a(t)}R.set("Accept-Encoding","gzip, compress, deflate"+(te?", br":""),!1);let S={path:l,method:x,headers:R.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:r,protocol:_,family:m,beforeRedirect:tr,beforeRedirects:{}};W.isUndefined(d)||(S.lookup=d),e.socketPath?S.socketPath=e.socketPath:(S.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,S.port=w.port,function e(t,a,n){let i=a;if(!i&&!1!==i){let e=eU.getProxyForUrl(n);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));let e=Buffer.from(i.auth,"utf8").toString("base64");t.headers["Proxy-Authorization"]="Basic "+e}t.headers.host=t.hostname+(t.port?":"+t.port:"");let e=i.hostname||i.host;t.hostname=e,t.host=e,t.port=i.port,t.path=n,i.protocol&&(t.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}t.beforeRedirects.proxy=function(t){e(t,a,t.href)}}(S,e.proxy,_+"//"+w.hostname+(w.port?":"+w.port:"")+S.path));let T=tn.test(S.protocol);if(S.agent=T?e.httpsAgent:e.httpAgent,e.transport?p=e.transport:0===e.maxRedirects?p=T?eF:eL:(e.maxRedirects&&(S.maxRedirects=e.maxRedirects),e.beforeRedirect&&(S.beforeRedirects.config=e.beforeRedirect),p=T?ta:tt),e.maxBodyLength>-1?S.maxBodyLength=e.maxBodyLength:S.maxBodyLength=1/0,e.insecureHTTPParser&&(S.insecureHTTPParser=e.insecureHTTPParser),c=p.request(S,function(n){if(c.destroyed)return;let i=[n],r=+n.headers["content-length"];if(j||o){let e=new eV({maxRate:W.toFiniteNumber(o)});j&&e.on("progress",to(e,e5(r,e6(e9(j),!0,3)))),i.push(e)}let s=n,l=n.req||c;if(!1!==e.decompress&&n.headers["content-encoding"])switch(("HEAD"===x||204===n.statusCode)&&delete n.headers["content-encoding"],(n.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":i.push(eD.createUnzip(e8)),delete n.headers["content-encoding"];break;case"deflate":i.push(new e1),i.push(eD.createUnzip(e8)),delete n.headers["content-encoding"];break;case"br":te&&(i.push(eD.createBrotliDecompress(e7)),delete n.headers["content-encoding"])}s=i.length>1?e$.pipeline(i,W.noop):i[0];let p=e$.finished(s,()=>{p(),g()}),u={status:n.statusCode,statusText:n.statusMessage,headers:new eT(n.headers),config:e,request:l};if("stream"===f)u.data=s,eM(t,a,u);else{let n=[],i=0;s.on("data",function(t){n.push(t),i+=t.length,e.maxContentLength>-1&&i>e.maxContentLength&&(v=!0,s.destroy(),a(new V("maxContentLength size of "+e.maxContentLength+" exceeded",V.ERR_BAD_RESPONSE,e,l)))}),s.on("aborted",function(){if(v)return;let t=new V("stream has been aborted",V.ERR_BAD_RESPONSE,e,l);s.destroy(t),a(t)}),s.on("error",function(t){c.destroyed||a(V.from(t,null,e,l))}),s.on("end",function(){try{let e=1===n.length?n[0]:Buffer.concat(n);"arraybuffer"!==f&&(e=e.toString(h),h&&"utf8"!==h||(e=W.stripBOM(e))),u.data=e}catch(t){return a(V.from(t,null,e,u.request,u))}eM(t,a,u)})}b.once("abort",e=>{s.destroyed||(s.emit("error",e),s.destroy())})}),b.once("abort",e=>{a(e),c.destroy(e)}),c.on("error",function(t){a(V.from(t,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let t=parseInt(e.timeout,10);if(Number.isNaN(t))return void a(new V("error trying to parse `config.timeout` to int",V.ERR_BAD_OPTION_VALUE,e,c));c.setTimeout(t,function(){if(s)return;let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||es;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),a(new V(t,n.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,e,c)),y()})}if(W.isStream(u)){let t=!1,a=!1;u.on("end",()=>{t=!0}),u.once("error",e=>{a=!0,c.destroy(e)}),u.on("close",()=>{t||a||y(new eC("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},td=eg.hasStandardBrowserEnv?((e,t)=>a=>(a=new URL(a,eg.origin),e.protocol===a.protocol&&e.host===a.host&&(t||e.port===a.port)))(new URL(eg.origin),eg.navigator&&/(msie|trident)/i.test(eg.navigator.userAgent)):()=>!0,tm=eg.hasStandardBrowserEnv?{write(e,t,a,n,i,o){let r=[e+"="+encodeURIComponent(t)];W.isNumber(a)&&r.push("expires="+new Date(a).toGMTString()),W.isString(n)&&r.push("path="+n),W.isString(i)&&r.push("domain="+i),!0===o&&r.push("secure"),document.cookie=r.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},tf=e=>e instanceof eT?{...e}:e;function th(e,t){t=t||{};let a={};function n(e,t,a,n){return W.isPlainObject(e)&&W.isPlainObject(t)?W.merge.call({caseless:n},e,t):W.isPlainObject(t)?W.merge({},t):W.isArray(t)?t.slice():t}function i(e,t,a,i){return W.isUndefined(t)?W.isUndefined(e)?void 0:n(void 0,e,a,i):n(e,t,a,i)}function o(e,t){if(!W.isUndefined(t))return n(void 0,t)}function r(e,t){return W.isUndefined(t)?W.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function s(a,i,o){return o in t?n(a,i):o in e?n(void 0,a):void 0}let c={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:s,headers:(e,t,a)=>i(tf(e),tf(t),a,!0)};return W.forEach(Object.keys(Object.assign({},e,t)),function(n){let o=c[n]||i,r=o(e[n],t[n],n);W.isUndefined(r)&&o!==s||(a[n]=r)}),a}let tx=e=>{let t,a=th({},e),{data:n,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:r,headers:s,auth:c}=a;if(a.headers=s=eT.from(s),a.url=eo(eN(a.baseURL,a.url,a.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&s.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),W.isFormData(n)){if(eg.hasStandardBrowserEnv||eg.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(t=s.getContentType())){let[e,...a]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...a].join("; "))}}if(eg.hasStandardBrowserEnv&&(i&&W.isFunction(i)&&(i=i(a)),i||!1!==i&&td(a.url))){let e=o&&r&&tm.read(r);e&&s.set(o,e)}return a},tv="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,a){let n,i,o,r,s,c=tx(e),l=c.data,p=eT.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){r&&r(),s&&s(),c.cancelToken&&c.cancelToken.unsubscribe(n),c.signal&&c.signal.removeEventListener("abort",n)}let h=new XMLHttpRequest;function x(){if(!h)return;let n=eT.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());eM(function(e){t(e),f()},function(e){a(e),f()},{data:u&&"text"!==u&&"json"!==u?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:n,config:e,request:h}),h=null}h.open(c.method.toUpperCase(),c.url,!0),h.timeout=c.timeout,"onloadend"in h?h.onloadend=x:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(x)},h.onabort=function(){h&&(a(new V("Request aborted",V.ECONNABORTED,e,h)),h=null)},h.onerror=function(){a(new V("Network Error",V.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",n=c.transitional||es;c.timeoutErrorMessage&&(t=c.timeoutErrorMessage),a(new V(t,n.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,e,h)),h=null},void 0===l&&p.setContentType(null),"setRequestHeader"in h&&W.forEach(p.toJSON(),function(e,t){h.setRequestHeader(t,e)}),W.isUndefined(c.withCredentials)||(h.withCredentials=!!c.withCredentials),u&&"json"!==u&&(h.responseType=c.responseType),m&&([o,s]=e6(m,!0),h.addEventListener("progress",o)),d&&h.upload&&([i,r]=e6(d),h.upload.addEventListener("progress",i),h.upload.addEventListener("loadend",r)),(c.cancelToken||c.signal)&&(n=t=>{h&&(a(!t||t.type?new eC(null,e,h):t),h.abort(),h=null)},c.cancelToken&&c.cancelToken.subscribe(n),c.signal&&(c.signal.aborted?n():c.signal.addEventListener("abort",n)));let v=eq(c.url);if(v&&-1===eg.protocols.indexOf(v))return void a(new V("Unsupported protocol "+v+":",V.ERR_BAD_REQUEST,e));h.send(l||null)})},tb=(e,t)=>{let{length:a}=e=e?e.filter(Boolean):[];if(t||a){let a,n=new AbortController,i=function(e){if(!a){a=!0,r();let t=e instanceof Error?e:this.reason;n.abort(t instanceof V?t:new eC(t instanceof Error?t.message:t))}},o=t&&setTimeout(()=>{o=null,i(new V(`timeout ${t} of ms exceeded`,V.ETIMEDOUT))},t),r=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:s}=n;return s.unsubscribe=()=>W.asap(r),s}},tg=function*(e,t){let a,n=e.byteLength;if(!t||n<t)return void(yield e);let i=0;for(;i<n;)a=i+t,yield e.slice(i,a),i=a},ty=async function*(e,t){for await(let a of tw(e))yield*tg(a,t)},tw=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);let t=e.getReader();try{for(;;){let{done:e,value:a}=await t.read();if(e)break;yield a}}finally{await t.cancel()}},t_=(e,t,a,n)=>{let i,o=ty(e,t),r=0,s=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:t,value:n}=await o.next();if(t){s(),e.close();return}let i=n.byteLength;if(a){let e=r+=i;a(e)}e.enqueue(new Uint8Array(n))}catch(e){throw s(e),e}},cancel:e=>(s(e),o.return())},{highWaterMark:2})},tR="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tE=tR&&"function"==typeof ReadableStream,tj=tR&&("function"==typeof TextEncoder?(n=new TextEncoder,e=>n.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tO=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},tP=tE&&tO(()=>{let e=!1,t=new Request(eg.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),tS=tE&&tO(()=>W.isReadableStream(new Response("").body)),tT={stream:tS&&(e=>e.body)};tR&&(r=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{tT[e]||(tT[e]=W.isFunction(r[e])?t=>t[e]():(t,a)=>{throw new V(`Response type '${e}' is not supported`,V.ERR_NOT_SUPPORT,a)})}));let tk=async e=>{if(null==e)return 0;if(W.isBlob(e))return e.size;if(W.isSpecCompliantForm(e)){let t=new Request(eg.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return W.isArrayBufferView(e)||W.isArrayBuffer(e)?e.byteLength:(W.isURLSearchParams(e)&&(e+=""),W.isString(e))?(await tj(e)).byteLength:void 0},tA=async(e,t)=>{let a=W.toFiniteNumber(e.getContentLength());return null==a?tk(t):a},tC={http:tu,xhr:tv,fetch:tR&&(async e=>{let t,a,{url:n,method:i,data:o,signal:r,cancelToken:s,timeout:c,onDownloadProgress:l,onUploadProgress:p,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=tx(e);u=u?(u+"").toLowerCase():"text";let h=tb([r,s&&s.toAbortSignal()],c),x=h&&h.unsubscribe&&(()=>{h.unsubscribe()});try{if(p&&tP&&"get"!==i&&"head"!==i&&0!==(a=await tA(d,o))){let e,t=new Request(n,{method:"POST",body:o,duplex:"half"});if(W.isFormData(o)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,n]=e5(a,e6(e9(p)));o=t_(t.body,65536,e,n)}}W.isString(m)||(m=m?"include":"omit");let r="credentials"in Request.prototype;t=new Request(n,{...f,signal:h,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:r?m:void 0});let s=await fetch(t,f),c=tS&&("stream"===u||"response"===u);if(tS&&(l||c&&x)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});let t=W.toFiniteNumber(s.headers.get("content-length")),[a,n]=l&&e5(t,e6(e9(l),!0))||[];s=new Response(t_(s.body,65536,a,()=>{n&&n(),x&&x()}),e)}u=u||"text";let v=await tT[W.findKey(tT,u)||"text"](s,e);return!c&&x&&x(),await new Promise((a,n)=>{eM(a,n,{data:v,headers:eT.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:t})})}catch(a){if(x&&x(),a&&"TypeError"===a.name&&/Load failed|fetch/i.test(a.message))throw Object.assign(new V("Network Error",V.ERR_NETWORK,e,t),{cause:a.cause||a});throw V.from(a,a&&a.code,e,t)}})};W.forEach(tC,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let tM=e=>`- ${e}`,tN=e=>W.isFunction(e)||null===e||!1===e,tU={getAdapter:e=>{let t,a,{length:n}=e=W.isArray(e)?e:[e],i={};for(let o=0;o<n;o++){let n;if(a=t=e[o],!tN(t)&&void 0===(a=tC[(n=String(t)).toLowerCase()]))throw new V(`Unknown adapter '${n}'`);if(a)break;i[n||"#"+o]=a}if(!a){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new V("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(tM).join("\n"):" "+tM(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return a}};function tL(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eC(null,e)}function tF(e){return tL(e),e.headers=eT.from(e.headers),e.data=ek.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),tU.getAdapter(e.adapter||ew.adapter)(e).then(function(t){return tL(e),t.data=ek.call(e,e.transformResponse,t),t.headers=eT.from(t.headers),t},function(t){return!eA(t)&&(tL(e),t&&t.response&&(t.response.data=ek.call(e,e.transformResponse,t.response),t.response.headers=eT.from(t.response.headers))),Promise.reject(t)})}let tz={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tz[e]=function(a){return typeof a===e||"a"+(t<1?"n ":" ")+e}});let tI={};tz.transitional=function(e,t,a){function n(e,t){return"[Axios v"+eB+"] Transitional option '"+e+"'"+t+(a?". "+a:"")}return(a,i,o)=>{if(!1===e)throw new V(n(i," has been removed"+(t?" in "+t:"")),V.ERR_DEPRECATED);return t&&!tI[i]&&(tI[i]=!0,console.warn(n(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(a,i,o)}},tz.spelling=function(e){return(t,a)=>(console.warn(`${a} is likely a misspelling of ${e}`),!0)};let tD={assertOptions:function(e,t,a){if("object"!=typeof e)throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let o=n[i],r=t[o];if(r){let t=e[o],a=void 0===t||r(t,o,e);if(!0!==a)throw new V("option "+o+" must be "+a,V.ERR_BAD_OPTION_VALUE);continue}if(!0!==a)throw new V("Unknown option "+o,V.ERR_BAD_OPTION)}},validators:tz},tB=tD.validators;class tq{constructor(e){this.defaults=e||{},this.interceptors={request:new er,response:new er}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let a=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?a&&!String(e.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+a):e.stack=a}catch(e){}}throw e}}_request(e,t){let a,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:o,headers:r}=t=th(this.defaults,t);void 0!==i&&tD.assertOptions(i,{silentJSONParsing:tB.transitional(tB.boolean),forcedJSONParsing:tB.transitional(tB.boolean),clarifyTimeoutError:tB.transitional(tB.boolean)},!1),null!=o&&(W.isFunction(o)?t.paramsSerializer={serialize:o}:tD.assertOptions(o,{encode:tB.function,serialize:tB.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tD.assertOptions(t,{baseUrl:tB.spelling("baseURL"),withXsrfToken:tB.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=r&&W.merge(r.common,r[t.method]);r&&W.forEach(["delete","get","head","post","put","patch","common"],e=>{delete r[e]}),t.headers=eT.concat(s,r);let c=[],l=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(l=l&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let p=[];this.interceptors.response.forEach(function(e){p.push(e.fulfilled,e.rejected)});let u=0;if(!l){let e=[tF.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,p),n=e.length,a=Promise.resolve(t);u<n;)a=a.then(e[u++],e[u++]);return a}n=c.length;let d=t;for(u=0;u<n;){let e=c[u++],t=c[u++];try{d=e(d)}catch(e){t.call(this,e);break}}try{a=tF.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,n=p.length;u<n;)a=a.then(p[u++],p[u++]);return a}getUri(e){return eo(eN((e=th(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}W.forEach(["delete","get","head","options"],function(e){tq.prototype[e]=function(t,a){return this.request(th(a||{},{method:e,url:t,data:(a||{}).data}))}}),W.forEach(["post","put","patch"],function(e){function t(t){return function(a,n,i){return this.request(th(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:a,data:n}))}}tq.prototype[e]=t(),tq.prototype[e+"Form"]=t(!0)});class tH{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let a=this;this.promise.then(e=>{if(!a._listeners)return;let t=a._listeners.length;for(;t-- >0;)a._listeners[t](e);a._listeners=null}),this.promise.then=e=>{let t,n=new Promise(e=>{a.subscribe(e),t=e}).then(e);return n.cancel=function(){a.unsubscribe(t)},n},e(function(e,n,i){a.reason||(a.reason=new eC(e,n,i),t(a.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason)return void e(this.reason);this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new tH(function(t){e=t}),cancel:e}}}let t$={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(t$).forEach(([e,t])=>{t$[t]=e});let tW=function e(t){let a=new tq(t),n=c(tq.prototype.request,a);return W.extend(n,tq.prototype,a,{allOwnKeys:!0}),W.extend(n,a,null,{allOwnKeys:!0}),n.create=function(a){return e(th(t,a))},n}(ew);tW.Axios=tq,tW.CanceledError=eC,tW.CancelToken=tH,tW.isCancel=eA,tW.VERSION=eB,tW.toFormData=ee,tW.AxiosError=V,tW.Cancel=tW.CanceledError,tW.all=function(e){return Promise.all(e)},tW.spread=function(e){return function(t){return e.apply(null,t)}},tW.isAxiosError=function(e){return W.isObject(e)&&!0===e.isAxiosError},tW.mergeConfig=th,tW.AxiosHeaders=eT,tW.formToJSON=e=>ey(W.isHTMLForm(e)?new FormData(e):e),tW.getAdapter=tU.getAdapter,tW.HttpStatusCode=t$,tW.default=tW;let tV=tW},51105:(e,t,a)=>{"use strict";var n=a(92482),i=a(51951),o=a(99819);e.exports=a(78360)||n.call(o,i)},51499:(e,t,a)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}a.r(t),a.d(t,{_:()=>n})},51951:e=>{"use strict";e.exports=Function.prototype.apply},53038:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=a(43210);function i(e,t){let a=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=a.current;e&&(a.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(a.current=o(e,n)),t&&(i.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let a=e(t);return"function"==typeof a?a:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53147:e=>{"use strict";e.exports=Math.min},53293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let a=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return a.test(e)?e.replace(n,"\\$&"):e}},54544:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),a=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(a))return!1;for(var n in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var i=Object.getOwnPropertySymbols(e);if(1!==i.length||i[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},54674:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=a(84949),i=a(19169),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:a,hash:o}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+a+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56786:(e,t,a)=>{"use strict";var n=Function.prototype.call,i=Object.prototype.hasOwnProperty;e.exports=a(92482).call(n,i)},56928:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=a(41500),i=a(33898);function o(e,t,a,o,r){let{tree:s,seedData:c,head:l,isRootRender:p}=o;if(null===c)return!1;if(p){let i=c[1];a.loading=c[3],a.rsc=i,a.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,a,t,s,c,l,r)}else a.rsc=t.rsc,a.prefetchRsc=t.prefetchRsc,a.parallelRoutes=new Map(t.parallelRoutes),a.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,a,t,o,r);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58501:e=>{"use strict";e.exports=Math.pow},59435:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=a(70642);function i(e){return void 0!==e}function o(e,t){var a,o;let r=null==(a=t.shouldScroll)||a,s=e.nextUrl;if(i(t.patchedTree)){let a=(0,n.computeChangedPath)(e.tree,t.patchedTree);a?s=a:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!r&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:r?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:r?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},60863:e=>{"use strict";e.exports=Math.abs},61794:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=a(79289),i=a(26736);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),a=new URL(e,t);return a.origin===t&&(0,i.hasBasePath)(a.pathname)}catch(e){return!1}}},62427:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},62688:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var n=a(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),r=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let p=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:o="",children:r,iconNode:p,...u},d)=>(0,n.createElement)("svg",{ref:d,...l,width:t,height:t,stroke:e,strokeWidth:i?24*Number(a)/Number(t):a,className:s("lucide",o),...!r&&!c(u)&&{"aria-hidden":"true"},...u},[...p.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(r)?r:[r]])),u=(e,t)=>{let a=(0,n.forwardRef)(({className:a,...o},c)=>(0,n.createElement)(p,{ref:c,iconNode:t,className:s(`lucide-${i(r(e))}`,`lucide-${e}`,a),...o}));return a.displayName=r(e),a}},63690:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{createMutableActionQueue:function(){return f},dispatchNavigateAction:function(){return v},dispatchTraverseAction:function(){return b},getCurrentAppRouterState:function(){return h},publicAppRouterInstance:function(){return g}});let n=a(59154),i=a(8830),o=a(43210),r=a(91992);a(50593);let s=a(19129),c=a(96127),l=a(89752),p=a(75076),u=a(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?m({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function m(e){let{actionQueue:t,action:a,setState:n}=e,i=t.state;t.pending=a;let o=a.payload,s=t.action(i,o);function c(e){a.discarded||(t.state=e,d(t,n),a.resolve(e))}(0,r.isThenable)(s)?s.then(c,e=>{d(t,n),a.reject(e)}):c(s)}function f(e,t){let a={state:e,dispatch:(e,t)=>(function(e,t,a){let i={resolve:a,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,o.startTransition)(()=>{a(e)})}let r={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=r,m({actionQueue:e,action:r,setState:a})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,r.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),m({actionQueue:e,action:r,setState:a})):(null!==e.last&&(e.last.next=r),e.last=r)})(a,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return a}function h(){return null}function x(){return null}function v(e,t,a,i){let o=new URL((0,c.addBasePath)(e),location.href);(0,u.setLinkForCurrentNavigation)(i);(0,s.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:o,isExternalUrl:(0,l.isExternalURL)(o),locationSearch:location.search,shouldScroll:a,navigateType:t,allowAliasing:!0})}function b(e,t){(0,s.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let g={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let a=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,l.createPrefetchURL)(e);if(null!==i){var o;(0,p.prefetchReducer)(a.state,{type:n.ACTION_PREFETCH,url:i,kind:null!=(o=null==t?void 0:t.kind)?o:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,o.startTransition)(()=>{var a;v(e,"replace",null==(a=null==t?void 0:t.scroll)||a,null)})},push:(e,t)=>{(0,o.startTransition)(()=>{var a;v(e,"push",null==(a=null==t?void 0:t.scroll)||a,null)})},refresh:()=>{(0,o.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63963:(e,t,a)=>{var n=a(41536),i=a(80271),o=a(45793);e.exports=function(e,t,a){for(var r=i(e);r.index<(r.keyedList||e).length;)n(e,t,r,function(e,t){return e?void a(e,t):0===Object.keys(r.jobs).length?void a(null,r.results):void 0}),r.index++;return o.bind(r,a)}},64171:(e,t,a)=>{e.exports=a(84933)},65951:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,a){let[o,r]=a,[s,c]=t;return(0,i.matchSegment)(s,o)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),r[c]):!!Array.isArray(s)}}});let n=a(74007),i=a(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{abortTask:function(){return f},listenForDynamicRequest:function(){return m},startPPRNavigation:function(){return l},updateCacheNodeOnPopstateRestoration:function(){return function e(t,a){let n=a[1],i=t.parallelRoutes,r=new Map(i);for(let t in n){let a=n[t],s=a[0],c=(0,o.createRouterCacheKey)(s),l=i.get(t);if(void 0!==l){let n=l.get(c);if(void 0!==n){let i=e(n,a),o=new Map(l);o.set(c,i),r.set(t,o)}}}let s=t.rsc,c=v(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:c?t.prefetchHead:[null,null],prefetchRsc:c?t.prefetchRsc:null,loading:t.loading,parallelRoutes:r,navigatedAt:t.navigatedAt}}}});let n=a(83913),i=a(14077),o=a(33123),r=a(2030),s=a(5334),c={route:null,node:null,dynamicRequestTree:null,children:null};function l(e,t,a,r,s,l,d,m,f){return function e(t,a,r,s,l,d,m,f,h,x,v){let b=r[1],g=s[1],y=null!==d?d[2]:null;l||!0===s[4]&&(l=!0);let w=a.parallelRoutes,_=new Map(w),R={},E=null,j=!1,O={};for(let a in g){let r,s=g[a],u=b[a],d=w.get(a),P=null!==y?y[a]:null,S=s[0],T=x.concat([a,S]),k=(0,o.createRouterCacheKey)(S),A=void 0!==u?u[0]:void 0,C=void 0!==d?d.get(k):void 0;if(null!==(r=S===n.DEFAULT_SEGMENT_KEY?void 0!==u?{route:u,node:null,dynamicRequestTree:null,children:null}:p(t,u,s,C,l,void 0!==P?P:null,m,f,T,v):h&&0===Object.keys(s[1]).length?p(t,u,s,C,l,void 0!==P?P:null,m,f,T,v):void 0!==u&&void 0!==A&&(0,i.matchSegment)(S,A)&&void 0!==C&&void 0!==u?e(t,C,u,s,l,P,m,f,h,T,v):p(t,u,s,C,l,void 0!==P?P:null,m,f,T,v))){if(null===r.route)return c;null===E&&(E=new Map),E.set(a,r);let e=r.node;if(null!==e){let t=new Map(d);t.set(k,e),_.set(a,t)}let t=r.route;R[a]=t;let n=r.dynamicRequestTree;null!==n?(j=!0,O[a]=n):O[a]=t}else R[a]=s,O[a]=s}if(null===E)return null;let P={lazyData:null,rsc:a.rsc,prefetchRsc:a.prefetchRsc,head:a.head,prefetchHead:a.prefetchHead,loading:a.loading,parallelRoutes:_,navigatedAt:t};return{route:u(s,R),node:P,dynamicRequestTree:j?u(s,O):null,children:E}}(e,t,a,r,!1,s,l,d,m,[],f)}function p(e,t,a,n,i,l,p,m,f,h){return!i&&(void 0===t||(0,r.isNavigatingToNewRootLayout)(t,a))?c:function e(t,a,n,i,r,c,l,p){let m,f,h,x,v=a[1],b=0===Object.keys(v).length;if(void 0!==n&&n.navigatedAt+s.DYNAMIC_STALETIME_MS>t)m=n.rsc,f=n.loading,h=n.head,x=n.navigatedAt;else if(null===i)return d(t,a,null,r,c,l,p);else if(m=i[1],f=i[3],h=b?r:null,x=t,i[4]||c&&b)return d(t,a,i,r,c,l,p);let g=null!==i?i[2]:null,y=new Map,w=void 0!==n?n.parallelRoutes:null,_=new Map(w),R={},E=!1;if(b)p.push(l);else for(let a in v){let n=v[a],i=null!==g?g[a]:null,s=null!==w?w.get(a):void 0,u=n[0],d=l.concat([a,u]),m=(0,o.createRouterCacheKey)(u),f=e(t,n,void 0!==s?s.get(m):void 0,i,r,c,d,p);y.set(a,f);let h=f.dynamicRequestTree;null!==h?(E=!0,R[a]=h):R[a]=n;let x=f.node;if(null!==x){let e=new Map;e.set(m,x),_.set(a,e)}}return{route:a,node:{lazyData:null,rsc:m,prefetchRsc:null,head:h,prefetchHead:null,loading:f,parallelRoutes:_,navigatedAt:x},dynamicRequestTree:E?u(a,R):null,children:y}}(e,a,n,l,p,m,f,h)}function u(e,t){let a=[e[0],t];return 2 in e&&(a[2]=e[2]),3 in e&&(a[3]=e[3]),4 in e&&(a[4]=e[4]),a}function d(e,t,a,n,i,r,s){let c=u(t,t[1]);return c[3]="refetch",{route:t,node:function e(t,a,n,i,r,s,c){let l=a[1],p=null!==n?n[2]:null,u=new Map;for(let a in l){let n=l[a],d=null!==p?p[a]:null,m=n[0],f=s.concat([a,m]),h=(0,o.createRouterCacheKey)(m),x=e(t,n,void 0===d?null:d,i,r,f,c),v=new Map;v.set(h,x),u.set(a,v)}let d=0===u.size;d&&c.push(s);let m=null!==n?n[1]:null,f=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:u,prefetchRsc:void 0!==m?m:null,prefetchHead:d?i:[null,null],loading:void 0!==f?f:null,rsc:b(),head:d?b():null,navigatedAt:t}}(e,t,a,n,i,r,s),dynamicRequestTree:c,children:null}}function m(e,t){t.then(t=>{let{flightData:a}=t;if("string"!=typeof a){for(let t of a){let{segmentPath:a,tree:n,seedData:r,head:s}=t;r&&function(e,t,a,n,r){let s=e;for(let e=0;e<t.length;e+=2){let a=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(a);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}!function e(t,a,n,r){if(null===t.dynamicRequestTree)return;let s=t.children,c=t.node;if(null===s){null!==c&&(function e(t,a,n,r,s){let c=a[1],l=n[1],p=r[2],u=t.parallelRoutes;for(let t in c){let a=c[t],n=l[t],r=p[t],d=u.get(t),m=a[0],f=(0,o.createRouterCacheKey)(m),x=void 0!==d?d.get(f):void 0;void 0!==x&&(void 0!==n&&(0,i.matchSegment)(m,n[0])&&null!=r?e(x,a,n,r,s):h(a,x,null))}let d=t.rsc,m=r[1];null===d?t.rsc=m:v(d)&&d.resolve(m);let f=t.head;v(f)&&f.resolve(s)}(c,t.route,a,n,r),t.dynamicRequestTree=null);return}let l=a[1],p=n[2];for(let t in a){let a=l[t],n=p[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(a[0],t)&&null!=n)return e(o,a,n,r)}}}(s,a,n,r)}(e,a,n,r,s)}f(e,null)}},t=>{f(e,t)})}function f(e,t){let a=e.node;if(null===a)return;let n=e.children;if(null===n)h(e.route,a,t);else for(let e of n.values())f(e,t);e.dynamicRequestTree=null}function h(e,t,a){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],r=i.get(e);if(void 0===r)continue;let s=t[0],c=(0,o.createRouterCacheKey)(s),l=r.get(c);void 0!==l&&h(t,l,a)}let r=t.rsc;v(r)&&(null===a?r.resolve(null):r.reject(a));let s=t.head;v(s)&&s.resolve(null)}let x=Symbol();function v(e){return e&&e.tag===x}function b(){let e,t,a=new Promise((a,n)=>{e=a,t=n});return a.status="pending",a.resolve=t=>{"pending"===a.status&&(a.status="fulfilled",a.value=t,e(t))},a.reject=e=>{"pending"===a.status&&(a.status="rejected",a.reason=e,t(e))},a.tag=x,a}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66232:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},67802:e=>{function t(e,t,a,n){return Math.round(e/a)+" "+n+(t>=1.5*a?"s":"")}e.exports=function(e,a){a=a||{};var n,i,o,r,s=typeof e;if("string"===s&&e.length>0){var c=e;if(!((c=String(c)).length>100)){var l=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(c);if(l){var p=parseFloat(l[1]);switch((l[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*p;case"weeks":case"week":case"w":return 6048e5*p;case"days":case"day":case"d":return 864e5*p;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*p;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*p;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*p;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return p;default:break}}}return}if("number"===s&&isFinite(e)){return a.long?(i=Math.abs(n=e))>=864e5?t(n,i,864e5,"day"):i>=36e5?t(n,i,36e5,"hour"):i>=6e4?t(n,i,6e4,"minute"):i>=1e3?t(n,i,1e3,"second"):n+" ms":(r=Math.abs(o=e))>=864e5?Math.round(o/864e5)+"d":r>=36e5?Math.round(o/36e5)+"h":r>=6e4?Math.round(o/6e4)+"m":r>=1e3?Math.round(o/1e3)+"s":o+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},69148:(e,t,a)=>{"use strict";e.exports=a(94041).vendored.contexts.RouterContext},69556:(e,t,a)=>{"use strict";a.d(t,{WQq:()=>o,nej:()=>r,t50:()=>i});var n=a(90296);function i(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M416 277.333H277.333V416h-42.666V277.333H96v-42.666h138.667V96h42.666v138.667H416v42.666z"},child:[]}]})(e)}function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M405 136.798L375.202 107 256 226.202 136.798 107 107 136.798 226.202 256 107 375.202 136.798 405 256 285.798 375.202 405 405 375.202 285.798 256z"},child:[]}]})(e)}function r(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M64 384h384v-42.666H64V384zm0-106.666h384v-42.667H64v42.667zM64 128v42.665h384V128H64z"},child:[]}]})(e)}},69587:(e,t,a)=>{"use strict";a.d(t,{EcP:()=>c,QEs:()=>r,Vk6:()=>l,ao$:()=>o,feZ:()=>s,iYk:()=>i,paH:()=>p});var n=a(90296);function i(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"},child:[]}]})(e)}function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"},child:[]}]})(e)}function r(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"},child:[]}]})(e)}function s(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"},child:[]}]})(e)}function c(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"},child:[]}]})(e)}function l(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"},child:[]}]})(e)}function p(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M320 448v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V120c0-13.255 10.745-24 24-24h72v296c0 30.879 25.121 56 56 56h168zm0-344V0H152c-13.255 0-24 10.745-24 24v368c0 13.255 10.745 24 24 24h272c13.255 0 24-10.745 24-24V128H344c-13.2 0-24-10.8-24-24zm120.971-31.029L375.029 7.029A24 24 0 0 0 358.059 0H352v96h96v-6.059a24 24 0 0 0-7.029-16.97z"},child:[]}]})(e)}},69996:(e,t,a)=>{var n=a(27910).Stream,i=a(28354);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,i.inherits(o,n),o.create=function(e,t){var a=new this;for(var n in t=t||{})a[n]=t[n];a.source=e;var i=e.emit;return e.emit=function(){return a._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),a.pauseStream&&e.pause(),a},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released)return void this.emit.apply(this,e);"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},70554:(e,t)=>{"use strict";function a(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return a}})},70607:(e,t,a)=>{"use strict";var n=a(92482),i=a(49088),o=a(99819),r=a(51105);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new i("a function is required");return r(n,o,e)}},70642:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{computeChangedPath:function(){return p},extractPathFromFlightRouterState:function(){return l},getSelectedParams:function(){return function e(t,a){for(let n of(void 0===a&&(a={}),Object.values(t[1]))){let t=n[0],o=Array.isArray(t),r=o?t[1]:t;!r||r.startsWith(i.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?a[t[0]]=t[1].split("/"):o&&(a[t[0]]=t[1]),a=e(n,a))}return a}}});let n=a(72859),i=a(83913),o=a(14077),r=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function c(e){return e.reduce((e,t)=>""===(t=r(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function l(e){var t;let a=Array.isArray(e[0])?e[0][1]:e[0];if(a===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>a.startsWith(e)))return;if(a.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(a)],r=null!=(t=e[1])?t:{},p=r.children?l(r.children):void 0;if(void 0!==p)o.push(p);else for(let[e,t]of Object.entries(r)){if("children"===e)continue;let a=l(t);void 0!==a&&o.push(a)}return c(o)}function p(e,t){let a=function e(t,a){let[i,r]=t,[c,p]=a,u=s(i),d=s(c);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)||d.startsWith(e)))return"";if(!(0,o.matchSegment)(i,c)){var m;return null!=(m=l(a))?m:""}for(let t in r)if(p[t]){let a=e(r[t],p[t]);if(null!==a)return s(c)+"/"+a}return null}(e,t);return null==a||"/"===a?a:c(a.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71437:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return r},isInterceptionRouteAppPath:function(){return o}});let n=a(74722),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function r(e){let t,a,o;for(let n of e.split("/"))if(a=i.find(e=>n.startsWith(e))){[t,o]=e.split(a,2);break}if(!t||!a||!o)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),a){case"(.)":o="/"===t?"/"+o:t+"/"+o;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let r=t.split("/");if(r.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});o=r.slice(0,-2).concat(o).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:o}}},72575:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},73406:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{IDLE_LINK_STATUS:function(){return l},PENDING_LINK_STATUS:function(){return c},mountFormInstance:function(){return b},mountLinkInstance:function(){return v},onLinkVisibilityChanged:function(){return y},onNavigationIntent:function(){return w},pingVisibleLinks:function(){return R},setLinkForCurrentNavigation:function(){return p},unmountLinkForCurrentNavigation:function(){return u},unmountPrefetchableInstance:function(){return g}}),a(63690);let n=a(89752),i=a(59154),o=a(50593),r=a(43210),s=null,c={pending:!0},l={pending:!1};function p(e){(0,r.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(l),null==e||e.setOptimisticLinkStatus(c),s=e})}function u(e){s===e&&(s=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,m=new Set,f="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;y(t.target,e)}},{rootMargin:"200px"}):null;function h(e,t){void 0!==d.get(e)&&g(e),d.set(e,t),null!==f&&f.observe(e)}function x(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function v(e,t,a,n,i,o){if(i){let i=x(t);if(null!==i){let t={router:a,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:o};return h(e,t),t}}return{router:a,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:o}}function b(e,t,a,n){let i=x(t);null!==i&&h(e,{router:a,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function g(e){let t=d.get(e);if(void 0!==t){d.delete(e),m.delete(t);let a=t.prefetchTask;null!==a&&(0,o.cancelPrefetchTask)(a)}null!==f&&f.unobserve(e)}function y(e,t){let a=d.get(e);void 0!==a&&(a.isVisible=t,t?m.add(a):m.delete(a),_(a))}function w(e,t){let a=d.get(e);void 0!==a&&void 0!==a&&(a.wasHoveredOrTouched=!0,_(a))}function _(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function R(e,t){let a=(0,o.getCurrentCacheVersion)();for(let n of m){let r=n.prefetchTask;if(null!==r&&n.cacheVersion===a&&r.key.nextUrl===e&&r.treeAtTimeOfPrefetch===t)continue;null!==r&&(0,o.cancelPrefetchTask)(r);let s=(0,o.createCacheKey)(n.prefetchHref,e),c=n.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;n.prefetchTask=(0,o.schedulePrefetchTask)(s,t,n.kind===i.PrefetchKind.FULL,c),n.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73514:(e,t,a)=>{"use strict";var n=a(81422);e.exports=function(e){return n(e)||0===e?e:e<0?-1:1}},74722:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return r}});let n=a(85531),i=a(35499);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,a,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&a===n.length-1?e:e+"/"+t,""))}function r(e){return e.replace(/\.rsc($|\?)/,"$1")}},75012:(e,t,a)=>{"use strict";var n,i=a(3361),o=a(86558),r=a(78750),s=a(7315),c=a(87631),l=a(15219),p=a(49088),u=a(10096),d=a(60863),m=a(30461),f=a(75845),h=a(53147),x=a(58501),v=a(75095),b=a(73514),g=Function,y=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},w=a(80036),_=a(48720),R=function(){throw new p},E=w?function(){try{return arguments.callee,R}catch(e){try{return w(arguments,"callee").get}catch(e){return R}}}():R,j=a(6582)(),O=a(9181),P=a(81285),S=a(62427),T=a(51951),k=a(99819),A={},C="undefined"!=typeof Uint8Array&&O?O(Uint8Array):n,M={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":j&&O?O([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":A,"%AsyncGenerator%":A,"%AsyncGeneratorFunction%":A,"%AsyncIteratorPrototype%":A,"%Atomics%":"undefined"==typeof Atomics?n:Atomics,"%BigInt%":"undefined"==typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":r,"%Float16Array%":"undefined"==typeof Float16Array?n:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":A,"%Int8Array%":"undefined"==typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":j&&O?O(O([][Symbol.iterator]())):n,"%JSON%":"object"==typeof JSON?JSON:n,"%Map%":"undefined"==typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&j&&O?O(new Map()[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":i,"%Object.getOwnPropertyDescriptor%":w,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?n:Promise,"%Proxy%":"undefined"==typeof Proxy?n:Proxy,"%RangeError%":s,"%ReferenceError%":c,"%Reflect%":"undefined"==typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&j&&O?O(new Set()[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":j&&O?O(""[Symbol.iterator]()):n,"%Symbol%":j?Symbol:n,"%SyntaxError%":l,"%ThrowTypeError%":E,"%TypedArray%":C,"%TypeError%":p,"%Uint8Array%":"undefined"==typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?n:Uint32Array,"%URIError%":u,"%WeakMap%":"undefined"==typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?n:WeakSet,"%Function.prototype.call%":k,"%Function.prototype.apply%":T,"%Object.defineProperty%":_,"%Object.getPrototypeOf%":P,"%Math.abs%":d,"%Math.floor%":m,"%Math.max%":f,"%Math.min%":h,"%Math.pow%":x,"%Math.round%":v,"%Math.sign%":b,"%Reflect.getPrototypeOf%":S};if(O)try{null.error}catch(e){var N=O(O(e));M["%Error.prototype%"]=N}var U=function e(t){var a;if("%AsyncFunction%"===t)a=y("async function () {}");else if("%GeneratorFunction%"===t)a=y("function* () {}");else if("%AsyncGeneratorFunction%"===t)a=y("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(a=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var i=e("%AsyncGenerator%");i&&O&&(a=O(i.prototype))}return M[t]=a,a},L={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},F=a(92482),z=a(56786),I=F.call(k,Array.prototype.concat),D=F.call(T,Array.prototype.splice),B=F.call(k,String.prototype.replace),q=F.call(k,String.prototype.slice),H=F.call(k,RegExp.prototype.exec),$=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,W=/\\(\\)?/g,V=function(e){var t=q(e,0,1),a=q(e,-1);if("%"===t&&"%"!==a)throw new l("invalid intrinsic syntax, expected closing `%`");if("%"===a&&"%"!==t)throw new l("invalid intrinsic syntax, expected opening `%`");var n=[];return B(e,$,function(e,t,a,i){n[n.length]=a?B(i,W,"$1"):t||e}),n},K=function(e,t){var a,n=e;if(z(L,n)&&(n="%"+(a=L[n])[0]+"%"),z(M,n)){var i=M[n];if(i===A&&(i=U(n)),void 0===i&&!t)throw new p("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:a,name:n,value:i}}throw new l("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new p("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new p('"allowMissing" argument must be a boolean');if(null===H(/^%?[^%]*%?$/,e))throw new l("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var a=V(e),n=a.length>0?a[0]:"",i=K("%"+n+"%",t),o=i.name,r=i.value,s=!1,c=i.alias;c&&(n=c[0],D(a,I([0,1],c)));for(var u=1,d=!0;u<a.length;u+=1){var m=a[u],f=q(m,0,1),h=q(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===h||"'"===h||"`"===h)&&f!==h)throw new l("property names with quotes must have matching quotes");if("constructor"!==m&&d||(s=!0),n+="."+m,z(M,o="%"+n+"%"))r=M[o];else if(null!=r){if(!(m in r)){if(!t)throw new p("base intrinsic for "+e+" exists, but the property is not available.");return}if(w&&u+1>=a.length){var x=w(r,m);r=(d=!!x)&&"get"in x&&!("originalValue"in x.get)?x.get:r[m]}else d=z(r,m),r=r[m];d&&!s&&(M[o]=r)}}return r}},75076:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return r}});let n=a(5144),i=a(5334),o=new n.PromiseQueue(5),r=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:a}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:a,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75095:e=>{"use strict";e.exports=Math.round},75845:e=>{"use strict";e.exports=Math.max},76715:(e,t)=>{"use strict";function a(e){let t={};for(let[a,n]of e.entries()){let e=t[a];void 0===e?t[a]=n:Array.isArray(e)?e.push(n):t[a]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[a,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(a,n(e));else t.set(a,n(i));return t}function o(e){for(var t=arguments.length,a=Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];for(let t of a){for(let a of t.keys())e.delete(a);for(let[a,n]of t.entries())e.append(a,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return i}})},76759:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=a(42785),i=a(23736);function o(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},77022:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return r}});let n=a(43210),i=a(51215),o="next-route-announcer";function r(e){let{tree:t}=e,[a,r]=(0,n.useState)(null);(0,n.useEffect)(()=>(r(function(){var e;let t=document.getElementsByName(o)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,c]=(0,n.useState)(""),l=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==l.current&&l.current!==e&&c(e),l.current=e},[t]),a?(0,i.createPortal)(s,a):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78002:(e,t,a)=>{"use strict";var n=a(75012)("%Object.defineProperty%",!0),i=a(92909)(),o=a(56786),r=a(49088),s=i?Symbol.toStringTag:null;e.exports=function(e,t){var a=arguments.length>2&&!!arguments[2]&&arguments[2].force,i=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==a&&"boolean"!=typeof a||void 0!==i&&"boolean"!=typeof i)throw new r("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");s&&(a||!o(e,s))&&(n?n(e,s,{configurable:!i,enumerable:!1,value:t,writable:!1}):e[s]=t)}},78034:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=a(44827);function i(e){let{re:t,groups:a}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},r={};for(let[e,t]of Object.entries(a)){let a=i[t.pos];void 0!==a&&(t.repeat?r[e]=a.split("/").map(e=>o(e)):r[e]=o(a))}return r}}},78360:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},78750:e=>{"use strict";e.exports=EvalError},78866:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return f}});let n=a(59008),i=a(57391),o=a(86770),r=a(2030),s=a(25232),c=a(59435),l=a(41500),p=a(89752),u=a(96493),d=a(68214),m=a(22308);function f(e,t){let{origin:a}=t,f={},h=e.canonicalUrl,x=e.tree;f.preserveCustomHistoryState=!1;let v=(0,p.createEmptyCacheNode)(),b=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);v.lazyData=(0,n.fetchServerResponse)(new URL(h,a),{flightRouterState:[x[0],x[1],x[2],"refetch"],nextUrl:b?e.nextUrl:null});let g=Date.now();return v.lazyData.then(async a=>{let{flightData:n,canonicalUrl:p}=a;if("string"==typeof n)return(0,s.handleExternalUrl)(e,f,n,e.pushRef.pendingPush);for(let a of(v.lazyData=null,n)){let{tree:n,seedData:c,head:d,isRootRender:y}=a;if(!y)return console.log("REFRESH FAILED"),e;let w=(0,o.applyRouterStatePatchToTree)([""],x,n,e.canonicalUrl);if(null===w)return(0,u.handleSegmentMismatch)(e,t,n);if((0,r.isNavigatingToNewRootLayout)(x,w))return(0,s.handleExternalUrl)(e,f,h,e.pushRef.pendingPush);let _=p?(0,i.createHrefFromUrl)(p):void 0;if(p&&(f.canonicalUrl=_),null!==c){let e=c[1],t=c[3];v.rsc=e,v.prefetchRsc=null,v.loading=t,(0,l.fillLazyItemsTillLeafWithHead)(g,v,void 0,n,c,d,void 0),f.prefetchCache=new Map}await (0,m.refreshInactiveParallelSegments)({navigatedAt:g,state:e,updatedTree:w,updatedCache:v,includeNextUrl:b,canonicalUrl:f.canonicalUrl||e.canonicalUrl}),f.cache=v,f.patchedTree=w,x=w}return(0,c.handleMutable)(e,f)},()=>e)}a(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return v},NormalizeError:function(){return h},PageNotFoundError:function(){return x},SP:function(){return d},ST:function(){return m},WEB_VITALS:function(){return a},execOnce:function(){return n},getDisplayName:function(){return c},getLocationOrigin:function(){return r},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return l},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return p},stringifyError:function(){return g}});let a=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,a=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return a||(a=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function r(){let{protocol:e,hostname:t,port:a}=window.location;return e+"//"+t+(a?":"+a:"")}function s(){let{href:e}=window.location,t=r();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function p(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let a=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(a&&l(a))return n;if(!n)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,m=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class h extends Error{}class x extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function g(e){return JSON.stringify({message:e.message,stack:e.stack})}},80036:(e,t,a)=>{"use strict";var n=a(91176);if(n)try{n([],"length")}catch(e){n=null}e.exports=n},80271:e=>{e.exports=function(e,t){var a=!Array.isArray(e),n={index:0,keyedList:a||t?Object.keys(e):null,jobs:{},results:a?{}:[],size:a?Object.keys(e).length:e.length};return t&&n.keyedList.sort(a?t:function(a,n){return t(e[a],e[n])}),n}},81285:(e,t,a)=>{"use strict";e.exports=a(3361).getPrototypeOf||null},81422:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},83644:(e,t,a)=>{var n=a(28354),i=a(27910).Stream,o=a(69996);function r(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=r,n.inherits(r,i),r.create=function(e){var t=new this;for(var a in e=e||{})t[a]=e[a];return t},r.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},r.prototype.append=function(e){if(r.isStreamLike(e)){if(!(e instanceof o)){var t=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=t}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},r.prototype.pipe=function(e,t){return i.prototype.pipe.call(this,e,t),this.resume(),e},r.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},r.prototype._realGetNext=function(){var e=this._streams.shift();return void 0===e?void this.end():"function"!=typeof e?void this._pipeNext(e):void e((function(e){r.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},r.prototype._pipeNext=function(e){if(this._currentStream=e,r.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},r.prototype._handleErrors=function(e){var t=this;e.on("error",function(e){t._emitError(e)})},r.prototype.write=function(e){this.emit("data",e)},r.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},r.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},r.prototype.end=function(){this._reset(),this.emit("end")},r.prototype.destroy=function(){this._reset(),this.emit("close")},r.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},r.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},r.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(t){t.dataSize&&(e.dataSize+=t.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},r.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},84933:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},84949:(e,t)=>{"use strict";function a(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return a}})},85026:(e,t,a)=>{e.exports={parallel:a(63963),serial:a(86736),serialOrdered:a(86271)}},85531:(e,t)=>{"use strict";function a(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return a}})},85814:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return x},useLinkStatus:function(){return b}});let n=a(84441),i=a(60687),o=n._(a(43210)),r=a(30195),s=a(22142),c=a(59154),l=a(53038),p=a(79289),u=a(96127);a(50148);let d=a(73406),m=a(61794),f=a(63690);function h(e){return"string"==typeof e?e:(0,r.formatUrl)(e)}function x(e){let t,a,n,[r,x]=(0,o.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,o.useRef)(null),{href:g,as:y,children:w,prefetch:_=null,passHref:R,replace:E,shallow:j,scroll:O,onClick:P,onMouseEnter:S,onTouchStart:T,legacyBehavior:k=!1,onNavigate:A,ref:C,unstable_dynamicOnHover:M,...N}=e;t=w,k&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let U=o.default.useContext(s.AppRouterContext),L=!1!==_,F=null===_?c.PrefetchKind.AUTO:c.PrefetchKind.FULL,{href:z,as:I}=o.default.useMemo(()=>{let e=h(g);return{href:e,as:y?h(y):e}},[g,y]);k&&(a=o.default.Children.only(t));let D=k?a&&"object"==typeof a&&a.ref:C,B=o.default.useCallback(e=>(null!==U&&(b.current=(0,d.mountLinkInstance)(e,z,U,F,L,x)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[L,z,U,F,x]),q={ref:(0,l.useMergedRef)(B,D),onClick(e){k||"function"!=typeof P||P(e),k&&a.props&&"function"==typeof a.props.onClick&&a.props.onClick(e),U&&(e.defaultPrevented||function(e,t,a,n,i,r,s){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,m.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,f.dispatchNavigateAction)(a||t,i?"replace":"push",null==r||r,n.current)})}}(e,z,I,b,E,O,A))},onMouseEnter(e){k||"function"!=typeof S||S(e),k&&a.props&&"function"==typeof a.props.onMouseEnter&&a.props.onMouseEnter(e),U&&L&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){k||"function"!=typeof T||T(e),k&&a.props&&"function"==typeof a.props.onTouchStart&&a.props.onTouchStart(e),U&&L&&(0,d.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,p.isAbsoluteUrl)(I)?q.href=I:k&&!R&&("a"!==a.type||"href"in a.props)||(q.href=(0,u.addBasePath)(I)),n=k?o.default.cloneElement(a,q):(0,i.jsx)("a",{...N,...q,children:t}),(0,i.jsx)(v.Provider,{value:r,children:n})}a(32708);let v=(0,o.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,o.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86271:(e,t,a)=>{var n=a(41536),i=a(80271),o=a(45793);function r(e,t){return e<t?-1:+(e>t)}e.exports=function(e,t,a,r){var s=i(e,a);return n(e,t,s,function a(i,o){return i?void r(i,o):(s.index++,s.index<(s.keyedList||e).length)?void n(e,t,s,a):void r(null,s.results)}),o.bind(s,r)},e.exports.ascending=r,e.exports.descending=function(e,t){return -1*r(e,t)}},86338:e=>{e.exports=function(e){var t="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;t?t(e):setTimeout(e,0)}},86558:e=>{"use strict";e.exports=Error},86736:(e,t,a)=>{var n=a(86271);e.exports=function(e,t,a){return n(e,t,null,a)}},86770:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,a,n,c){let l,[p,u,d,m,f]=a;if(1===t.length){let e=s(a,n);return(0,r.addRefreshMarkerToActiveParallelSegments)(e,c),e}let[h,x]=t;if(!(0,o.matchSegment)(h,p))return null;if(2===t.length)l=s(u[x],n);else if(null===(l=e((0,i.getNextFlightSegmentPath)(t),u[x],n,c)))return null;let v=[t[0],{...u,[x]:l},d,m];return f&&(v[4]=!0),(0,r.addRefreshMarkerToActiveParallelSegments)(v,c),v}}});let n=a(83913),i=a(74007),o=a(14077),r=a(22308);function s(e,t){let[a,i]=e,[r,c]=t;if(r===n.DEFAULT_SEGMENT_KEY&&a!==n.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(a,r)){let t={};for(let e in i)void 0!==c[e]?t[e]=s(i[e],c[e]):t[e]=i[e];for(let e in c)t[e]||(t[e]=c[e]);let n=[a,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87631:e=>{"use strict";e.exports=ReferenceError},88212:(e,t,a)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=a(26415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},89513:(e,t,a)=>{"use strict";e.exports=a(94041).vendored.contexts.HeadManagerContext},89752:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{createEmptyCacheNode:function(){return T},createPrefetchURL:function(){return P},default:function(){return M},isExternalURL:function(){return O}});let n=a(84441),i=a(60687),o=n._(a(43210)),r=a(22142),s=a(59154),c=a(57391),l=a(10449),p=a(19129),u=n._(a(35656)),d=a(35416),m=a(96127),f=a(77022),h=a(67086),x=a(44397),v=a(89330),b=a(25942),g=a(26736),y=a(70642),w=a(12776),_=a(63690),R=a(36875),E=a(97860);a(73406);let j={};function O(e){return e.origin!==window.location.origin}function P(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,m.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return O(t)?null:t}function S(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:a,canonicalUrl:n}=t,i={...a.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};a.pendingPush&&(0,c.createHrefFromUrl)(new URL(window.location.href))!==n?(a.pendingPush=!1,window.history.pushState(i,"",n)):window.history.replaceState(i,"",n)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function T(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function k(e){null==e&&(e={});let t=window.history.state,a=null==t?void 0:t.__NA;a&&(e.__NA=a);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function A(e){let{headCacheNode:t}=e,a=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:a;return(0,o.useDeferredValue)(a,i)}function C(e){let t,{actionQueue:a,assetPrefix:n,globalError:c}=e,d=(0,p.useActionQueue)(a),{canonicalUrl:m}=d,{searchParams:w,pathname:O}=(0,o.useMemo)(()=>{let e=new URL(m,"http://n");return{searchParams:e.searchParams,pathname:(0,g.hasBasePath)(e.pathname)?(0,b.removeBasePath)(e.pathname):e.pathname}},[m]);(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(j.pendingMpaPath=void 0,(0,p.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,E.isRedirectError)(t)){e.preventDefault();let a=(0,R.getURLFromRedirectError)(t);(0,R.getRedirectTypeFromError)(t)===E.RedirectType.push?_.publicAppRouterInstance.push(a,{}):_.publicAppRouterInstance.replace(a,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:P}=d;if(P.mpaNavigation){if(j.pendingMpaPath!==m){let e=window.location;P.pendingPush?e.assign(m):e.replace(m),j.pendingMpaPath=m}(0,o.use)(v.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),a=e=>{var t;let a=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{(0,p.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:a,a),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=k(t),i&&a(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=k(e),i&&a(i)),t(e,n,i)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,o.startTransition)(()=>{(0,_.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:T,tree:C,nextUrl:M,focusAndScrollRef:N}=d,U=(0,o.useMemo)(()=>(0,x.findHeadInCache)(T,C[1]),[T,C]),F=(0,o.useMemo)(()=>(0,y.getSelectedParams)(C),[C]),z=(0,o.useMemo)(()=>({parentTree:C,parentCacheNode:T,parentSegmentPath:null,url:m}),[C,T,m]),I=(0,o.useMemo)(()=>({tree:C,focusAndScrollRef:N,nextUrl:M}),[C,N,M]);if(null!==U){let[e,a]=U;t=(0,i.jsx)(A,{headCacheNode:e},a)}else t=null;let D=(0,i.jsxs)(h.RedirectBoundary,{children:[t,T.rsc,(0,i.jsx)(f.AppRouterAnnouncer,{tree:C})]});return D=(0,i.jsx)(u.ErrorBoundary,{errorComponent:c[0],errorStyles:c[1],children:D}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(S,{appRouterState:d}),(0,i.jsx)(L,{}),(0,i.jsx)(l.PathParamsContext.Provider,{value:F,children:(0,i.jsx)(l.PathnameContext.Provider,{value:O,children:(0,i.jsx)(l.SearchParamsContext.Provider,{value:w,children:(0,i.jsx)(r.GlobalLayoutRouterContext.Provider,{value:I,children:(0,i.jsx)(r.AppRouterContext.Provider,{value:_.publicAppRouterInstance,children:(0,i.jsx)(r.LayoutRouterContext.Provider,{value:z,children:D})})})})})})]})}function M(e){let{actionQueue:t,globalErrorComponentAndStyles:[a,n],assetPrefix:o}=e;return(0,w.useNavFailureHandler)(),(0,i.jsx)(u.ErrorBoundary,{errorComponent:u.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:o,globalError:[a,n]})})}let N=new Set,U=new Set;function L(){let[,e]=o.default.useState(0),t=N.size;return(0,o.useEffect)(()=>{let a=()=>e(e=>e+1);return U.add(a),t!==N.size&&a(),()=>{U.delete(a)}},[t,e]),[...N].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=N.size;return N.add(e),N.size!==t&&U.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90296:(e,t,a)=>{"use strict";a.d(t,{k5:()=>p});var n=a(43210),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=n.createContext&&n.createContext(i),r=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}function c(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,n)}return a}function l(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?c(Object(a),!0).forEach(function(t){var n,i,o;n=e,i=t,o=a[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var n=a.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function p(e){return t=>n.createElement(u,s({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,a)=>n.createElement(t.tag,l({key:a},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var a,{attr:i,size:o,title:c}=e,p=function(e,t){if(null==e)return{};var a,n,i=function(e,t){if(null==e)return{};var a={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;a[n]=e[n]}return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)a=o[n],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(i[a]=e[a])}return i}(e,r),u=o||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,p,{className:a,style:l(l({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==o?n.createElement(o.Consumer,null,e=>t(e)):t(i)}},91176:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},91268:(e,t,a)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=a(36632):e.exports=a(30678)},92296:(e,t,a)=>{var n;e.exports=function(){if(!n){try{n=a(91268)("follow-redirects")}catch(e){}"function"!=typeof n&&(n=function(){})}n.apply(null,arguments)}},92482:(e,t,a)=>{"use strict";var n=a(47530);e.exports=Function.prototype.bind||n},92909:(e,t,a)=>{"use strict";var n=a(54544);e.exports=function(){return n()&&!!Symbol.toStringTag}},94458:(e,t,a)=>{var n=a(86338);e.exports=function(e){var t=!1;return n(function(){t=!0}),function(a,i){t?e(a,i):n(function(){e(a,i)})}}},95796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return a}});let a=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},95930:(e,t,a)=>{"use strict";var n=a(64171),i=a(33873).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,r=/^text\//i;function s(e){if(!e||"string"!=typeof e)return!1;var t=o.exec(e),a=t&&n[t[1].toLowerCase()];return a&&a.charset?a.charset:!!(t&&r.test(t[1]))&&"UTF-8"}t.charset=s,t.charsets={lookup:s},t.contentType=function(e){if(!e||"string"!=typeof e)return!1;var a=-1===e.indexOf("/")?t.lookup(e):e;if(!a)return!1;if(-1===a.indexOf("charset")){var n=t.charset(a);n&&(a+="; charset="+n.toLowerCase())}return a},t.extension=function(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),n=a&&t.extensions[a[1].toLowerCase()];return!!n&&!!n.length&&n[0]},t.extensions=Object.create(null),t.lookup=function(e){if(!e||"string"!=typeof e)return!1;var a=i("x."+e).toLowerCase().substr(1);return!!a&&(t.types[a]||!1)},t.types=Object.create(null),function(e,t){var a=["nginx","apache",void 0,"iana"];Object.keys(n).forEach(function(i){var o=n[i],r=o.extensions;if(r&&r.length){e[i]=r;for(var s=0;s<r.length;s++){var c=r[s];if(t[c]){var l=a.indexOf(n[t[c]].source),p=a.indexOf(o.source);if("application/octet-stream"!==t[c]&&(l>p||l===p&&"application/"===t[c].substr(0,12)))continue}t[c]=i}}})}(t.extensions,t.types)},96127:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=a(98834),i=a(54674);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96211:(e,t,a)=>{e.exports=function(e){function t(e){let a,i,o,r=null;function s(...e){if(!s.enabled)return;let n=Number(new Date);s.diff=n-(a||n),s.prev=a,s.curr=n,a=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(a,n)=>{if("%%"===a)return"%";i++;let o=t.formatters[n];if("function"==typeof o){let t=e[i];a=o.call(s,t),e.splice(i,1),i--}return a}),t.formatArgs.call(s,e),(s.log||t.log).apply(s,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=n,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(i!==t.namespaces&&(i=t.namespaces,o=t.enabled(e)),o),set:e=>{r=e}}),"function"==typeof t.init&&t.init(s),s}function n(e,a){let n=t(this.namespace+(void 0===a?":":a)+e);return n.log=this.log,n}function i(e,t){let a=0,n=0,i=-1,o=0;for(;a<e.length;)if(n<t.length&&(t[n]===e[a]||"*"===t[n]))"*"===t[n]?(i=n,o=a):a++,n++;else{if(-1===i)return!1;n=i+1,a=++o}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let a of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===a[0]?t.skips.push(a.slice(1)):t.names.push(a)},t.enabled=function(e){for(let a of t.skips)if(i(e,a))return!1;for(let a of t.names)if(i(e,a))return!0;return!1},t.humanize=a(67802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(a=>{t[a]=e[a]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t)|0;return t.colors[Math.abs(a)%t.colors.length]},t.enable(t.load()),t}},96493:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=a(25232);function i(e,t,a){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,a,o){let r=o.length<=2,[s,c]=o,l=(0,i.createRouterCacheKey)(c),p=a.parallelRoutes.get(s),u=t.parallelRoutes.get(s);u&&u!==p||(u=new Map(p),t.parallelRoutes.set(s,u));let d=null==p?void 0:p.get(l),m=u.get(l);if(r){m&&m.lazyData&&m!==d||u.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!m||!d){m||u.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return m===d&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),loading:m.loading},u.set(l,m)),e(m,d,(0,n.getNextFlightSegmentPath)(o))}}});let n=a(74007),i=a(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),a(59008),a(57391),a(86770),a(2030),a(25232),a(59435),a(56928),a(89752),a(96493),a(68214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=a(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:a,query:i,hash:o}=(0,n.parsePath)(e);return""+t+a+i+o}},99819:e=>{"use strict";e.exports=Function.prototype.call}};