(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[909],{1168:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a={src:"/_next/static/media/pinktree.b502aac7.png",height:195,width:256,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAKlBMVEVMaXH/q9bdorPZoK7YoK/RlafMnaeyg43Ll6LCjprTnavgq7jWobB/XV3u3GU9AAAADnRSTlMAAjtgjSgXUCVqL0x4Hph5UpkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAQAwDMMwp3+PP91JgCQsgBqOJ4VviwKb2ysRHuvtFLS3CfgR1AC3VmqfqgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6}},1966:(e,r,s)=>{"use strict";s.d(r,{JW:()=>a,KB:()=>l,c$:()=>t});let a={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},t=e=>{let r=a.BASE_URL,s=e.startsWith("/")?e:"/".concat(e);return"".concat(r,"/api").concat(s)},l=t},3074:(e,r,s)=>{Promise.resolve().then(s.bind(s,5700))},5700:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var a=s(5155),t=s(2115),l=s(7771),o=s(9946);let n=(0,o.A)("phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),i=(0,o.A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),c=(0,o.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),d=(0,o.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var m=s(7314),u=s(8543);let h=()=>{let[e,r]=(0,t.useState)({name:"",email:"",countryCode:"+91",phoneNumber:"",service:"",message:""}),[s,o]=(0,t.useState)({}),[h,x]=(0,t.useState)(!1),g=e=>{let{name:a,value:t}=e.target;r(e=>({...e,[a]:t})),s[a]&&o(e=>({...e,[a]:""}))},p=async s=>{s.preventDefault();let a=(0,m.h)(e);if(!a.isValid)return void o(a.errors);x(!0),o({});try{let s=await m.T.submitContact(e);s.success?(u.oR.success("\uD83C\uDF89 Thank you for your message! We'll get back to you soon.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0}),r({name:"",email:"",countryCode:"+91",phoneNumber:"",service:"",message:""})):u.oR.error(s.message||"Failed to send message. Please try again.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0})}catch(e){console.error("Contact form error:",e),e.errors?(o(e.errors),u.oR.error("Please fix the errors below and try again.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0})):u.oR.error(e.message||"Failed to send message. Please try again.",{position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0})}finally{x(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{title:"CONTACT",breadcrumbs:[{label:"HOME",href:"/"},{label:"CONTACT",href:"/contact"}]}),(0,a.jsx)("div",{className:"min-h-screen py-12",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-medium mb-6",children:"Get In Touch"}),(0,a.jsx)("p",{className:"text-gray-700 mb-8",children:"We'd love to hear from you! Whether you're ready to start planning your special day or just have questions, our team is here to help."}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-[#FEF2EB] p-3 rounded-full mr-4",children:(0,a.jsx)(n,{className:"h-5 w-5 text-[#FE904B]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Phone"}),(0,a.jsx)("p",{className:"text-gray-700",children:"+91-9735284928"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-[#FEF2EB] p-3 rounded-full mr-4",children:(0,a.jsx)(i,{className:"h-5 w-5 text-[#FE904B]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-700",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-[#FEF2EB] p-3 rounded-full mr-4",children:(0,a.jsx)(c,{className:"h-5 w-5 text-[#FE904B]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Address"}),(0,a.jsx)("p",{className:"text-gray-700",children:"123 Wedding Lane, Mumbai, India 400001"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-[#FEF2EB] p-3 rounded-full mr-4",children:(0,a.jsx)(d,{className:"h-5 w-5 text-[#FE904B]"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:"Hours"}),(0,a.jsx)("p",{className:"text-gray-700",children:"Monday - Friday: 9am - 6pm"}),(0,a.jsx)("p",{className:"text-gray-700",children:"Saturday: 10am - 4pm"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-[#FEF2EB] p-8 rounded-lg",children:[(0,a.jsx)("h2",{className:"text-2xl font-medium mb-6",children:"Send Us a Message"}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:g,className:"w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ".concat(s.name?"border-red-500":"border-gray-300"),placeholder:"Your full name",required:!0}),s.name&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:s.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:g,className:"w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ".concat(s.email?"border-red-500":"border-gray-300"),placeholder:"<EMAIL>",required:!0}),s.email&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:s.email})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"countryCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Country Code *"}),(0,a.jsxs)("select",{id:"countryCode",name:"countryCode",value:e.countryCode,onChange:g,className:"w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ".concat(s.countryCode?"border-red-500":"border-gray-300"),required:!0,children:[(0,a.jsx)("option",{value:"+91",children:"+91 (India)"}),(0,a.jsx)("option",{value:"+1",children:"+1 (US/Canada)"}),(0,a.jsx)("option",{value:"+44",children:"+44 (UK)"}),(0,a.jsx)("option",{value:"+61",children:"+61 (Australia)"}),(0,a.jsx)("option",{value:"+971",children:"+971 (UAE)"}),(0,a.jsx)("option",{value:"+65",children:"+65 (Singapore)"})]}),s.countryCode&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:s.countryCode})]}),(0,a.jsxs)("div",{className:"sm:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number *"}),(0,a.jsx)("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:e.phoneNumber,onChange:g,className:"w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ".concat(s.phoneNumber?"border-red-500":"border-gray-300"),placeholder:"9876543210",required:!0}),s.phoneNumber&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:s.phoneNumber})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-1",children:"Service Required *"}),(0,a.jsx)("input",{type:"text",id:"service",name:"service",value:e.service,onChange:g,className:"w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ".concat(s.service?"border-red-500":"border-gray-300"),placeholder:"e.g., Wedding Planning, Event Management, Venue Booking",required:!0}),s.service&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:s.service})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Message *"}),(0,a.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:g,rows:5,className:"w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ".concat(s.message?"border-red-500":"border-gray-300"),placeholder:"Tell us about your requirements...",required:!0}),s.message&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:s.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.message.length,"/1000 characters"]})]}),(0,a.jsx)("button",{type:"submit",disabled:h,className:"w-full px-6 py-3 rounded-full transition-colors ".concat(h?"bg-gray-400 cursor-not-allowed":"bg-[#FE904B] hover:bg-[#e87f3d] text-white"),children:h?"Sending...":"Send Message"})]})]})]})})})]})}},7314:(e,r,s)=>{"use strict";s.d(r,{T:()=>l,h:()=>o});var a=s(7693),t=s(1966);let l={submitContact:async e=>{try{return(await a.u.post((0,t.c$)("/contacts"),e)).data}catch(e){var r;if(null==(r=e.response)?void 0:r.data)throw e.response.data;throw{success:!1,message:"Failed to submit contact form. Please try again."}}},submitQuote:async e=>{try{return(await a.u.post((0,t.c$)("/contacts"),{...e,service:e.service||"Quote Request"})).data}catch(e){var r;if(null==(r=e.response)?void 0:r.data)throw e.response.data;throw{success:!1,message:"Failed to submit quote request. Please try again."}}}},o=e=>{var r,s,a,t,l,o;let n={};if((null==(r=e.name)?void 0:r.trim())?e.name.length>100&&(n.name="Name cannot exceed 100 characters"):n.name="Name is required",(null==(s=e.email)?void 0:s.trim())?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(n.email="Invalid email format"):n.email="Email is required",(null==(a=e.countryCode)?void 0:a.trim())||(n.countryCode="Country code is required"),null==(t=e.phoneNumber)?void 0:t.trim()){let r=e.phoneNumber.replace(/\s|-/g,"");/^[\d\s\-]{8,15}$/.test(r)||(n.phoneNumber="Invalid phone number format (8-15 digits)")}else n.phoneNumber="Phone number is required";return(null==(l=e.service)?void 0:l.trim())||(n.service="Service is required"),(null==(o=e.message)?void 0:o.trim())?e.message.length>1e3&&(n.message="Message cannot exceed 1000 characters"):n.message="Message is required",{isValid:0===Object.keys(n).length,errors:n}}},7693:(e,r,s)=>{"use strict";s.d(r,{u:()=>o});var a=s(3464),t=s(1966);let l=a.A.create({timeout:t.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});l.interceptors.request.use(e=>{{let r=localStorage.getItem("authToken");r&&e.headers&&(e.headers.Authorization="Bearer ".concat(r))}return e},e=>(console.error("❌ Request Error:",e),Promise.reject(e))),l.interceptors.response.use(e=>e,e=>{if(e.response){let{status:r,data:s}=e.response;switch(r){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",s)}}else if(e.request){var r,s,a;console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:null==(r=e.config)?void 0:r.url,method:null==(s=e.config)?void 0:s.method,baseURL:null==(a=e.config)?void 0:a.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."}else console.error("❌ Error:",e.message);return Promise.reject(e)});let o={get:(e,r)=>l.get(e,r),post:(e,r,s)=>l.post(e,r,s),put:(e,r,s)=>l.put(e,r,s),patch:(e,r,s)=>l.patch(e,r,s),delete:(e,r)=>l.delete(e,r)}},7771:(e,r,s)=>{"use strict";s.d(r,{A:()=>c});var a=s(5155),t=s(2115),l=s(6766),o=s(6874),n=s.n(o),i=s(1168);let c=e=>{let{title:r,breadcrumbs:s}=e;return(0,a.jsx)("div",{className:"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0",children:r}),(0,a.jsx)("div",{className:"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10",children:(0,a.jsx)(l.default,{src:i.default,alt:"Cherry Blossom",width:130,height:100,style:{width:"auto",height:"auto"},className:"object-contain"})}),(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm z-20",children:s.map((e,r)=>(0,a.jsxs)(t.Fragment,{children:[(0,a.jsx)(n(),{href:e.href,className:"hover:text-[#FE904B] transition-colors",children:e.label}),r<s.length-1&&(0,a.jsx)("span",{className:"text-gray-400",children:"›"})]},r))})]})})}},9946:(e,r,s)=>{"use strict";s.d(r,{A:()=>m});var a=s(2115);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),o=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},n=function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return r.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim()},i=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,r)=>{let{color:s="currentColor",size:t=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:m,iconNode:u,...h}=e;return(0,a.createElement)("svg",{ref:r,...c,width:t,height:t,stroke:s,strokeWidth:o?24*Number(l)/Number(t):l,className:n("lucide",d),...!m&&!i(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[r,s]=e;return(0,a.createElement)(r,s)}),...Array.isArray(m)?m:[m]])}),m=(e,r)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:i,...c}=s;return(0,a.createElement)(d,{ref:l,iconNode:r,className:n("lucide-".concat(t(o(e))),"lucide-".concat(e),i),...c})});return s.displayName=o(e),s}}},e=>{var r=r=>e(e.s=r);e.O(0,[83,854,543,441,684,358],()=>r(3074)),_N_E=e.O()}]);