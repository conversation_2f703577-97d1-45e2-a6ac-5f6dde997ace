// Venue API module
import { apiClient } from '../../customaxios';
import { buildApiUrl } from '../../globalurl';

// Types for venue API
export interface Venue {
  _id: string;
  id: string;
  name: string;
  description: string;
  location: string;
  capacity: number;
  seats: number;
  price: number;
  amenities: string[];
  images: string[];
  image: string; // Primary image URL
  venueType: string;
  rating: number;
  availability: boolean;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface VenueBookingFormData {
  name: string;
  email: string;
  phone: string;
  eventDate: string;
  eventType: string;
  guestCount: number;
  venueId: string;
  message?: string;
}

export interface VenueQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  venueType?: string;
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  minCapacity?: number;
  maxCapacity?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface VenuesResponse {
  success: boolean;
  message: string;
  data: {
    venues: Venue[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface VenueResponse {
  success: boolean;
  message: string;
  data?: Venue;
}

export interface VenueBookingResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    name: string;
    email: string;
    phone: string;
    eventDate: string;
    eventType: string;
    guestCount: number;
    venueId: string;
    message?: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  };
}

// Venue API functions
export const venueApi = {
  // Get all venues with pagination and filters
  getVenues: async (params: VenueQueryParams = {}): Promise<VenuesResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<VenuesResponse>(
      buildApiUrl(`/venues?${queryParams.toString()}`)
    );
    return response.data;
  },

  // Get single venue by ID
  getVenueById: async (id: string): Promise<VenueResponse> => {
    const response = await apiClient.get<VenueResponse>(
      buildApiUrl(`/venues/${id}`)
    );
    return response.data;
  },

  // Submit venue booking
  submitBooking: async (bookingData: VenueBookingFormData): Promise<VenueBookingResponse> => {
    try {
      const response = await apiClient.post<VenueBookingResponse>(
        buildUrl('/venue-bookings'),
        bookingData
      );
      return response.data;
    } catch (error: any) {
      // Handle API errors
      if (error.response?.data) {
        throw error.response.data;
      }
      throw {
        success: false,
        message: 'Failed to submit venue booking. Please try again.',
      };
    }
  },

  // Get venue types
  getVenueTypes: async (): Promise<string[]> => {
    const response = await apiClient.get<{ types: string[] }>(
      buildUrl('/venues/types')
    );
    return response.data.types;
  },

  // Get venue locations
  getVenueLocations: async (): Promise<string[]> => {
    const response = await apiClient.get<{ locations: string[] }>(
      buildUrl('/venues/locations')
    );
    return response.data.locations;
  },
};

// Helper functions
export const venueHelpers = {
  // Format venue type for display
  formatVenueType: (type: string): string => {
    return type
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  },

  // Format price for display
  formatPrice: (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  },

  // Calculate total price with taxes
  calculateTotalPrice: (basePrice: number, guestCount: number): number => {
    const totalBase = basePrice * guestCount;
    const tax = totalBase * 0.18; // 18% GST
    return totalBase + tax;
  },

  // Validate booking form
  validateBooking: (data: VenueBookingFormData): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.name.trim()) errors.push('Name is required');
    if (!data.email.trim()) errors.push('Email is required');
    if (!data.phone.trim()) errors.push('Phone is required');
    if (!data.eventDate) errors.push('Event date is required');
    if (!data.eventType.trim()) errors.push('Event type is required');
    if (data.guestCount <= 0) errors.push('Guest count must be greater than 0');
    if (!data.venueId.trim()) errors.push('Venue selection is required');

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (data.email && !emailRegex.test(data.email)) {
      errors.push('Please enter a valid email address');
    }

    // Phone validation
    const phoneRegex = /^[0-9]{10}$/;
    if (data.phone && !phoneRegex.test(data.phone.replace(/\D/g, ''))) {
      errors.push('Please enter a valid 10-digit phone number');
    }

    // Date validation (should be in future)
    const eventDate = new Date(data.eventDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (eventDate < today) {
      errors.push('Event date must be in the future');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};

// Export individual functions for easier imports
export const getVenues = venueApi.getVenues;
export const getVenueById = venueApi.getVenueById;
export const submitVenueBooking = venueApi.submitBooking;
export const getVenueTypes = venueApi.getVenueTypes;
export const getVenueLocations = venueApi.getVenueLocations;

// Validation function export
export const validateVenueBooking = venueHelpers.validateBooking;
