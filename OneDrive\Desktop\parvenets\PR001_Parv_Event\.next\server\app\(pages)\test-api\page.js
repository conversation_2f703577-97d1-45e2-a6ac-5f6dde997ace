(()=>{var e={};e.id=242,e.ids=[242],e.modules={707:(e,t,s)=>{Promise.resolve().then(s.bind(s,82633))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13707:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(60687),a=s(43210),n=s(34115),i=s(684);let o={testConnection:async()=>{try{console.log("\uD83D\uDD0D Testing connection to:",i.JW.BASE_URL);let e=await n.u.get((0,i.c$)("/health"));return console.log("✅ Connection test successful:",e.data),e.data}catch(e){return console.error("❌ Connection test failed:",e),{success:!1,message:e.message||"Connection failed",timestamp:new Date().toISOString(),data:{error:e.code,url:e.config?.url,status:e.response?.status}}}},testApiEndpoint:async()=>{try{console.log("\uD83D\uDD0D Testing API endpoint:",(0,i.KB)("/test"));let e=await n.u.get((0,i.KB)("/test"));return console.log("✅ API test successful:",e.data),e.data}catch(e){return console.error("❌ API test failed:",e),{success:!1,message:e.message||"API test failed",timestamp:new Date().toISOString(),data:{error:e.code,url:e.config?.url,status:e.response?.status}}}},testMultipleEndpoints:async()=>{let e=[{name:"Health Check",url:(0,i.c$)("/health")},{name:"API Health",url:(0,i.KB)("/health")},{name:"API Test",url:(0,i.KB)("/test")},{name:"Events",url:(0,i.KB)("/events")},{name:"Venues",url:(0,i.c$)("/venues")},{name:"Blogs",url:(0,i.c$)("/blogs")}],t=[];for(let s of e)try{console.log(`🔍 Testing ${s.name}:`,s.url);let e=await n.u.get(s.url);t.push({success:!0,message:`${s.name} - Success`,timestamp:new Date().toISOString(),data:{url:s.url,status:e.status,responseSize:JSON.stringify(e.data).length}}),console.log(`✅ ${s.name} successful`)}catch(e){t.push({success:!1,message:`${s.name} - Failed: ${e.message}`,timestamp:new Date().toISOString(),data:{url:s.url,error:e.code,status:e.response?.status}}),console.log(`❌ ${s.name} failed:`,e.message)}return t}};function l(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!1),[i,l]=(0,a.useState)(null),d=async()=>{n(!0);try{let e=await o.testConnection();t(t=>[...t,{type:"Connection Test",...e}])}catch(e){console.error("Test failed:",e)}n(!1)},c=async()=>{n(!0);try{let e=await o.testApiEndpoint();t(t=>[...t,{type:"API Test",...e}])}catch(e){console.error("API test failed:",e)}n(!1)},p=async()=>{n(!0);try{let e=await o.testMultipleEndpoints();t(t=>[...t,...e.map(e=>({type:"Multiple Tests",...e}))])}catch(e){console.error("Multiple tests failed:",e)}n(!1)};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"API Network Test"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Configuration Debug"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-x-auto",children:JSON.stringify(i,null,2)})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Network Tests"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("button",{onClick:d,disabled:s,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50",children:"Test Connection"}),(0,r.jsx)("button",{onClick:c,disabled:s,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50",children:"Test API Endpoint"}),(0,r.jsx)("button",{onClick:p,disabled:s,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50",children:"Test Multiple Endpoints"}),(0,r.jsx)("button",{onClick:()=>{t([])},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded",children:"Clear Results"})]}),s&&(0,r.jsx)("div",{className:"mt-4 text-blue-600",children:"Running tests..."})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),0===e.length?(0,r.jsx)("p",{className:"text-gray-500",children:"No test results yet. Run a test to see results."}):(0,r.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,r.jsxs)("div",{className:`p-4 rounded border-l-4 ${e.success?"border-green-500 bg-green-50":"border-red-500 bg-red-50"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("span",{className:"font-medium",children:e.type}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm ${e.success?"bg-green-200 text-green-800":"bg-red-200 text-red-800"}`,children:e.success?"SUCCESS":"FAILED"})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:e.message}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:e.timestamp}),e.data&&(0,r.jsxs)("details",{className:"mt-2",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-sm text-gray-600",children:"View Details"}),(0,r.jsx)("pre",{className:"mt-2 bg-gray-100 p-2 rounded text-xs overflow-x-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})]})]})})}},15262:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["(pages)",{children:["test-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82633)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\test-api\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,48754)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\test-api\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(pages)/test-api/page",pathname:"/test-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66363:(e,t,s)=>{Promise.resolve().then(s.bind(s,13707))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82633:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\(pages)\\\\test-api\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\test-api\\page.tsx","default")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,814,950,443],()=>s(15262));module.exports=r})();