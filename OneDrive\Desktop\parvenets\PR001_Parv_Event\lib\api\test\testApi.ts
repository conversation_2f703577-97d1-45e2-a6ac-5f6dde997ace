// Test API module for debugging network issues
import { apiClient } from '../../customaxios';
import { buildApiUrl, buildUrl, GLOBAL_CONFIG } from '../../globalurl';

export interface TestResponse {
  success: boolean;
  message: string;
  timestamp: string;
  data?: any;
}

// Test API functions
export const testApi = {
  // Test basic connectivity
  testConnection: async (): Promise<TestResponse> => {
    try {
      console.log('🔍 Testing connection to:', GLOBAL_CONFIG.BASE_URL);
      
      // Try a simple GET request to check if server is reachable
      const response = await apiClient.get<TestResponse>(
        buildUrl('/health') // Most APIs have a health endpoint
      );
      
      console.log('✅ Connection test successful:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ Connection test failed:', error);
      
      // Return a structured error response
      return {
        success: false,
        message: error.message || 'Connection failed',
        timestamp: new Date().toISOString(),
        data: {
          error: error.code,
          url: error.config?.url,
          status: error.response?.status,
        }
      };
    }
  },

  // Test API endpoint
  testApiEndpoint: async (): Promise<TestResponse> => {
    try {
      console.log('🔍 Testing API endpoint:', buildApiUrl('/test'));
      
      const response = await apiClient.get<TestResponse>(
        buildApiUrl('/test')
      );
      
      console.log('✅ API test successful:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ API test failed:', error);
      
      return {
        success: false,
        message: error.message || 'API test failed',
        timestamp: new Date().toISOString(),
        data: {
          error: error.code,
          url: error.config?.url,
          status: error.response?.status,
        }
      };
    }
  },

  // Test with different endpoints to find working ones
  testMultipleEndpoints: async (): Promise<TestResponse[]> => {
    const endpoints = [
      { name: 'Health Check', url: buildUrl('/health') },
      { name: 'API Health', url: buildApiUrl('/health') },
      { name: 'API Test', url: buildApiUrl('/test') },
      { name: 'Events', url: buildApiUrl('/events') },
      { name: 'Venues', url: buildUrl('/venues') },
      { name: 'Blogs', url: buildUrl('/blogs') },
    ];

    const results: TestResponse[] = [];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 Testing ${endpoint.name}:`, endpoint.url);
        
        const response = await apiClient.get(endpoint.url);
        
        results.push({
          success: true,
          message: `${endpoint.name} - Success`,
          timestamp: new Date().toISOString(),
          data: {
            url: endpoint.url,
            status: response.status,
            responseSize: JSON.stringify(response.data).length
          }
        });
        
        console.log(`✅ ${endpoint.name} successful`);
      } catch (error: any) {
        results.push({
          success: false,
          message: `${endpoint.name} - Failed: ${error.message}`,
          timestamp: new Date().toISOString(),
          data: {
            url: endpoint.url,
            error: error.code,
            status: error.response?.status,
          }
        });
        
        console.log(`❌ ${endpoint.name} failed:`, error.message);
      }
    }

    return results;
  },

  // Debug configuration
  debugConfig: (): any => {
    return {
      baseUrl: GLOBAL_CONFIG.BASE_URL,
      apiVersion: GLOBAL_CONFIG.API_VERSION,
      timeout: GLOBAL_CONFIG.TIMEOUT,
      sampleUrls: {
        buildUrl: buildUrl('/test'),
        buildApiUrl: buildApiUrl('/test'),
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        nextPublicBaseUrl: process.env.NEXT_PUBLIC_BASE_URL,
      }
    };
  }
};
