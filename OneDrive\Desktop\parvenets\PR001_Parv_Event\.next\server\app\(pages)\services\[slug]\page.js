(()=>{var e={};e.id=97,e.ids=[97],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8562:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(60687),a=r(43210),i=r(56947),l=r(30474),n=r(13858),o=r(93853);let d=({params:e,searchParams:t})=>{let[r,d]=(0,a.useState)(null),[c,x]=(0,a.useState)(!0),[m,g]=(0,a.useState)(null);if(!e||!e.slug)return(0,s.jsx)("div",{className:"py-20 text-center",children:(0,s.jsx)("p",{children:"Invalid service URL."})});let p=async()=>{try{x(!0),g(null);let t=await n.s7.getServiceById(e.slug);if(t.success&&t.data)d(t.data);else throw Error("Service not found")}catch(e){console.error("Error fetching service:",e),g(e.message||"Failed to load service"),o.oR.error("Failed to load service details.")}finally{x(!1)}};return((0,a.useEffect)(()=>{p()},[e.slug]),c)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{title:"LOADING...",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICES",href:"/services"}]}),(0,s.jsxs)("div",{className:"py-20 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FE904B] mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading service details..."})]})]}):m||!r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{title:"SERVICE NOT FOUND",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICES",href:"/services"}]}),(0,s.jsxs)("div",{className:"py-20 text-center",children:[(0,s.jsxs)("div",{className:"text-red-500 mb-4",children:[(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Service not found"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:m})]}),(0,s.jsx)("button",{onClick:p,className:"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors mr-4",children:"Try Again"}),(0,s.jsx)("a",{href:"/services",className:"px-4 py-2 border border-[#FE904B] text-[#FE904B] rounded-md hover:bg-[#FE904B] hover:text-white transition-colors",children:"Back to Services"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{title:"SERVICE DETAILS",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICE DETAILS",href:"/services"}]}),(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-white py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,s.jsx)("h1",{className:"text-xs sm:text-sm text-[#BC7B77] uppercase tracking-wider mb-3 sm:mb-4 font-medium",children:"LATEST SERVICE"}),(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 uppercase text-gray-900 leading-tight",children:r.title}),(0,s.jsxs)("div",{className:"max-w-[68rem] mx-auto space-y-4 sm:space-y-6",children:[(0,s.jsx)("p",{className:"text-gray-700 text-sm sm:text-base lg:text-lg leading-relaxed text-center sm:text-left px-4 sm:px-0",children:r.description}),r.description2&&(0,s.jsx)("p",{className:"text-gray-600 text-xs sm:text-sm lg:text-base leading-relaxed text-center sm:text-left px-4 sm:px-0",children:r.description2})]})]})}),(0,s.jsx)("div",{className:"py-6 sm:py-8 lg:py-12",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"relative h-[250px] sm:h-[350px] md:h-[450px] lg:h-[500px] xl:h-[550px] rounded-xl sm:rounded-2xl overflow-hidden shadow-xl",children:[(0,s.jsx)(l.default,{src:r.image,alt:r.title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-700"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})})}),(0,s.jsx)("div",{className:"py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-orange-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("div",{className:"text-center mb-12 sm:mb-16",children:(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-gray-900 mb-4 sm:mb-6 uppercase tracking-wide",children:"How We Do It"})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10",children:r.howWeDoIt&&r.howWeDoIt.length>0?r.howWeDoIt.slice(0,3).map((e,t)=>(0,s.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 border ",style:{borderColor:"rgba(229, 218, 207, 1)"},children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl  text-center  font-semibold text-gray-900 leading-tight ",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-center text-sm sm:text-base leading-relaxed",children:e.description})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 "})]},t)):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 ",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ",children:"Professional Planning"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm sm:text-base leading-relaxed",children:"Our experienced team creates detailed plans tailored to your specific needs and vision."})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})]}),(0,s.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-orange-200 hover:-translate-y-2",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ",children:"Quality Execution"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm sm:text-base leading-relaxed",children:"We ensure every detail is executed flawlessly with attention to quality and precision."})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})]}),(0,s.jsxs)("div",{className:"group relative bg-white rounded-2xl sm:rounded-3xl p-6 sm:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 ",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 leading-tight ",children:"Memorable Experience"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm sm:text-base leading-relaxed",children:"Creating unforgettable moments that exceed your expectations and delight your guests."})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 rounded-b-2xl sm:rounded-b-3xl transform scale-x-0 "})]})]})})]})})]})}},10013:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s={src:"/_next/static/media/pinktree.b502aac7.png",height:195,width:256,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAKlBMVEVMaXH/q9bdorPZoK7YoK/RlafMnaeyg43Ll6LCjprTnavgq7jWobB/XV3u3GU9AAAADnRSTlMAAjtgjSgXUCVqL0x4Hph5UpkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAQAwDMMwp3+PP91JgCQsgBqOJ4VviwKb2ysRHuvtFLS3CfgR1AC3VmqfqgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11042:(e,t,r)=>{Promise.resolve().then(r.bind(r,8562))},12412:e=>{"use strict";e.exports=require("assert")},13858:(e,t,r)=>{"use strict";r.d(t,{_Z:()=>n,dU:()=>o,jE:()=>l,s7:()=>i});var s=r(34115),a=r(684);let i={getServices:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())}),(await s.u.get((0,a.c$)(`/services?${t.toString()}`))).data},getServiceById:async e=>(await s.u.get((0,a.c$)(`/services/${e}`))).data,getServiceBySlug:async e=>(await s.u.get((0,a.c$)(`/services/slug/${e}`))).data,getServiceCategories:async()=>(await s.u.get((0,a.c$)("/services/categories"))).data.categories},l={getGallery:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&(Array.isArray(r)?r.forEach(r=>t.append(e,r.toString())):t.append(e,r.toString()))}),(await s.u.get((0,a.c$)(`/gallery?${t.toString()}`))).data},getGalleryItems:async(e={})=>l.getGallery(e),getGalleryCategories:async()=>(await s.u.get((0,a.c$)("/gallery/categories"))).data.categories,getGalleryByCategory:async(e,t={})=>{let r=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,t.toString())}),(await s.u.get((0,a.c$)(`/gallery/category/${e}?${r.toString()}`))).data}},n={getReviews:async(e={})=>{let t=new URLSearchParams;return Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())}),(await s.u.get((0,a.c$)(`/reviews?${t.toString()}`))).data},getServiceReviews:async(e,t={})=>{let r=new URLSearchParams;return Object.entries(t).forEach(([e,t])=>{null!=t&&r.append(e,t.toString())}),(await s.u.get((0,a.c$)(`/reviews/service/${e}?${r.toString()}`))).data}},o={formatPrice:e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(e),createSlug:e=>e.toLowerCase().replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),truncateText:(e,t)=>e.length<=t?e:e.substring(0,t).trim()+"...",formatRating:e=>e.toFixed(1),getStarRating:e=>{let t=Math.floor(e),r=e%1>=.5;return{filled:t,half:r,empty:5-t-!!r}},formatRelationship:e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),formatCardTitle:e=>e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "),formatCardDescription:(e,t=150)=>e?e.length<=t?e:e.substring(0,t).trim()+"...":"",formatGalleryCategory:e=>e?e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):"All",formatGalleryTitle:e=>e?e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):""};i.getServices,i.getServiceById,i.getServiceBySlug,i.getServiceCategories,l.getGallery,l.getGalleryCategories,l.getGalleryByCategory,n.getReviews,n.getServiceReviews},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41870:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["(pages)",{children:["services",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81150)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\services\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,48754)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\services\\[slug]\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(pages)/services/[slug]/page",pathname:"/services/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56947:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(60687),a=r(43210),i=r.n(a),l=r(30474),n=r(85814),o=r.n(n),d=r(10013);let c=({title:e,breadcrumbs:t})=>(0,s.jsx)("div",{className:"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0",children:e}),(0,s.jsx)("div",{className:"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10",children:(0,s.jsx)(l.default,{src:d.default,alt:"Cherry Blossom",width:130,height:100,style:{width:"auto",height:"auto"},className:"object-contain"})}),(0,s.jsx)("div",{className:"flex items-center space-x-2 text-sm z-20",children:t.map((e,r)=>(0,s.jsxs)(i().Fragment,{children:[(0,s.jsx)(o(),{href:e.href,className:"hover:text-[#FE904B] transition-colors",children:e.label}),r<t.length-1&&(0,s.jsx)("span",{className:"text-gray-400",children:"›"})]},r))})]})})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70874:(e,t,r)=>{Promise.resolve().then(r.bind(r,71220))},71220:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\parvenets\\\\PR001_Parv_Event\\\\app\\\\(pages)\\\\services\\\\[slug]\\\\service-detail-client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\parvenets\\PR001_Parv_Event\\app\\(pages)\\services\\[slug]\\service-detail-client.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81150:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,generateMetadata:()=>l});var s=r(37413),a=r(61120),i=r(71220);async function l({params:e}){let t=await Promise.resolve(e);return{title:`Service: ${t.slug}`}}async function n({params:e,searchParams:t}){let r=await Promise.resolve(e),l=await Promise.resolve(t);return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(i.default,{params:r,searchParams:l})})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,814,950,443],()=>r(41870));module.exports=s})();