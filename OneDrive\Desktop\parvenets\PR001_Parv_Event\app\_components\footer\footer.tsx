'use client';
import React from "react";
import Image from "next/image";
import Link from "next/link";
import logo from "@/public/image/logo.png";
import bottomflower from "@/public/image/assests/downborderflower.png";
import { Instagram, Facebook, Twitter } from "lucide-react";

const Footer = () => {
  return (
    <footer className="relative bg-[#F6E4DB] py-12 px-4 sm:px-6 md:px-8 overflow-hidden">
      {/* Background flower decoration */}
      <div className="absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none">
        <Image 
          src={bottomflower} 
          alt="Decorative flower" 
          className="absolute top-0 right-0 w-auto h-full object-contain object-right-top"
          priority
        />
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Column 1: Logo and Description */}
          <div className="space-y-4">
            <Image src={logo} alt="Parv Events Logo" width={60} height={60} />
            
            <p className="text-sm text-gray-700 max-w-xs mt-4">
              From intimate gatherings to grand celebrations, our experienced wedding planners ensure you create unforgettable memories. We handle every detail so you can enjoy every moment.
            </p>
            
            <div className="pt-2">
              <p className="text-sm text-gray-700">+91-9735284928</p>
              <p className="text-sm text-gray-700"><EMAIL></p>
            </div>
            
            {/* Social Media Icons */}
            <div className="flex gap-3 pt-2">
              <Link href="#" className="w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors">
                <Instagram size={18} />
              </Link>
              <Link href="#" className="w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors">
                <Facebook size={18} />
              </Link>
              <Link href="#" className="w-9 h-9 rounded-full border border-[#FE904B] flex items-center justify-center text-[#FE904B] hover:bg-[#FE904B] hover:text-white transition-colors">
                <Twitter size={18} />
              </Link>
            </div>
          </div>
          
          {/* Column 2: Quick Links */}
          <div className="lg:ml-auto">
            <h3 className="text-base font-medium mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Home</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">About Us</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Venue</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Service</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Gallery</Link></li>
            </ul>
          </div>
          
          {/* Column 3: Service new idss*/}
          <div>
            <h3 className="text-base font-medium mb-4">Service</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Full-Service Planning</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Partial Planning</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Day-of Coordination</Link></li>
              <li><Link href="#" className="text-sm text-gray-700 hover:text-[#FE904B]">Destination Weddings</Link></li>
            </ul>
          </div>
          
          {/* Column 4: Empty space to balance layout */}
          <div className="hidden lg:block"></div>
        </div>
        
        {/* Bottom Section with Copyright and Policy Links */}
        <div className="md:w-[50%]  border-t border-gray-200 mt-10 pt-6 flex flex-col sm:flex-row justify-between items-center">
          <div className="flex gap-4 mb-4 sm:mb-0">
            <Link href="#" className="text-xs text-gray-700 hover:text-[#FE904B]">Privacy policy</Link>
            <Link href="#" className="text-xs text-gray-700 hover:text-[#FE904B]">Terms and Condition</Link>
          </div>
          <div className="text-xs text-gray-700">
            Copyright 2024
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
