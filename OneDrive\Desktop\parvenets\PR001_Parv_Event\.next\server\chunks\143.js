"use strict";exports.id=143,exports.ids=[143],exports.modules={72837:(e,t,a)=>{a.d(t,{P1:()=>m,_n:()=>c,dZ:()=>i,f_:()=>n,tz:()=>d,z$:()=>l});var s=a(34115),r=a(684);let o={getBlogs:async(e={})=>{let t=new URLSearchParams;e.page&&t.append("page",e.page.toString()),e.pageSize&&t.append("pageSize",e.pageSize.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),e.category&&t.append("category",e.category),e.search&&t.append("search",e.search),e.keywords&&t.append("keywords",e.keywords);let a=`/blogs${t.toString()?`?${t.toString()}`:""}`;return(await s.u.get((0,r.c$)(a))).data},getBlogById:async e=>(await s.u.get((0,r.c$)(`/blogs/${e}`))).data,getBlogsByCategory:async(e,t={})=>{let a=new URLSearchParams;t.page&&a.append("page",t.page.toString()),t.pageSize&&a.append("pageSize",t.pageSize.toString()),t.sortBy&&a.append("sortBy",t.sortBy),t.sortOrder&&a.append("sortOrder",t.sortOrder),t.search&&a.append("search",t.search),t.keywords&&a.append("keywords",t.keywords);let o=`/blogs/category/${e}${a.toString()?`?${a.toString()}`:""}`;return(await s.u.get((0,r.c$)(o))).data},createBlog:async e=>{let t=new FormData;return t.append("title",e.title),t.append("description",e.description),Array.isArray(e.category)?t.append("category",e.category.join(",")):t.append("category",e.category),e.keywords&&(Array.isArray(e.keywords)?t.append("keywords",e.keywords.join(",")):t.append("keywords",e.keywords)),e.image&&t.append("image",e.image),(await s.u.post((0,r.c$)("/blogs"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},updateBlog:async e=>{let{id:t,...a}=e,o=new FormData;return a.title&&o.append("title",a.title),a.description&&o.append("description",a.description),a.category&&(Array.isArray(a.category)?o.append("category",a.category.join(",")):o.append("category",a.category)),a.keywords&&(Array.isArray(a.keywords)?o.append("keywords",a.keywords.join(",")):o.append("keywords",a.keywords)),a.image&&o.append("image",a.image),(await s.u.put((0,r.c$)(`/blogs/${t}`),o,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteBlog:async e=>(await s.u.delete((0,r.c$)(`/blogs/${e}`))).data,searchBlogs:async(e,t={})=>o.getBlogs({...t,search:e}),getPopularBlogs:async(e=5)=>o.getBlogs({pageSize:e,sortBy:"views",sortOrder:"desc"}),getRecentBlogs:async(e=5)=>o.getBlogs({pageSize:e,sortBy:"createdAt",sortOrder:"desc"})},n=async e=>o.getBlogs({page:e.page,pageSize:e.pageSize||e.limit,search:e.search,category:e.category,keywords:e.keywords}),d=async()=>{try{let e=await o.getBlogs({pageSize:1e3});if(!e.status||!e.data||!e.data.data)return{status:!0,data:[]};let t={};return e.data.data.forEach(e=>{e.category&&Array.isArray(e.category)&&e.category.forEach(e=>{e&&"string"==typeof e&&(t[e]=(t[e]||0)+1)})}),{status:!0,data:Object.entries(t).map(([e,t])=>({name:e,count:t}))}}catch(e){return console.error("Error getting categories from blogs:",e),{status:!0,data:[]}}},l=async(e=5)=>o.getRecentBlogs(e),i=async e=>o.getBlogById(e),c=e=>{try{if(!e)return"No Date";if(e.includes("/")&&e.includes(","))return e.split(",")[0];let t=new Date(e);if(isNaN(t.getTime()))return"Invalid Date";let a=t.getDate().toString().padStart(2,"0"),s=(t.getMonth()+1).toString().padStart(2,"0"),r=t.getFullYear();return`${a}/${s}/${r}`}catch(e){return console.error("Error formatting date:",e),"Invalid Date"}},m=(e,t,a)=>{let s=encodeURIComponent(t),r=encodeURIComponent(a);return{facebook:`https://www.facebook.com/sharer/sharer.php?u=${r}`,twitter:`https://twitter.com/intent/tweet?text=${s}&url=${r}`,linkedin:`https://www.linkedin.com/sharing/share-offsite/?url=${r}`,whatsapp:`https://wa.me/?text=${s} - ${r}`,telegram:`https://t.me/share/url?url=${r}&text=${s}`,reddit:`https://reddit.com/submit?url=${r}&title=${s}`,pinterest:`https://pinterest.com/pin/create/button/?url=${r}&description=${s}`}}},81244:(e,t,a)=>{a.d(t,{A:()=>y});var s=a(60687),r=a(43210),o=a(69587),n=a(17019),d=a(69101),l=a(11860),i=a(96241);function c({...e}){return(0,s.jsx)(d.bL,{"data-slot":"dialog",...e})}function m({...e}){return(0,s.jsx)(d.ZL,{"data-slot":"dialog-portal",...e})}function g({className:e,...t}){return(0,s.jsx)(d.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function p({className:e,children:t,showCloseButton:a=!0,...r}){return(0,s.jsxs)(m,{"data-slot":"dialog-portal",children:[(0,s.jsx)(g,{}),(0,s.jsxs)(d.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,a&&(0,s.jsxs)(d.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,s.jsx)(d.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}var u=a(72837);function y({isOpen:e,onClose:t,blogId:a,blogTitle:d,blogUrl:l}){let[i,m]=(0,r.useState)(!1),g=l||"",y=(0,u.P1)(a,d,g),b=async()=>{try{await navigator.clipboard.writeText(g),m(!0),setTimeout(()=>m(!1),2e3)}catch(e){console.error("Failed to copy link:",e)}},w=e=>{let t="";switch(e){case"facebook":t=y.facebook;break;case"twitter":t=y.twitter;break;case"linkedin":t=y.linkedin;break;case"whatsapp":t=`https://wa.me/?text=${encodeURIComponent(`${d} - ${g}`)}`;break;case"instagram":b();return}t&&window.open(t,"_blank","width=600,height=400")};return(0,s.jsx)(c,{open:e,onOpenChange:t,children:(0,s.jsxs)(p,{className:"bg-white border-gray-200 max-w-xs sm:max-w-md",children:[(0,s.jsx)(x,{children:(0,s.jsxs)(h,{className:"text-gray-900 text-lg sm:text-xl font-bold flex items-center gap-2",children:[(0,s.jsx)(n.Pum,{className:"w-4 h-4 sm:w-5 sm:h-5"}),"Share this article"]})}),(0,s.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-2 sm:p-3",children:(0,s.jsx)("p",{className:"text-gray-700 text-xs sm:text-sm line-clamp-2",children:d})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 sm:gap-3",children:[(0,s.jsxs)("button",{onClick:()=>w("facebook"),className:"flex items-center gap-2 p-2 sm:p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.iYk,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Facebook"})]}),(0,s.jsxs)("button",{onClick:()=>w("twitter"),className:"flex items-center gap-2 p-2 sm:p-3 bg-sky-500 hover:bg-sky-600 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.feZ,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Twitter"})]}),(0,s.jsxs)("button",{onClick:()=>w("linkedin"),className:"flex items-center gap-2 p-2 sm:p-3 bg-blue-700 hover:bg-blue-800 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.QEs,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"LinkedIn"})]}),(0,s.jsxs)("button",{onClick:()=>w("whatsapp"),className:"flex items-center gap-2 p-2 sm:p-3 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.EcP,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"WhatsApp"})]}),(0,s.jsxs)("button",{onClick:()=>w("instagram"),className:"flex items-center gap-2 p-2 sm:p-3 bg-pink-600 hover:bg-pink-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:[(0,s.jsx)(o.ao$,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Instagram"})]}),(0,s.jsx)("button",{onClick:b,className:"flex items-center gap-2 p-2 sm:p-3 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors text-white text-xs sm:text-sm",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.YrT,{className:"w-4 h-4 sm:w-5 sm:h-5 text-green-400"}),(0,s.jsx)("span",{className:"font-medium text-green-400",children:"Copied!"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.paH,{className:"w-4 h-4 sm:w-5 sm:h-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Copy Link"})]})})]}),(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-2 sm:p-3",children:[(0,s.jsx)("p",{className:"text-gray-600 text-xs mb-1",children:"Link:"}),(0,s.jsx)("p",{className:"text-gray-700 text-xs sm:text-sm break-all",children:g})]})]})]})})}},83230:(e,t,a)=>{a.d(t,{A:()=>m});var s=a(60687),r=a(43210),o=a(30474),n=a(85814),d=a.n(n),l=a(16189),i=a(17257),c=a(72837);function m({categories:e,recentBlogs:t,allKeywords:a,searchQuery:n,selectedCategory:m,onSearchChange:g,onSearchSubmit:p,onCategoryFilter:x,onKeywordClick:h}){let u=(0,l.useRouter)(),[y,b]=(0,r.useState)(!1),[w,f]=(0,r.useState)(!1),[j,N]=(0,r.useState)(!1),k=(e,t)=>{let a=t.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");u.push(`/blog/${e}/${a}`)};return(0,s.jsxs)("div",{className:"w-full lg:w-[350px] flex flex-col gap-6 md:gap-8",children:[(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Search"}),(0,s.jsxs)("form",{onSubmit:p,className:"relative",children:[(0,s.jsx)("input",{type:"text",value:n,onChange:e=>g(e.target.value),placeholder:"Search blogs...",className:"w-full bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 md:py-3 pr-10 md:pr-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-orange-500 text-sm md:text-base"}),(0,s.jsx)("button",{type:"submit",className:"absolute right-2 md:right-3 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-orange-500 transition-colors",children:(0,s.jsx)(i.YQq,{className:"w-4 h-4 md:w-5 md:h-5"})})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Category"}),(0,s.jsxs)("div",{className:"space-y-2 md:space-y-3",children:[e.slice(0,y?e.length:j?3:5).map((e,t)=>(0,s.jsxs)("div",{onClick:()=>x(e.name),className:`flex justify-between items-center p-2 md:p-3 rounded-lg cursor-pointer transition-colors ${m===e.name?"bg-orange-500 text-white":"bg-gray-50 text-gray-700 hover:bg-gray-100"}`,children:[(0,s.jsx)("span",{className:"font-medium text-sm md:text-base",children:e.name}),(0,s.jsxs)("span",{className:"text-xs md:text-sm",children:["(",e.count,")"]})]},t)),e.length>(j?3:5)&&(0,s.jsx)("button",{onClick:()=>b(!y),className:"w-full text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium py-1 md:py-2 transition-colors",children:y?"Show Less":"More"})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Recent News"}),(0,s.jsx)("div",{className:"space-y-3 md:space-y-4",children:t.map(e=>(0,s.jsxs)("div",{onClick:()=>k(e._id,e.title),className:"flex gap-2 md:gap-3 cursor-pointer group",children:[(0,s.jsx)("div",{className:"w-12 h-12 md:w-16 md:h-16 rounded-lg overflow-hidden flex-shrink-0",children:(0,s.jsx)(o.default,{src:e.imageUrl,alt:e.title,width:64,height:64,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"text-gray-900 text-xs md:text-sm font-medium line-clamp-2 group-hover:text-orange-500 transition-colors",children:e.title}),(0,s.jsx)("p",{className:"text-gray-500 text-[10px] md:text-xs mt-0.5 md:mt-1",children:(0,c._n)(e.createdAt)})]})]},e._id))})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Keywords"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 md:gap-2",children:[a.slice(0,w?void 0:j?5:10).map((e,t)=>(0,s.jsx)("span",{onClick:()=>h(e),className:"bg-gray-100 hover:bg-orange-500 text-gray-700 hover:text-white px-2 py-0.5 md:px-3 md:py-1 rounded-full text-xs md:text-sm cursor-pointer transition-colors",children:e.replace(/[\[\]"]/g,"")},t)),a.length>(j?5:10)&&(0,s.jsx)("button",{onClick:()=>f(!w),className:"text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors",children:w?"Less":"More"})]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6",children:[(0,s.jsx)("h3",{className:"text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4",children:"Any Questions?"}),(0,s.jsx)("p",{className:"text-gray-600 text-xs md:text-sm mb-3 md:mb-4",children:"Have questions about our services or need help with your project?"}),(0,s.jsx)(d(),{href:"/contact",className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-3 md:py-3 md:px-4 rounded-lg transition-colors text-center block text-sm md:text-base",children:"Let's Talk"})]})]})}},96241:(e,t,a)=>{a.d(t,{cn:()=>o});var s=a(49384),r=a(82348);function o(...e){return(0,r.QP)((0,s.$)(e))}}};