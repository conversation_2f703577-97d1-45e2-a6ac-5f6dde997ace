(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[719],{1168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s={src:"/_next/static/media/pinktree.b502aac7.png",height:195,width:256,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAMAAADJ2y/JAAAAKlBMVEVMaXH/q9bdorPZoK7YoK/RlafMnaeyg43Ll6LCjprTnavgq7jWobB/XV3u3GU9AAAADnRSTlMAAjtgjSgXUCVqL0x4Hph5UpkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAQAwDMMwp3+PP91JgCQsgBqOJ4VviwKb2ysRHuvtFLS3CfgR1AC3VmqfqgAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:6}},1506:(e,t,r)=>{"use strict";r.d(t,{_Z:()=>l,dU:()=>c,jE:()=>i,s7:()=>o});var s=r(7693),a=r(1966);let o={getServices:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,s]=e;null!=s&&t.append(r,s.toString())}),(await s.u.get((0,a.c$)("/services?".concat(t.toString())))).data},getServiceById:async e=>(await s.u.get((0,a.c$)("/services/".concat(e)))).data,getServiceBySlug:async e=>(await s.u.get((0,a.c$)("/services/slug/".concat(e)))).data,getServiceCategories:async()=>(await s.u.get((0,a.c$)("/services/categories"))).data.categories},i={getGallery:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,s]=e;null!=s&&(Array.isArray(s)?s.forEach(e=>t.append(r,e.toString())):t.append(r,s.toString()))}),(await s.u.get((0,a.c$)("/gallery?".concat(t.toString())))).data},getGalleryItems:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return i.getGallery(e)},getGalleryCategories:async()=>(await s.u.get((0,a.c$)("/gallery/categories"))).data.categories,getGalleryByCategory:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,s]=e;null!=s&&r.append(t,s.toString())}),(await s.u.get((0,a.c$)("/gallery/category/".concat(e,"?").concat(r.toString())))).data}},l={getReviews:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,s]=e;null!=s&&t.append(r,s.toString())}),(await s.u.get((0,a.c$)("/reviews?".concat(t.toString())))).data},getServiceReviews:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new URLSearchParams;return Object.entries(t).forEach(e=>{let[t,s]=e;null!=s&&r.append(t,s.toString())}),(await s.u.get((0,a.c$)("/reviews/service/".concat(e,"?").concat(r.toString())))).data}},c={formatPrice:e=>new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(e),createSlug:e=>e.toLowerCase().replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),truncateText:(e,t)=>e.length<=t?e:e.substring(0,t).trim()+"...",formatRating:e=>e.toFixed(1),getStarRating:e=>{let t=Math.floor(e),r=e%1>=.5;return{filled:t,half:r,empty:5-t-!!r}},formatRelationship:e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),formatCardTitle:e=>e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "),formatCardDescription:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150;return e?e.length<=t?e:e.substring(0,t).trim()+"...":""}};o.getServices,o.getServiceById,o.getServiceBySlug,o.getServiceCategories,i.getGallery,i.getGalleryCategories,i.getGalleryByCategory,l.getReviews,l.getServiceReviews},1542:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(5155),a=r(2115),o=r(7771),i=r(6766),l=r(6874),c=r.n(l),n=r(1506),d=r(8543);let g={src:"/_next/static/media/servicerighttopflower.ab643a48.png",height:203,width:426,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAMAAACEE47CAAAABlBMVEUAAABMaXFP2lwvAAAAAnRSTlMCAESlr7oAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAeSURBVHicY2BkAAFGRgYQgxGMGBgZGcHCIJqRkQEAARQAESJ8Vm0AAAAASUVORK5CYII=",blurWidth:8,blurHeight:4},m=()=>{let[e,t]=(0,a.useState)([]),[r,l]=(0,a.useState)(!0),[m,h]=(0,a.useState)(null),u=async()=>{try{l(!0),h(null);let e=await n.s7.getServices({page:1,limit:20,sortBy:"sortOrder",sortOrder:"asc"});if(e.success&&e.data.services)t(e.data.services),console.log("✅ Services loaded:",e.data.services.length,"services");else throw Error("Failed to fetch services")}catch(e){console.error("Error fetching services:",e),h(e.message||"Failed to load services"),d.oR.error("Failed to load services. Please try again later.")}finally{l(!1)}};return(0,a.useEffect)(()=>{u()},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{title:"SERVICES",breadcrumbs:[{label:"HOME",href:"/"},{label:"SERVICES",href:"/services"}]}),(0,s.jsxs)("div",{className:"relative py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"absolute top-10 right-0 -z-10 hidden lg:block opacity-30",children:(0,s.jsx)(i.default,{src:g,alt:"Decorative flower",width:300,height:250,className:"object-contain"})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12 sm:mb-16 lg:mb-20",children:[(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 font-urbanist uppercase text-gray-900 leading-tight",children:"Our Professional Services"}),(0,s.jsx)("div",{className:"w-20 sm:w-24 h-1 bg-gradient-to-r from-[#FE904B] to-orange-600 mx-auto rounded-full mb-4 sm:mb-6"})]}),r&&(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] p-4 sm:p-6 animate-pulse",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4 sm:mb-6",children:(0,s.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-gray-300 rounded-full"})}),(0,s.jsx)("div",{className:"h-4 sm:h-5 bg-gray-300 rounded mb-3"}),(0,s.jsx)("div",{className:"h-3 sm:h-4 bg-gray-300 rounded mb-2"}),(0,s.jsx)("div",{className:"h-3 sm:h-4 bg-gray-300 rounded mb-4 sm:mb-6"}),(0,s.jsx)("div",{className:"h-8 sm:h-10 bg-gray-300 rounded-lg"})]},t))}),m&&!r&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsxs)("div",{className:"text-red-500 mb-4",children:[(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Failed to load services"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:m})]}),(0,s.jsx)("button",{onClick:u,className:"px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors",children:"Try Again"})]}),!r&&!m&&(0,s.jsx)(s.Fragment,{children:0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"text-gray-500",children:[(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"No services available"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Please check back later"})]})}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:e.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden shadow-sm border-2 border-b-[#FE904D] hover:shadow-lg transition-all duration-300 text-center p-4 sm:p-6 group hover:transform hover:-translate-y-1",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4 sm:mb-6",children:(0,s.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center bg-orange-50 rounded-full group-hover:bg-orange-100 transition-colors duration-300 shadow-sm overflow-hidden",children:(0,s.jsx)("img",{src:e.icons,alt:e.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 rounded-full",onLoad:()=>{console.log("✅ Backend service icon loaded:",e.title)},onError:()=>{console.error("❌ Backend service icon failed:",e.title,e.icons)}})})}),(0,s.jsx)("h3",{className:"text-base sm:text-lg font-semibold uppercase mb-2 sm:mb-3 text-gray-800 group-hover:text-[#FE904D] transition-colors duration-300 leading-tight",children:n.dU.formatCardTitle(e.title)}),(0,s.jsx)("p",{className:"text-gray-600 text-xs sm:text-sm mb-4 sm:mb-6 line-clamp-3 leading-relaxed px-2",children:n.dU.formatCardDescription(e.description)}),(0,s.jsx)(c(),{href:"/services/".concat(e._id),children:(0,s.jsx)("div",{className:"inline-block border-2 border-[#FE904D] hover:bg-[#FE904D] hover:text-white text-[#FE904D] text-xs sm:text-sm font-medium uppercase tracking-wider py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-all duration-300 cursor-pointer group-hover:bg-[#FE904D] group-hover:text-white group-hover:shadow-md",children:"See More"})})]},e._id))})})]})]})]})}},1966:(e,t,r)=>{"use strict";r.d(t,{JW:()=>s,KB:()=>o,c$:()=>a});let s={BASE_URL:"https://parevent-new-backend.onrender.com",TIMEOUT:1e4},a=e=>{let t=s.BASE_URL,r=e.startsWith("/")?e:"/".concat(e);return"".concat(t,"/api").concat(r)},o=a},7693:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var s=r(3464),a=r(1966);let o=s.A.create({timeout:a.JW.TIMEOUT,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!1});o.interceptors.request.use(e=>{{let t=localStorage.getItem("authToken");t&&e.headers&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>(console.error("❌ Request Error:",e),Promise.reject(e))),o.interceptors.response.use(e=>e,e=>{if(e.response){let{status:t,data:r}=e.response;switch(t){case 401:console.error("❌ Unauthorized access");break;case 403:console.error("❌ Forbidden access");break;case 404:console.error("❌ Resource not found");break;case 500:console.error("❌ Server error");break;default:console.error("❌ API Error:",r)}}else if(e.request){var t,r,s;console.error("❌ Network Error - No response from server:",{message:e.message,code:e.code,config:{url:null==(t=e.config)?void 0:t.url,method:null==(r=e.config)?void 0:r.method,baseURL:null==(s=e.config)?void 0:s.baseURL}}),e.message="Network error: Unable to connect to server. Please check your internet connection."}else console.error("❌ Error:",e.message);return Promise.reject(e)});let i={get:(e,t)=>o.get(e,t),post:(e,t,r)=>o.post(e,t,r),put:(e,t,r)=>o.put(e,t,r),patch:(e,t,r)=>o.patch(e,t,r),delete:(e,t)=>o.delete(e,t)}},7771:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(5155),a=r(2115),o=r(6766),i=r(6874),l=r.n(i),c=r(1168);let n=e=>{let{title:t,breadcrumbs:r}=e;return(0,s.jsx)("div",{className:"relative bg-[#FEF2EB] py-20 px-8 sm:px-6 md:px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium uppercase tracking-wider text-[#13031F] mb-4 md:mb-0",children:t}),(0,s.jsx)("div",{className:"absolute left-1/2 bottom-0  transform -translate-x-1/2 z-10",children:(0,s.jsx)(o.default,{src:c.default,alt:"Cherry Blossom",width:130,height:100,style:{width:"auto",height:"auto"},className:"object-contain"})}),(0,s.jsx)("div",{className:"flex items-center space-x-2 text-sm z-20",children:r.map((e,t)=>(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)(l(),{href:e.href,className:"hover:text-[#FE904B] transition-colors",children:e.label}),t<r.length-1&&(0,s.jsx)("span",{className:"text-gray-400",children:"›"})]},t))})]})})}},9268:(e,t,r)=>{Promise.resolve().then(r.bind(r,1542))}},e=>{var t=t=>e(e.s=t);e.O(0,[83,854,543,441,684,358],()=>t(9268)),_N_E=e.O()}]);