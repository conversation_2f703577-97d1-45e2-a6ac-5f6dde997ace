{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport useEmblaCarousel, {\r\n  type UseEmblaCarouselType,\r\n} from \"embla-carousel-react\"\r\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\n\r\ntype CarouselApi = UseEmblaCarouselType[1]\r\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\r\ntype CarouselOptions = UseCarouselParameters[0]\r\ntype CarouselPlugin = UseCarouselParameters[1]\r\n\r\ntype CarouselProps = {\r\n  opts?: CarouselOptions\r\n  plugins?: CarouselPlugin\r\n  orientation?: \"horizontal\" | \"vertical\"\r\n  setApi?: (api: CarouselApi) => void\r\n}\r\n\r\ntype CarouselContextProps = {\r\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\r\n  api: ReturnType<typeof useEmblaCarousel>[1]\r\n  scrollPrev: () => void\r\n  scrollNext: () => void\r\n  canScrollPrev: boolean\r\n  canScrollNext: boolean\r\n} & CarouselProps\r\n\r\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\r\n\r\nfunction useCarousel() {\r\n  const context = React.useContext(CarouselContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction Carousel({\r\n  orientation = \"horizontal\",\r\n  opts,\r\n  setApi,\r\n  plugins,\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & CarouselProps) {\r\n  const [carouselRef, api] = useEmblaCarousel(\r\n    {\r\n      ...opts,\r\n      axis: orientation === \"horizontal\" ? \"x\" : \"y\",\r\n    },\r\n    plugins\r\n  )\r\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\r\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\r\n\r\n  const onSelect = React.useCallback((api: CarouselApi) => {\r\n    if (!api) return\r\n    setCanScrollPrev(api.canScrollPrev())\r\n    setCanScrollNext(api.canScrollNext())\r\n  }, [])\r\n\r\n  const scrollPrev = React.useCallback(() => {\r\n    api?.scrollPrev()\r\n  }, [api])\r\n\r\n  const scrollNext = React.useCallback(() => {\r\n    api?.scrollNext()\r\n  }, [api])\r\n\r\n  const handleKeyDown = React.useCallback(\r\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\r\n      if (event.key === \"ArrowLeft\") {\r\n        event.preventDefault()\r\n        scrollPrev()\r\n      } else if (event.key === \"ArrowRight\") {\r\n        event.preventDefault()\r\n        scrollNext()\r\n      }\r\n    },\r\n    [scrollPrev, scrollNext]\r\n  )\r\n\r\n  React.useEffect(() => {\r\n    if (!api || !setApi) return\r\n    setApi(api)\r\n  }, [api, setApi])\r\n\r\n  React.useEffect(() => {\r\n    if (!api) return\r\n    onSelect(api)\r\n    api.on(\"reInit\", onSelect)\r\n    api.on(\"select\", onSelect)\r\n\r\n    return () => {\r\n      api?.off(\"select\", onSelect)\r\n    }\r\n  }, [api, onSelect])\r\n\r\n  return (\r\n    <CarouselContext.Provider\r\n      value={{\r\n        carouselRef,\r\n        api: api,\r\n        opts,\r\n        orientation:\r\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\r\n        scrollPrev,\r\n        scrollNext,\r\n        canScrollPrev,\r\n        canScrollNext,\r\n      }}\r\n    >\r\n      <div\r\n        onKeyDownCapture={handleKeyDown}\r\n        className={cn(\"relative\", className)}\r\n        role=\"region\"\r\n        aria-roledescription=\"carousel\"\r\n        data-slot=\"carousel\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    </CarouselContext.Provider>\r\n  )\r\n}\r\n\r\nfunction CarouselContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const { carouselRef, orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      ref={carouselRef}\r\n      className=\"overflow-hidden\"\r\n      data-slot=\"carousel-content\"\r\n    >\r\n      <div\r\n        className={cn(\r\n          \"flex\",\r\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CarouselItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const { orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      role=\"group\"\r\n      aria-roledescription=\"slide\"\r\n      data-slot=\"carousel-item\"\r\n      className={cn(\r\n        \"min-w-0 shrink-0 grow-0 basis-full\",\r\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CarouselPrevious({\r\n  className,\r\n  variant = null,\r\n  size = \"icon\",\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      data-slot=\"carousel-previous\"\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute size-8 rounded-sm \",\r\n        orientation === \"horizontal\"\r\n          ? \"top-1/2 -left-12 -translate-y-1/2\"\r\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollPrev}\r\n      onClick={scrollPrev}\r\n      {...props}\r\n    >\r\n      <ArrowLeft />\r\n      <span className=\"sr-only\">Previous slide</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction CarouselNext({\r\n  className,\r\n  variant = null,\r\n  size = \"icon\",\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      data-slot=\"carousel-next\"\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute size-8 rounded-sm\",\r\n        orientation === \"horizontal\"\r\n          ? \"top-1/2 -right-12 -translate-y-1/2\"\r\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollNext}\r\n      onClick={scrollNext}\r\n      {...props}\r\n    >\r\n      <ArrowRight />\r\n      <span className=\"sr-only\">Next slide</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nexport {\r\n  type CarouselApi,\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselPrevious,\r\n  CarouselNext,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;AATA;;;;;;;AAgCA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA+B;AAEzE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAClC,IAAI,CAAC,KAAK;QACV,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACpC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrB,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,KAAK;QACV,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,8OAAC;YACC,kBAAkB;YAClB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACrB,aAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,8OAAC;QACC,MAAK;QACL,wBAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,IAAI,EACd,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+BACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,IAAI,EACd,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8BACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,kNAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/api/hero/heroApi.ts"], "sourcesContent": ["// Hero Section API module\nimport { apiClient } from '../../customaxios';\nimport { buildUrl } from '../../globalurl';\n\n// Types for hero API\nexport interface HeroSection {\n  id: string;\n  title: string;\n  subtitle: string;\n  description: string;\n  backgroundImage: string;\n  ctaText: string;\n  ctaLink: string;\n  isActive: boolean;\n  order: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface HeroSectionQueryParams {\n  page?: number;\n  limit?: number;\n  isActive?: boolean;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface HeroSectionsResponse {\n  heroSections: HeroSection[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface HeroSectionResponse {\n  success: boolean;\n  message: string;\n  data?: HeroSection;\n}\n\n// Hero API functions\nexport const heroApi = {\n  // Get all hero sections with pagination and filters\n  getHeroSections: async (params: HeroSectionQueryParams = {}): Promise<HeroSectionsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<HeroSectionsResponse>(\n      buildUrl(`/hero-section?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get active hero sections (for public display)\n  getActiveHeroSections: async (): Promise<HeroSectionsResponse> => {\n    const response = await apiClient.get<HeroSectionsResponse>(\n      buildUrl('/hero-section?activeOnly=true&sortBy=sortOrder&sortOrder=asc')\n    );\n    return response.data;\n  },\n\n  // Get single hero section by ID\n  getHeroSectionById: async (id: string): Promise<HeroSectionResponse> => {\n    const response = await apiClient.get<HeroSectionResponse>(\n      buildUrl(`/hero-section/${id}`)\n    );\n    return response.data;\n  },\n};\n\n// Helper functions\nexport const heroHelpers = {\n  // Format hero section for display\n  formatHeroSection: (heroSection: HeroSection) => {\n    return {\n      ...heroSection,\n      formattedTitle: heroSection.title.toUpperCase(),\n      shortDescription: heroSection.description.length > 150 \n        ? heroSection.description.substring(0, 150) + '...'\n        : heroSection.description,\n    };\n  },\n\n  // Get hero section background style\n  getBackgroundStyle: (imageUrl: string) => {\n    return {\n      backgroundImage: `url(${imageUrl})`,\n      backgroundSize: 'cover',\n      backgroundPosition: 'center',\n      backgroundRepeat: 'no-repeat',\n    };\n  },\n\n  // Validate hero section data\n  validateHeroSection: (data: Partial<HeroSection>): { isValid: boolean; errors: string[] } => {\n    const errors: string[] = [];\n\n    if (!data.title?.trim()) errors.push('Title is required');\n    if (!data.subtitle?.trim()) errors.push('Subtitle is required');\n    if (!data.description?.trim()) errors.push('Description is required');\n    if (!data.backgroundImage?.trim()) errors.push('Background image is required');\n    if (!data.ctaText?.trim()) errors.push('CTA text is required');\n    if (!data.ctaLink?.trim()) errors.push('CTA link is required');\n\n    // Validate URL format for CTA link\n    if (data.ctaLink && !isValidUrl(data.ctaLink)) {\n      errors.push('CTA link must be a valid URL');\n    }\n\n    // Validate image URL format\n    if (data.backgroundImage && !isValidImageUrl(data.backgroundImage)) {\n      errors.push('Background image must be a valid image URL');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  },\n};\n\n// Utility functions\nconst isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nconst isValidImageUrl = (url: string): boolean => {\n  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];\n  const lowerUrl = url.toLowerCase();\n  return imageExtensions.some(ext => lowerUrl.includes(ext)) || url.startsWith('data:image/');\n};\n\n// Export individual functions for easier imports\nexport const getHeroSections = heroApi.getHeroSections;\nexport const getActiveHeroSections = heroApi.getActiveHeroSections;\nexport const getHeroSectionById = heroApi.getHeroSectionById;\nexport const formatHeroSection = heroHelpers.formatHeroSection;\nexport const getBackgroundStyle = heroHelpers.getBackgroundStyle;\nexport const validateHeroSection = heroHelpers.validateHeroSection;\n"], "names": [], "mappings": "AAAA,0BAA0B;;;;;;;;;;;AAC1B;AACA;;;AAwCO,MAAM,UAAU;IACrB,oDAAoD;IACpD,iBAAiB,OAAO,SAAiC,CAAC,CAAC;QACzD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAEpD,OAAO,SAAS,IAAI;IACtB;IAEA,gDAAgD;IAChD,uBAAuB;QACrB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,cAAc,EAAE,IAAI;QAEhC,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,kCAAkC;IAClC,mBAAmB,CAAC;QAClB,OAAO;YACL,GAAG,WAAW;YACd,gBAAgB,YAAY,KAAK,CAAC,WAAW;YAC7C,kBAAkB,YAAY,WAAW,CAAC,MAAM,GAAG,MAC/C,YAAY,WAAW,CAAC,SAAS,CAAC,GAAG,OAAO,QAC5C,YAAY,WAAW;QAC7B;IACF;IAEA,oCAAoC;IACpC,oBAAoB,CAAC;QACnB,OAAO;YACL,iBAAiB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACnC,gBAAgB;YAChB,oBAAoB;YACpB,kBAAkB;QACpB;IACF;IAEA,6BAA6B;IAC7B,qBAAqB,CAAC;QACpB,MAAM,SAAmB,EAAE;QAE3B,IAAI,CAAC,KAAK,KAAK,EAAE,QAAQ,OAAO,IAAI,CAAC;QACrC,IAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,OAAO,IAAI,CAAC;QACxC,IAAI,CAAC,KAAK,WAAW,EAAE,QAAQ,OAAO,IAAI,CAAC;QAC3C,IAAI,CAAC,KAAK,eAAe,EAAE,QAAQ,OAAO,IAAI,CAAC;QAC/C,IAAI,CAAC,KAAK,OAAO,EAAE,QAAQ,OAAO,IAAI,CAAC;QACvC,IAAI,CAAC,KAAK,OAAO,EAAE,QAAQ,OAAO,IAAI,CAAC;QAEvC,mCAAmC;QACnC,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,KAAK,OAAO,GAAG;YAC7C,OAAO,IAAI,CAAC;QACd;QAEA,4BAA4B;QAC5B,IAAI,KAAK,eAAe,IAAI,CAAC,gBAAgB,KAAK,eAAe,GAAG;YAClE,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;AACF;AAEA,oBAAoB;AACpB,MAAM,aAAa,CAAC;IAClB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAM,kBAAkB,CAAC;IACvB,MAAM,kBAAkB;QAAC;QAAQ;QAAS;QAAQ;QAAQ;QAAS;KAAO;IAC1E,MAAM,WAAW,IAAI,WAAW;IAChC,OAAO,gBAAgB,IAAI,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC,SAAS,IAAI,UAAU,CAAC;AAC/E;AAGO,MAAM,kBAAkB,QAAQ,eAAe;AAC/C,MAAM,wBAAwB,QAAQ,qBAAqB;AAC3D,MAAM,qBAAqB,QAAQ,kBAAkB;AACrD,MAAM,oBAAoB,YAAY,iBAAiB;AACvD,MAAM,qBAAqB,YAAY,kBAAkB;AACzD,MAAM,sBAAsB,YAAY,mBAAmB", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/top_section/top_section.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselPrevious,\r\n  CarouselNext,\r\n  type CarouselApi,\r\n} from \"@/components/ui/carousel\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { heroApi, HeroSection } from '@/lib/api/hero/heroApi';\r\nimport { toast } from 'react-toastify';\r\nimport Link from \"next/link\";\r\n\r\n\r\n\r\n\r\nconst TopSection = () => {\r\n  const [api, setApi] = useState<CarouselApi | null>(null);\r\n  const [heroSections, setHeroSections] = useState<HeroSection[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Fetch hero sections from API\r\n  const fetchHeroSections = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      const response = await heroApi.getHeroSections({\r\n        activeOnly: true,\r\n        sortBy: 'sortOrder',\r\n        sortOrder: 'asc',\r\n        limit: 10\r\n      });\r\n\r\n      if (response.success && response.data.heroSections.length > 0) {\r\n        setHeroSections(response.data.heroSections);\r\n        console.log('✅ Hero sections loaded:', response.data.heroSections.length);\r\n      } else {\r\n        console.log('⚠️ No hero sections found');\r\n      }\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching hero sections:', error);\r\n      toast.error('Failed to load hero images');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Fetch hero sections on component mount\r\n  useEffect(() => {\r\n    fetchHeroSections();\r\n  }, []);\r\n\r\n  // Autoplay every 4 seconds\r\n  useEffect(() => {\r\n    if (!api) return;\r\n\r\n    const interval = setInterval(() => {\r\n      api.scrollNext();\r\n    }, 4000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [api]);\r\n\r\n  return (\r\n    <div className=\"relative w-full h-[400px] sm:h-[450px] md:h-[500px] lg:h-[550px] xl:h-[600px] 2xl:h-[650px] overflow-hidden\">\r\n      <Carousel orientation=\"horizontal\" opts={{ loop: true }} setApi={setApi}>\r\n        <CarouselContent>\r\n          {heroSections.length > 0 ? (\r\n            heroSections.map((hero) => (\r\n              <CarouselItem key={hero._id} className=\"w-full h-full\">\r\n                <img\r\n                  src={hero.image}\r\n                  alt={hero.title}\r\n                  className=\"w-full h-[600px] object-cover\"\r\n                />\r\n              </CarouselItem>\r\n            ))\r\n          ) : (\r\n            !isLoading && (\r\n              <CarouselItem className=\"w-full h-full\">\r\n                <div className=\"w-full h-[600px] bg-gray-200 flex items-center justify-center\">\r\n                  <p className=\"text-gray-500\">No hero images available</p>\r\n                </div>\r\n              </CarouselItem>\r\n            )\r\n          )}\r\n        </CarouselContent>\r\n\r\n        {/* Navigation Buttons - hidden on mobile */}\r\n        <CarouselPrevious className=\"hidden sm:flex absolute left-4 top-1/2 -translate-y-1/2 z-30 bg-[#FE904B] hover:bg-[#fc873f] text-white\" />\r\n        <CarouselNext className=\"hidden sm:flex absolute right-4 top-1/2 -translate-y-1/2 z-30 bg-[#FE904B] hover:bg-[#fc873f] text-white\" />\r\n\r\n      </Carousel>\r\n\r\n      {/* Black overlay */}\r\n      <div className=\"absolute inset-0 bg-black opacity-60 z-10\" />\r\n\r\n      {/* Overlay Text */}\r\n      <div className=\"absolute inset-0 flex flex-col items-center justify-center text-white z-20 px-4 text-center gap-y-4 sm:gap-y-5 md:gap-y-6\">\r\n        <h1 className=\"text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-[#FE904B] font-bold drop-shadow-lg font-cormorant\">\r\n          Trust in Our Expert Event Planning\r\n        </h1>\r\n        <p className=\"text-2xl sm:text-3xl md:text-4xl lg:text-4xl xl:text-4xl max-w-[100%] md:max-w-2xl font-cormorant font-semibold drop-shadow-md\">\r\n          JOIN US FOR AN UNFORGETTABLE EXPERIENCE\r\n        </p>\r\n        <p className=\"text-sm sm:text-base md:text-lg max-w-[90%] md:max-w-xl font-cormorant mb-2\">\r\n          Discover amazing moments with top professionals\r\n        </p>\r\n        <div className=\"flex flex-wrap justify-center gap-3 sm:gap-4\">\r\n          <Link href=\"/services\">\r\n            <button className=\"bg-[#FE904B] hover:bg-[#f18440] px-5 sm:px-6 py-2 text-sm sm:text-base font-urbanist rounded\">\r\n              our services\r\n            </button></Link>\r\n          <Link href={\"/contact\"}>\r\n          <button className=\"bg-transparent border border-white text-white hover:border-[#FE904B] hover:text-[#FE904B] px-5 sm:px-6 py-2 text-sm sm:text-base font-urbanist rounded\">\r\n            contact us\r\n          </button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TopSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AASA;AACA;AACA;AACA;AAdA;;;;;;;AAmBA,MAAM,aAAa;IACjB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB,IAAI;YACF,aAAa;YAEb,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAO,CAAC,eAAe,CAAC;gBAC7C,YAAY;gBACZ,QAAQ;gBACR,WAAW;gBACX,OAAO;YACT;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC7D,gBAAgB,SAAS,IAAI,CAAC,YAAY;gBAC1C,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI,CAAC,YAAY,CAAC,MAAM;YAC1E,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK;QAEV,MAAM,WAAW,YAAY;YAC3B,IAAI,UAAU;QAChB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAI;IAER,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,WAAQ;gBAAC,aAAY;gBAAa,MAAM;oBAAE,MAAM;gBAAK;gBAAG,QAAQ;;kCAC/D,8OAAC,6HAAA,CAAA,kBAAe;kCACb,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,qBAChB,8OAAC,6HAAA,CAAA,eAAY;gCAAgB,WAAU;0CACrC,cAAA,8OAAC;oCACC,KAAK,KAAK,KAAK;oCACf,KAAK,KAAK,KAAK;oCACf,WAAU;;;;;;+BAJK,KAAK,GAAG;;;;wCAS7B,CAAC,2BACC,8OAAC,6HAAA,CAAA,eAAY;4BAAC,WAAU;sCACtB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC,6HAAA,CAAA,mBAAgB;wBAAC,WAAU;;;;;;kCAC5B,8OAAC,6HAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;0BAK1B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgH;;;;;;kCAG9H,8OAAC;wBAAE,WAAU;kCAAiI;;;;;;kCAG9I,8OAAC;wBAAE,WAAU;kCAA8E;;;;;;kCAG3F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC;oCAAO,WAAU;8CAA+F;;;;;;;;;;;0CAGnH,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM;0CACZ,cAAA,8OAAC;oCAAO,WAAU;8CAAyJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrL;uCAEe", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/icons/tilearrow.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 10, height: 11, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/homeservices/components/cardcomponents.tsx"], "sourcesContent": ["import Image, { StaticImageData } from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport tiltarrow from \"@/public/image/icons/tilearrow.svg\"\r\n\r\ntype CardProps = {\r\n  number: string;\r\n  title: string;\r\n  description: string;\r\n  image: StaticImageData;\r\n};\r\n\r\nexport default function Card({ number, title, description, image }: CardProps) {\r\n  return (\r\n    <div className=\"relative w-full max-w-sm rounded-xl overflow-hidden shadow-md bg-white\">\r\n      {/* Image (No fill) */}\r\n      <div className=\"relative\">\r\n        <Image\r\n          src={image}\r\n          alt={title}\r\n          width={400}\r\n          height={350}\r\n          className=\"object-cover w-full h-[400px]\"\r\n        />\r\n\r\n        {/* Top Left Number Badge */}\r\n        <div className=\"absolute top-2 left-2 bg-white font-urbanist text-black w-8 h-8 rounded-full flex items-center justify-center text-xs font-thin shadow\">\r\n          {number}\r\n        </div>\r\n\r\n        {/* Top Right Icon */}\r\n       <Link href=\"/gallary\">\r\n       <div className=\"absolute top-2 right-2 bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center shadow\">\r\n        <Image src={tiltarrow} alt=\"Arrow\" width={30} height={31} className='p-2'/>\r\n        </div>\r\n        </Link>\r\n\r\n        {/* Bottom Overlay */}\r\n        <div className=\"absolute bottom-0 w-[95%] bg-white text-black px-3 rounded-lg text-left py-2 m-2 flex gap-y-1 flex-col items-start justify-center\">\r\n          <h3 className=\"text-sm font-semibold font-urbanist\">{title}</h3>\r\n          <p className=\"text-xs text-gray-600 font-urbanist\">{description}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AASe,SAAS,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAa;IAC3E,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,WAAU;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIJ,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACX,cAAA,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BAAC,KAAK,0SAAA,CAAA,UAAS;4BAAE,KAAI;4BAAQ,OAAO;4BAAI,QAAQ;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAKpE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;;;;;;;;;;;;;;;;;;AAK9D", "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/content1.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 800, height: 600, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAA0UlEQVR42gHGADn/ALeWdf+6ooL/w7GW/8Wwl//Er5P/08y7/7LCpP+EkmX/AJVzVv97alf/sp+K/6iCXv+VeFr/vraj/56shf9kZjX/AG9ZR/9MPTH/kHxs/5p2Vv9dNx7/gGFK/5VrRf9fOx7/AINvWv9qTDP/jGpM/6R1UP9ZOSX/QSgV/1szF/97RiP/AD48M/9CNir/Y046/7F+Uf+KTyT/ZD8m/1M3Iv+JYDr/ADEgFf5YNBf+c00q/rV4Qf6mZS3+ml4u/qNySf63hFX+0Rpr1mIDZkQAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8X,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/content%202.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 419, height: 463, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAYAAAA1BOUGAAAA3UlEQVR42g3LvU7CUABA4fsU7iaKYkwkWBta2yK3P6S00l6gKGJjowhGMf7GOBjRzcG4uPggJj7esdMZTj5x21niu1jm722L38UqP5dVbpI1UnsDUTjrvI8sPnKDz3GNl0OD87jBNDYQRztV7no2s8hgHmnkvs6BqzNLTERq1nidBDxkNvN9m0m8x3Tgs7gaInqexdd1l8dyPJ9l3I9Chq5FkUjESeDw5NdJmw0GWcD42EVFHqrjIvqtXZTnIE2trEbalVzkirZslbK+Ta4CQukQ65uEbZ/TfpNKZYV/1rZr+m2s7GoAAAAASUVORK5CYII=\", blurWidth: 7, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,iIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8Y,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/Content%203.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 419, height: 468, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAYAAAA1BOUGAAAA4klEQVR42g3OTUvCYADA8efWpVAwUyxf0jloLbaV4Fsj1CUDCWtLkhl56dCMWkUyNvCQX8AOfd3xz2/wE5u3UfL99YofRfhxRBhPeZplefFqiXCaFeY31yzjkPXvhtX6E9+rsnAVhGeq2FqN5bjNXzBjNb8luuviGBXEldkjmI5YWGe8Dwweh5fYzXMcrYQ4KCtYPQu33+LeNDitH3NxIhMMVUTmUKZcVRi3NCZtDbej8zzQ+ejLiNR+kUJRoqtISIU8HaXOj9MgtLegnb1ssps5QpWrpNJpcrk8E73AQ6OU/AO4fmPgUGqNtgAAAABJRU5ErkJggg==\", blurWidth: 7, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,iIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsZ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/homeservices/components/cargrid.tsx"], "sourcesContent": ["import Card from \"./cardcomponents\";\r\nimport img1 from \"@/public/image/assests/content1.png\";\r\nimport img2 from \"@/public/image/assests/content 2.png\";\r\nimport img3 from \"@/public/image/assests/Content 3.png\";\r\n\r\nexport default function CardGrid() {\r\n  const cards = [\r\n    {\r\n      number: \"01\",\r\n      title: \"Full‑Service Planning\",\r\n      description: \"Complete wedding planning from concept to execution.\",\r\n      image: img1,\r\n    },\r\n    {\r\n      number: \"02\",\r\n      title: \"Partial Planning\",\r\n      description: \"Assistance with specific aspects of your wedding.\",\r\n      image: img2,\r\n    },\r\n    {\r\n      number: \"03\",\r\n      title: \"Day‑of Coordination\",\r\n      description: \"Ensuring your big day runs smoothly.\",\r\n      image: img3,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 p-4\">\r\n    {cards.map((card, index) => (\r\n      <Card key={index} {...card} />\r\n    ))}\r\n  </div>\r\n  \r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,OAAO,4SAAA,CAAA,UAAI;QACb;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,OAAO,gTAAA,CAAA,UAAI;QACb;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,OAAO,gTAAA,CAAA,UAAI;QACb;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACd,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,mKAAA,CAAA,UAAI;gBAAc,GAAG,IAAI;eAAf;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/homeservices/home_service.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport CardGrid from './components/cargrid';\r\n\r\nconst HomeService = () => {\r\n  return (\r\n    <div className=\"w-full flex flex-col justify-center items-center gap-y-6 mb-8\">\r\n      {/* text container and card container */}\r\n      <div className=\"w-full px-4 sm:px-6 md:px-10 lg:px-20 flex flex-col md:flex-row justify-center items-center gap-y-10 md:gap-y-0 gap-x-2 py-10\">\r\n        <h1 className=\"text-3xl sm:text-4xl md:text-4xl lg:text-[55px] font-urbanist text-[#13031F] font-normal w-full md:w-[58%] text-center md:text-left\">\r\n          <span className=\"block mb-0 sm:mb-12 md:mb-8\">Our Comprehensive</span>\r\n          <span className=\"block\">Planning Services</span>\r\n        </h1>\r\n\r\n        <div className=\"flex flex-col justify-center items-start sm:w-full w-full md:w-[35%] gap-y-8\">\r\n          <div className=\"flex items-center justify-start gap-5\">\r\n            <h1 className=\"text-2xl sm:text-xl md:text-2xl text-[#13031FB2] font-urbanist font-medium\">Our Service</h1>\r\n            <h2 className='md:w-[5.9rem] sm:w-[6rem] w-[8rem] h-0 border'></h2>\r\n          </div>\r\n          <h2 className=\"text-sm sm:text-base text-[#13031FB2] font-urbanist\">\r\n            We offer a wide range of services tailored to meet your unique needs and preferences.\r\n            Let us take care of everything from start to finish.\r\n          </h2>\r\n        </div>\r\n      </div>\r\n\r\n      {/* card container */}\r\n      <div className=\"w-full flex justify-center items-center px-4 sm:px-6 md:px-10 lg:px-20\">\r\n        <CardGrid />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomeService;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;0CAC9C,8OAAC;gCAAK,WAAU;0CAAQ;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6E;;;;;;kDAC3F,8OAAC;wCAAG,WAAU;;;;;;;;;;;;0CAEhB,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;0BAQxE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/moments/moments.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from \"@/components/ui/carousel\";\r\nimport { ArrowRight } from 'lucide-react';\r\nimport titlearrow from \"@/public/image/icons/tilearrow.svg\"\r\nimport Link from 'next/link';\r\n\r\n// Sample data - replace with your actual images and content\r\nconst momentsData = [\r\n  {\r\n    couple: \"Andra & Jane\",\r\n    service: \"Full-Service Planning\",\r\n    image: \"/image/assests/moments/jane.png\" \r\n  },\r\n  {\r\n    couple: \"Mira & Jhon\",\r\n    service: \"Full-Service Planning\",\r\n    image: \"/image/assests/moments/jhon.png\"\r\n  },\r\n  {\r\n    couple: \"<PERSON><PERSON> & Martin\",\r\n    service: \"Partial Planning\",\r\n    image: \"/image/assests/moments/martin.png\"\r\n  },\r\n  {\r\n    couple: \"Miranda & Tom\",\r\n    service: \"Full-Service Planning\",\r\n    image: \"/image/assests/moments/jhon.png\"\r\n  }\r\n];\r\n\r\nconst Moments = () => {\r\n  return (\r\n    <div className=\"py-16 px-4 md:px-8 max-w-7xl mx-auto\">\r\n      {/* Header section */}\r\n      <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-12\">\r\n        <div className=\"mb-6 md:mb-0 flex flex-col justify-between h-[7rem]\">\r\n         <div className='flex flex-row items-center gap-2'>\r\n         <p className=\"text-sm uppercase tracking-wider text-gray-500 \">Our Work</p>\r\n         <span className=\"w-36 h-[1px] bg-gray-300 \"></span>\r\n         </div>\r\n          <p className=\"text-sm text-gray-600 max-w-xs\">\r\n            Take a look at some of the beautiful weddings we've had the pleasure of planning\r\n          </p>\r\n        </div>\r\n        \r\n        <h2 className=\"text-3xl md:text-4xl font-medium max-w-md \">\r\n          Memorable Moments We've Created\r\n        </h2>\r\n      </div>\r\n      \r\n\r\n      {/* Carousel */}\r\n      <Carousel\r\n        opts={{\r\n          align: \"start\",\r\n          loop: true,\r\n        }}\r\n        className=\"w-full\"\r\n      >\r\n          {/* Navigation buttons */}\r\n          <div className=\"flex justify-end gap-2 mt-6 pb-4\">\r\n          <CarouselPrevious className=\"static bg-white border border-[#fc873f]  text-[#fc873f] hover:bg-gray-50 transform-none translate-y-0 translate-x-0 rounded-full\" />\r\n          <CarouselNext className=\"static bg-[#FE904B] hover:bg-[#fc873f] text-white transform-none translate-y-0 translate-x-0 rounded-full\" />\r\n        </div>\r\n        <CarouselContent className=\"-ml-4\">\r\n          {momentsData.map((item, index) => (\r\n            <CarouselItem key={index} className=\"pl-4 md:basis-1/2 lg:basis-1/3\">\r\n              <div className=\"bg-[#FEECE2] rounded-lg p-4 h-full\">\r\n                <div className=\"flex justify-between items-center mb-4 border-b border-[#13031F33] py-2\">\r\n                  <div className=''>\r\n                    <h3 className=\"font-medium text-lg\">{item.couple}</h3>\r\n                    <p className=\"text-sm text-gray-600\">{item.service}</p>\r\n                  </div>\r\n                <Link href=\"/gallary\">\r\n                <button className=\"bg-[#FE904B] rounded-full w-8 h-8 flex items-center justify-center\">\r\n                    <Image src={titlearrow} alt=\"Arrow\" width={20} height={20} className=\"p-1\"/>\r\n                  </button>\r\n                </Link>\r\n                </div>\r\n                <div className=\"relative h-[16rem] md:h-[20rem] w-full overflow-hidden rounded-md\">\r\n                  <Image \r\n                    src={item.image}\r\n                    alt={item.couple}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </CarouselItem>\r\n          ))}\r\n        </CarouselContent>\r\n        \r\n      \r\n      </Carousel>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Moments;"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,4DAA4D;AAC5D,MAAM,cAAc;IAClB;QACE,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA;QACE,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA;QACE,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA;QACE,QAAQ;QACR,SAAS;QACT,OAAO;IACT;CACD;AAED,MAAM,UAAU;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACf,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,8OAAC;wCAAK,WAAU;;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;kCAKhD,8OAAC;wBAAG,WAAU;kCAA6C;;;;;;;;;;;;0BAO7D,8OAAC,6HAAA,CAAA,WAAQ;gBACP,MAAM;oBACJ,OAAO;oBACP,MAAM;gBACR;gBACA,WAAU;;kCAGR,8OAAC;wBAAI,WAAU;;0CACf,8OAAC,6HAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,8OAAC,6HAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;kCAE1B,8OAAC,6HAAA,CAAA,kBAAe;wBAAC,WAAU;kCACxB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,6HAAA,CAAA,eAAY;gCAAa,WAAU;0CAClC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAuB,KAAK,MAAM;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,OAAO;;;;;;;;;;;;8DAEtD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACX,cAAA,8OAAC;wDAAO,WAAU;kEACd,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DAAC,KAAK,0SAAA,CAAA,UAAU;4DAAE,KAAI;4DAAQ,OAAO;4DAAI,QAAQ;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAIzE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,MAAM;gDAChB,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;+BAlBC;;;;;;;;;;;;;;;;;;;;;;AA8B/B;uCAEe", "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/bigflower.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 445, height: 287, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAV0lEQVR42k1MWwrAIAxr+tLp/Nj9L7tmODAQyKONyAGIqoq5IZKkZ2hlGgtH9sA1A3062sVOKHY4EmOR1MyrDTmKu+t6so4N2QweENRITWsZhv/nN73xAr2WAn5v/VCDAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0N,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/vision_home/visionhome.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport leafflower from \"@/public/image/assests/bigflower.png\";\r\nimport { ArrowLeft, ArrowRight } from 'lucide-react';\r\n\r\n// Sample data for the steps\r\nconst stepsData = [\r\n  {\r\n    id: \"01\",\r\n    title: \"Login\",\r\n    description: \"Receive your personalized wedding planning dashboard! Stay organized on every detail of the details for your big day.\",\r\n    image: \"/image/assests/event1.png\" // Replace with actual image path\r\n  },\r\n  {\r\n    id: \"02\",\r\n    title: \"Consultation\",\r\n    description: \"Meet with our expert planners to discuss your vision, preferences, and budget for your special day.\",\r\n    image: \"/image/assests/event2.png\" // Replace with actual image path\r\n  },\r\n  {\r\n    id: \"03\",\r\n    title: \"Planning\",\r\n    description: \"We create a detailed timeline and coordinate with vendors to ensure everything runs smoothly.\",\r\n    image: \"/image/assests/event3.png\" // Replace with actual image path\r\n  },\r\n  {\r\n    id: \"04\",\r\n    title: \"Execution\",\r\n    description: \"On your big day, we handle all the details so you can focus on creating memories that last a lifetime.\",\r\n    image: \"/image/assests/event1.png\" // Replace with actual image path\r\n  }\r\n];\r\n\r\nconst VisionHome = () => {\r\n  const [activeStep, setActiveStep] = useState(0);\r\n  const [progress, setProgress] = useState(25); // Initial progress percentage\r\n  const currentStep = stepsData[activeStep];\r\n  \r\n  // Update progress when active step changes\r\n  useEffect(() => {\r\n    const newProgress = ((activeStep + 1) / stepsData.length) * 100;\r\n    setProgress(newProgress);\r\n  }, [activeStep]);\r\n  \r\n  const handlePrevStep = () => {\r\n    setActiveStep((prev) => (prev === 0 ? stepsData.length - 1 : prev - 1));\r\n  };\r\n  \r\n  const handleNextStep = () => {\r\n    setActiveStep((prev) => (prev === stepsData.length - 1 ? 0 : prev + 1));\r\n  };\r\n\r\n  return (\r\n    <div className=\"py-16 px-0 md:px-8 w-full bg-[#F8E7DD]\">\r\n      <div className=\"flex flex-col lg:flex-row gap-8 lg:gap-16 items-center justify-between\">\r\n        <div>\r\n            {/* Left Section */}\r\n        <div className=\"lg:w-1/2 h- relative px-4\">\r\n          <div className='flex flex-row items-center gap-2 w-full'>\r\n          <p className=\"text-sm uppercase tracking-wider text-gray-500 \">Our Process</p>\r\n          <span className=\"w-[4rem] h-px bg-gray-300 \"></span>\r\n          </div>\r\n          \r\n          <h2 className=\"text-4xl md:text-6xl font-medium mb-8 md:w-[33rem] w-full\">\r\n            How We Bring Your Vision to Life\r\n          </h2>\r\n          \r\n          {/* Navigation buttons */}\r\n          <div className=\"flex gap-2 mt-6\">\r\n            <button \r\n              onClick={handlePrevStep}\r\n              className=\"bg-white border border-gray-200 hover:bg-gray-50 text-black w-10 h-10 rounded-full flex items-center justify-center\"\r\n            >\r\n              <ArrowLeft size={18} />\r\n            </button>\r\n            <button \r\n              onClick={handleNextStep}\r\n              className=\"bg-[#FE904B] hover:bg-[#fc873f] text-white w-10 h-10 rounded-full flex items-center justify-center\"\r\n            >\r\n              <ArrowRight size={18} />\r\n            </button>\r\n          </div>\r\n        </div>\r\n          \r\n          {/* Decorative flower image */}\r\n          <div className=\"w-full h-full opacity-30\">\r\n            <Image \r\n              src={leafflower} \r\n              alt=\"Decorative flower\" \r\n              className=\"object-contain\"\r\n            />\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Right Section - Steps Carousel */}\r\n        <div className=\"lg:w-[45%] w-[100%] flex flex-col justify-center items-center\">\r\n          {/* Step indicators */}\r\n          <div className=\"w-[85%] flex justify-center mb-6 gap-[2rem] rounded-2xl p-2 items-center bg-[#f7f6f6]\">\r\n            {stepsData.map((step, index) => (\r\n              <button\r\n                key={index}\r\n                onClick={() => setActiveStep(index)}\r\n                className={`px-3 py-1 rounded-full text-xs ${\r\n                  activeStep === index \r\n                    ? \"bg-[#FE904B] text-white\" \r\n                    : \"bg-transparent text-gray-700 hover:bg-gray-100\"\r\n                }`}\r\n              >\r\n                Step {index + 1}\r\n              </button>\r\n            ))}\r\n          </div>\r\n          \r\n          {/* Current step card */}\r\n          <div className=\"bg-white w-[90%] rounded-lg p-0  md:p-6 shadow-sm\">\r\n            <div className=\"mb-4\">\r\n              <h3 className=\"text-2xl font-medium\">{currentStep.id}</h3>\r\n            </div>\r\n            \r\n            <div className=\"relative h-64 w-full mb-4 overflow-hidden rounded-md\">\r\n              <Image \r\n                src={currentStep.image}\r\n                alt={currentStep.title}\r\n                fill\r\n                className=\"object-cover\"\r\n              />\r\n            </div>\r\n            \r\n            <h4 className=\"text-xl font-medium mb-2\">{currentStep.title}</h4>\r\n            <p className=\"text-gray-600\">{currentStep.description}</p>\r\n            \r\n           \r\n          </div>\r\n           {/* Progress indicator with percentage in circle */}\r\n           <div className=\"mt-2 relative w-[80%]\">\r\n              <div className=\"bg-[#ffffff] h-8 p-1 rounded-full w-full\">\r\n                <div \r\n                  className=\"bg-[#fabacf] h-full rounded-full transition-all duration-500 ease-in-out\"\r\n                  style={{ width: `${progress}%` }}\r\n                />\r\n                <div \r\n                  className=\"absolute top-1/2 -translate-y-1/2 w-7 h-7 bg-[#FE904B] rounded-full flex items-center justify-center text-white text-[10px] font-medium transition-all duration-500 ease-in-out\"\r\n                  style={{ left: `calc(${progress}% - 16px)` }}\r\n                >\r\n                  {Math.round(progress)}%\r\n                </div>\r\n              </div>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VisionHome;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAJA;;;;;;AAMA,4BAA4B;AAC5B,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO,4BAA4B,iCAAiC;IACtE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO,4BAA4B,iCAAiC;IACtE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO,4BAA4B,iCAAiC;IACtE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO,4BAA4B,iCAAiC;IACtE;CACD;AAED,MAAM,aAAa;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,8BAA8B;IAC5E,MAAM,cAAc,SAAS,CAAC,WAAW;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,AAAC,CAAC,aAAa,CAAC,IAAI,UAAU,MAAM,GAAI;QAC5D,YAAY;IACd,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB;QACrB,cAAc,CAAC,OAAU,SAAS,IAAI,UAAU,MAAM,GAAG,IAAI,OAAO;IACtE;IAEA,MAAM,iBAAiB;QACrB,cAAc,CAAC,OAAU,SAAS,UAAU,MAAM,GAAG,IAAI,IAAI,OAAO;IACtE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCAED,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACf,8OAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAC/D,8OAAC;4CAAK,WAAU;;;;;;;;;;;;8CAGhB,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAK1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,8SAAA,CAAA,UAAU;gCACf,KAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oCAEC,SAAS,IAAM,cAAc;oCAC7B,WAAW,CAAC,+BAA+B,EACzC,eAAe,QACX,4BACA,kDACJ;;wCACH;wCACO,QAAQ;;mCART;;;;;;;;;;sCAcX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAwB,YAAY,EAAE;;;;;;;;;;;8CAGtD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,YAAY,KAAK;wCACtB,KAAK,YAAY,KAAK;wCACtB,IAAI;wCACJ,WAAU;;;;;;;;;;;8CAId,8OAAC;oCAAG,WAAU;8CAA4B,YAAY,KAAK;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAiB,YAAY,WAAW;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;sCACZ,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,SAAS,CAAC,CAAC;wCAAC;;;;;;kDAEjC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,MAAM,CAAC,KAAK,EAAE,SAAS,SAAS,CAAC;wCAAC;;4CAE1C,KAAK,KAAK,CAAC;4CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;uCAEe", "debugId": null}}, {"offset": {"line": 1587, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/icons/arrowreviewleft.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 71, height: 45, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/icons/arrowreviewright.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 71, height: 45, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,qIAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/icons/queto.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 18, height: 15, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/lib/api/service/serviceApi.ts"], "sourcesContent": ["// Service API module\nimport { apiClient } from '../../customaxios';\nimport { buildUrl } from '../../globalurl';\n\n// Types for service API\nexport interface Service {\n  _id: string;\n  id: string;\n  title: string;\n  description: string;\n  description2?: string;\n  shortDescription: string;\n  slug: string;\n  category: string;\n  price: number;\n  duration: string;\n  features: string[];\n  images: string[];\n  image: string; // Primary image URL\n  howWeDoIt?: Array<{\n    title: string;\n    description: string;\n    icon?: string;\n  }>;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Gallery {\n  id: string;\n  title: string;\n  description: string;\n  imageUrl: string;\n  category: string;\n  tags: string[];\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface GalleryCategory {\n  id: string;\n  name: string;\n  slug: string;\n  description: string;\n  imageCount: number;\n}\n\nexport interface Review {\n  _id: string;\n  id: string;\n  name: string;\n  email: string;\n  rating: number;\n  comment: string;\n  review: string; // Alias for comment\n  relationship: string;\n  serviceId?: string;\n  isApproved: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ServiceQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  category?: string;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface GalleryQueryParams {\n  page?: number;\n  limit?: number;\n  category?: string;\n  tags?: string[];\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface ServicesResponse {\n  success: boolean;\n  message: string;\n  data: {\n    services: Service[];\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\nexport interface ServiceResponse {\n  success: boolean;\n  message: string;\n  data?: Service;\n}\n\nexport interface GalleryResponse {\n  gallery: Gallery[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface ReviewsResponse {\n  success: boolean;\n  message: string;\n  data: {\n    reviews: Review[];\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\n// Service API functions\nexport const serviceApi = {\n  // Get all services with pagination and filters\n  getServices: async (params: ServiceQueryParams = {}): Promise<ServicesResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ServicesResponse>(\n      buildUrl(`/services?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get single service by ID or slug\n  getServiceById: async (id: string): Promise<ServiceResponse> => {\n    const response = await apiClient.get<ServiceResponse>(\n      buildUrl(`/services/${id}`)\n    );\n    return response.data;\n  },\n\n  // Get service by slug\n  getServiceBySlug: async (slug: string): Promise<ServiceResponse> => {\n    const response = await apiClient.get<ServiceResponse>(\n      buildUrl(`/services/slug/${slug}`)\n    );\n    return response.data;\n  },\n\n  // Get service categories\n  getServiceCategories: async (): Promise<string[]> => {\n    const response = await apiClient.get<{ categories: string[] }>(\n      buildUrl('/services/categories')\n    );\n    return response.data.categories;\n  },\n};\n\n// Gallery API functions\nexport const galleryApi = {\n  // Get all gallery items with pagination and filters\n  getGallery: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryParams = new URLSearchParams();\n\n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        if (Array.isArray(value)) {\n          value.forEach(v => queryParams.append(key, v.toString()));\n        } else {\n          queryParams.append(key, value.toString());\n        }\n      }\n    });\n\n    const response = await apiClient.get<GalleryResponse>(\n      buildUrl(`/gallery?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Alias for getGallery - for compatibility\n  getGalleryItems: async (params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    return galleryApi.getGallery(params);\n  },\n\n  // Get gallery categories\n  getGalleryCategories: async (): Promise<GalleryCategory[]> => {\n    const response = await apiClient.get<{ categories: GalleryCategory[] }>(\n      buildUrl('/gallery/categories')\n    );\n    return response.data.categories;\n  },\n\n  // Get gallery by category\n  getGalleryByCategory: async (category: string, params: GalleryQueryParams = {}): Promise<GalleryResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<GalleryResponse>(\n      buildUrl(`/gallery/category/${category}?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n};\n\n// Reviews API functions\nexport const reviewsApi = {\n  // Get all reviews with pagination\n  getReviews: async (params: { page?: number; limit?: number; serviceId?: string; sortBy?: string; sortOrder?: 'asc' | 'desc' } = {}): Promise<ReviewsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ReviewsResponse>(\n      buildUrl(`/reviews?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n\n  // Get reviews for a specific service\n  getServiceReviews: async (serviceId: string, params: { page?: number; limit?: number } = {}): Promise<ReviewsResponse> => {\n    const queryParams = new URLSearchParams();\n    \n    Object.entries(params).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        queryParams.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.get<ReviewsResponse>(\n      buildUrl(`/reviews/service/${serviceId}?${queryParams.toString()}`)\n    );\n    return response.data;\n  },\n};\n\n// Helper functions\nexport const serviceHelpers = {\n  // Format price for display\n  formatPrice: (price: number): string => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0,\n    }).format(price);\n  },\n\n  // Create slug from title\n  createSlug: (title: string): string => {\n    return title\n      .toLowerCase()\n      .replace(/[^a-z0-9 -]/g, '')\n      .replace(/\\s+/g, '-')\n      .replace(/-+/g, '-')\n      .trim();\n  },\n\n  // Truncate text\n  truncateText: (text: string, maxLength: number): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + '...';\n  },\n\n  // Format rating for display\n  formatRating: (rating: number): string => {\n    return rating.toFixed(1);\n  },\n\n  // Get star rating array for display\n  getStarRating: (rating: number): { filled: number; half: boolean; empty: number } => {\n    const filled = Math.floor(rating);\n    const half = rating % 1 >= 0.5;\n    const empty = 5 - filled - (half ? 1 : 0);\n\n    return { filled, half, empty };\n  },\n\n  // Format relationship for display\n  formatRelationship: (relationship: string): string => {\n    return relationship\n      .split('-')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n  },\n\n  // Format card title for display\n  formatCardTitle: (title: string): string => {\n    return title\n      .split(' ')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n      .join(' ');\n  },\n\n  // Format card description for display\n  formatCardDescription: (description: string, maxLength: number = 150): string => {\n    if (!description) return '';\n    if (description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  },\n};\n\n// Export individual functions for easier imports\nexport const getServices = serviceApi.getServices;\nexport const getServiceById = serviceApi.getServiceById;\nexport const getServiceBySlug = serviceApi.getServiceBySlug;\nexport const getServiceCategories = serviceApi.getServiceCategories;\nexport const getGallery = galleryApi.getGallery;\nexport const getGalleryCategories = galleryApi.getGalleryCategories;\nexport const getGalleryByCategory = galleryApi.getGalleryByCategory;\nexport const getReviews = reviewsApi.getReviews;\nexport const getServiceReviews = reviewsApi.getServiceReviews;\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;;;;;;;;;;;;AACrB;AACA;;;AAuHO,MAAM,aAAa;IACxB,+CAA+C;IAC/C,aAAa,OAAO,SAA6B,CAAC,CAAC;QACjD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,UAAU,EAAE,YAAY,QAAQ,IAAI;QAEhD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,UAAU,EAAE,IAAI;QAE5B,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,eAAe,EAAE,MAAM;QAEnC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI,CAAC,UAAU;IACjC;AACF;AAGO,MAAM,aAAa;IACxB,oDAAoD;IACpD,YAAY,OAAO,SAA6B,CAAC,CAAC;QAChD,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,YAAY,MAAM,CAAC,KAAK,EAAE,QAAQ;gBACvD,OAAO;oBACL,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACxC;YACF;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,SAAS,EAAE,YAAY,QAAQ,IAAI;QAE/C,OAAO,SAAS,IAAI;IACtB;IAEA,2CAA2C;IAC3C,iBAAiB,OAAO,SAA6B,CAAC,CAAC;QACrD,OAAO,WAAW,UAAU,CAAC;IAC/B;IAEA,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE;QAEX,OAAO,SAAS,IAAI,CAAC,UAAU;IACjC;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO,UAAkB,SAA6B,CAAC,CAAC;QAC5E,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAE,YAAY,QAAQ,IAAI;QAEpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,kCAAkC;IAClC,YAAY,OAAO,SAA6G,CAAC,CAAC;QAChI,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,SAAS,EAAE,YAAY,QAAQ,IAAI;QAE/C,OAAO,SAAS,IAAI;IACtB;IAEA,qCAAqC;IACrC,mBAAmB,OAAO,WAAmB,SAA4C,CAAC,CAAC;QACzF,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,WAAW,MAAM,kHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE,YAAY,QAAQ,IAAI;QAEpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,2BAA2B;IAC3B,aAAa,CAAC;QACZ,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,yBAAyB;IACzB,YAAY,CAAC;QACX,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;IACT;IAEA,gBAAgB;IAChB,cAAc,CAAC,MAAc;QAC3B,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;IAC/C;IAEA,4BAA4B;IAC5B,cAAc,CAAC;QACb,OAAO,OAAO,OAAO,CAAC;IACxB;IAEA,oCAAoC;IACpC,eAAe,CAAC;QACd,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,MAAM,OAAO,SAAS,KAAK;QAC3B,MAAM,QAAQ,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC;QAExC,OAAO;YAAE;YAAQ;YAAM;QAAM;IAC/B;IAEA,kCAAkC;IAClC,oBAAoB,CAAC;QACnB,OAAO,aACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,gCAAgC;IAChC,iBAAiB,CAAC;QAChB,OAAO,MACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACpE,IAAI,CAAC;IACV;IAEA,sCAAsC;IACtC,uBAAuB,CAAC,aAAqB,YAAoB,GAAG;QAClE,IAAI,CAAC,aAAa,OAAO;QACzB,IAAI,YAAY,MAAM,IAAI,WAAW,OAAO;QAC5C,OAAO,YAAY,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;IACtD;AACF;AAGO,MAAM,cAAc,WAAW,WAAW;AAC1C,MAAM,iBAAiB,WAAW,cAAc;AAChD,MAAM,mBAAmB,WAAW,gBAAgB;AACpD,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,aAAa,WAAW,UAAU;AACxC,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,uBAAuB,WAAW,oBAAoB;AAC5D,MAAM,aAAa,WAAW,UAAU;AACxC,MAAM,oBAAoB,WAAW,iBAAiB", "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/testimonials/testmonials.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport arrowleft from \"@/public/image/icons/arrowreviewleft.svg\";\r\nimport arrowright from \"@/public/image/icons/arrowreviewright.svg\";\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  type CarouselApi,\r\n} from \"@/components/ui/carousel\";\r\nimport queto from \"@/public/image/icons/queto.svg\";\r\nimport { reviewsApi, Review, serviceHelpers } from \"@/lib/api/service/serviceApi\";\r\nimport { toast } from \"react-toastify\";\r\n\r\n/* ─────────── fallback data if <PERSON> fails ─────────── */\r\nconst fallbackTestimonials = [\r\n  {\r\n    id: 1,\r\n    quote:\r\n      \"There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form. There are many variations of passages of Lorem. There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.\",\r\n    name: \"BROOKLYN SIMMONS\",\r\n    role: \"Parents\",\r\n    avatar: \"/image/assests/review/1.png\",\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 2,\r\n    quote:\r\n      \"There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form. There are many variations of passages of Lorem.\",\r\n    name: \"JESSICA PARKER\",\r\n    role: \"Bride\",\r\n    avatar: \"/image/assests/review/2.png\",\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 3,\r\n    quote:\r\n      \"There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.\",\r\n    name: \"MICHAEL JOHNSON\",\r\n    role: \"Groom\",\r\n    avatar: \"/image/assests/review/6.png\",\r\n    rating: 5,\r\n  },\r\n];\r\n\r\nconst Testimonials = () => {\r\n  const [api, setApi] = useState<CarouselApi | null>(null);\r\n  const [testimonials, setTestimonials] = useState<any[]>(fallbackTestimonials);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  /* ─────────── fetch reviews from backend ─────────── */\r\n  const fetchReviews = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const res = await reviewsApi.getReviews({\r\n        sortBy: \"sortOrder\",\r\n        sortOrder: \"asc\",\r\n        limit: 10,\r\n      });\r\n\r\n      if (res.success && res.data.reviews?.length) {\r\n        const mapped = res.data.reviews.map((r: Review) => ({\r\n          id: r._id,\r\n          quote: r.review,\r\n          name: r.name.toUpperCase(),\r\n          role: serviceHelpers.formatRelationship(r.relationship),\r\n          avatar: r.image,\r\n          rating: r.star,\r\n        }));\r\n        setTestimonials(mapped);\r\n      } else {\r\n        setTestimonials(fallbackTestimonials);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Failed to load reviews:\", err);\r\n      toast.error(\"Failed to load testimonials\");\r\n      setTestimonials(fallbackTestimonials);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchReviews();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"py-16 px-4 md:px-8 max-w-7xl mx-auto\">\r\n      {/* Heading */}\r\n      <div className=\"mb-12 text-center\">\r\n        <h3 className=\"mb-2 flex items-center justify-center gap-4 text-[24px] uppercase tracking-wider text-gray-500\">\r\n          <span className=\"h-0 w-[6rem] border border-[#cacaca]\" />\r\n          Happy Clients\r\n          <span className=\"h-0 w-[6rem] border border-[#cacaca]\" />\r\n        </h3>\r\n        <h2 className=\"text-3xl font-normal md:text-4xl\">\r\n          LOVE SEALED WITH A KISS\r\n        </h2>\r\n        <h2 className=\"text-3xl font-normal md:text-4xl\">FOREVER IN BLISS</h2>\r\n      </div>\r\n\r\n      {/* Carousel */}\r\n      <div className=\"relative\">\r\n        <Carousel\r\n          setApi={setApi}\r\n          opts={{ loop: true, align: \"center\" }}\r\n          className=\"w-full\"\r\n        >\r\n          <CarouselContent>\r\n            {testimonials.map((t) => (\r\n              <CarouselItem key={t.id} className=\"flex justify-center\">\r\n                <div className=\"w-full max-w-[700px] rounded-lg bg-[#FEF2EB] p-6 md:p-10 text-center\">\r\n                  {/* Avatar */}\r\n                  <div className=\"relative mx-auto mb-6 h-16 w-16\">\r\n                    {t.avatar && t.avatar.startsWith(\"http\") ? (\r\n                      <img\r\n                        src={t.avatar}\r\n                        alt={t.name}\r\n                        className=\"h-16 w-16 rounded-full object-cover\"\r\n                      />\r\n                    ) : t.avatar ? (\r\n                      <Image\r\n                        src={t.avatar}\r\n                        alt={t.name}\r\n                        width={64}\r\n                        height={64}\r\n                        className=\"rounded-full object-cover\"\r\n                      />\r\n                    ) : (\r\n                      <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-[#FE904B] text-xl font-bold text-white\">\r\n                        {t.name.charAt(0)}\r\n                      </div>\r\n                    )}\r\n                    <div className=\"absolute -bottom-2 -right-2 flex h-7 w-7 items-center justify-center rounded-full bg-[#FE904B] p-1 text-white\">\r\n                      <Image src={queto} alt=\"Quote\" width={18} height={15} />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Quote */}\r\n                  <p className=\"mx-auto mb-6 text-base leading-relaxed text-gray-700 md:text-lg md:leading-loose break-words\">\r\n                    {t.quote}\r\n                  </p>\r\n\r\n                  {/* Rating */}\r\n                  <div className=\"mb-3 flex justify-center\">\r\n                    {Array.from({ length: t.rating }).map((_, i) => (\r\n                      <span key={i} className=\"text-xl text-yellow-400\">\r\n                        ★\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {/* Name + Role */}\r\n                  <h4 className=\"text-lg font-semibold uppercase text-gray-800\">\r\n                    {t.name}\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-500\">{t.role}</p>\r\n                </div>\r\n              </CarouselItem>\r\n            ))}\r\n          </CarouselContent>\r\n\r\n\r\n          {/* arrows – desktop */}\r\n          <button\r\n            onClick={() => api?.scrollPrev()}\r\n            className=\"absolute left-0 top-[45%] -translate-x-1/2 -translate-y-1/2 hidden sm:block\"\r\n          >\r\n            <Image src={arrowleft} alt=\"Prev\" width={71} height={45} />\r\n          </button>\r\n          <button\r\n            onClick={() => api?.scrollNext()}\r\n            className=\"absolute right-0 top-[45%] translate-x-1/2 -translate-y-1/2 hidden sm:block\"\r\n          >\r\n            <Image src={arrowright} alt=\"Next\" width={71} height={45} />\r\n          </button>\r\n\r\n          {/* arrows – mobile */}\r\n          <div className=\"sm:hidden mt-6 flex w-full justify-center gap-8\">\r\n            <button onClick={() => api?.scrollPrev()}>\r\n              <Image src={arrowleft} alt=\"Prev\" width={50} height={32} />\r\n            </button>\r\n            <button onClick={() => api?.scrollNext()}>\r\n              <Image src={arrowright} alt=\"Next\" width={50} height={32} />\r\n            </button>\r\n          </div>\r\n        </Carousel>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Testimonials;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AAbA;;;;;;;;;;AAeA,sDAAsD,GACtD,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,OACE;QACF,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OACE;QACF,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OACE;QACF,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,eAAe;IACnB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sDAAsD,GACtD,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,MAAM,MAAM,MAAM,mIAAA,CAAA,aAAU,CAAC,UAAU,CAAC;gBACtC,QAAQ;gBACR,WAAW;gBACX,OAAO;YACT;YAEA,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ;gBAC3C,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAc,CAAC;wBAClD,IAAI,EAAE,GAAG;wBACT,OAAO,EAAE,MAAM;wBACf,MAAM,EAAE,IAAI,CAAC,WAAW;wBACxB,MAAM,mIAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,EAAE,YAAY;wBACtD,QAAQ,EAAE,KAAK;wBACf,QAAQ,EAAE,IAAI;oBAChB,CAAC;gBACD,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;;;;;;4BAAyC;0CAEzD,8OAAC;gCAAK,WAAU;;;;;;;;;;;;kCAElB,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCAGjD,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;0BAInD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oBACP,QAAQ;oBACR,MAAM;wBAAE,MAAM;wBAAM,OAAO;oBAAS;oBACpC,WAAU;;sCAEV,8OAAC,6HAAA,CAAA,kBAAe;sCACb,aAAa,GAAG,CAAC,CAAC,kBACjB,8OAAC,6HAAA,CAAA,eAAY;oCAAY,WAAU;8CACjC,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;oDACZ,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,wBAC/B,8OAAC;wDACC,KAAK,EAAE,MAAM;wDACb,KAAK,EAAE,IAAI;wDACX,WAAU;;;;;+DAEV,EAAE,MAAM,iBACV,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,EAAE,MAAM;wDACb,KAAK,EAAE,IAAI;wDACX,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;kEACZ,EAAE,IAAI,CAAC,MAAM,CAAC;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DAAC,KAAK,kSAAA,CAAA,UAAK;4DAAE,KAAI;4DAAQ,OAAO;4DAAI,QAAQ;;;;;;;;;;;;;;;;;0DAKtD,8OAAC;gDAAE,WAAU;0DACV,EAAE,KAAK;;;;;;0DAIV,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ,EAAE,MAAM;gDAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBACxC,8OAAC;wDAAa,WAAU;kEAA0B;uDAAvC;;;;;;;;;;0DAOf,8OAAC;gDAAG,WAAU;0DACX,EAAE,IAAI;;;;;;0DAET,8OAAC;gDAAE,WAAU;0DAAyB,EAAE,IAAI;;;;;;;;;;;;mCA9C7B,EAAE,EAAE;;;;;;;;;;sCAsD3B,8OAAC;4BACC,SAAS,IAAM,KAAK;4BACpB,WAAU;sCAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAK,sTAAA,CAAA,UAAS;gCAAE,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAEvD,8OAAC;4BACC,SAAS,IAAM,KAAK;4BACpB,WAAU;sCAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCAAC,KAAK,wTAAA,CAAA,UAAU;gCAAE,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,SAAS,IAAM,KAAK;8CAC1B,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,sTAAA,CAAA,UAAS;wCAAE,KAAI;wCAAO,OAAO;wCAAI,QAAQ;;;;;;;;;;;8CAEvD,8OAAC;oCAAO,SAAS,IAAM,KAAK;8CAC1B,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCAAC,KAAK,wTAAA,CAAA,UAAU;wCAAE,KAAI;wCAAO,OAAO;wCAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpE;uCAEe", "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/public/image/assests/borderflower.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 471, height: 459, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAbUlEQVR42n2PSw7DMAhEweZnTNwkatXe/6Q1UZZJ3g7xGDGgxDzU29Duw7qvFr6I29Yi3v4awJXI2TRFI+FGKn3OufzFvkOCgAgnBQvmwUzqR8IVVGrNtM+dkKTwjW19EmSZz98KOGms8ihksz/4nAQrux4x5AAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0P,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 2215, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/parvenets/PR001_Parv_Event/app/_components/gettouch/get_touch.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Image from 'next/image';\r\nimport rightflower from \"@/public/image/assests/borderflower.png\";\r\nimport { ArrowRight } from 'lucide-react';\r\nimport tiltarrow from \"@/public/image/icons/tilearrow.svg\"\r\nimport Link from 'next/link';\r\n\r\nconst GetTouch = () => {\r\n  return (\r\n    <div className=\"relative w-full overflow-hidden\">\r\n      {/* Background with gradient */}\r\n      <div \r\n        className=\"w-full py-16 lg:py-24 bg-gradient-to-br from-[#FFEBDE] via-white to-[#FFEBDE]  \"\r\n        \r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8 relative z-10\">\r\n          <div className=\"flex flex-row items-center justify-center\">\r\n            {/* Text section - Centered */}\r\n            <div className=\"w-full mb-8 text-center\">\r\n              <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-left font-medium text-[#13031F] leading-tight max-w-md mx-auto\">\r\n                Get in touch with us to schedule a consultation\r\n              </h2>\r\n            </div>\r\n            \r\n            {/* Button section - Centered */}\r\n         \r\n           <div className=\"w-full flex flex-col sm:flex-row justify-center sm:justify-start items-center gap-2 sm:gap-0\">\r\n             <Link href={\"/contact\"} className=\"flex items-center\">\r\n               <button className=\"bg-[#FE904B] hover:bg-[#e87f3d] text-white px-4 sm:px-6 py-2 sm:py-3 rounded-full flex items-center gap-2 transition-all duration-300 shadow-lg text-sm sm:text-base\">\r\n                 Get Started\r\n               </button>\r\n               <span className=\"px-[5px] sm:px-[7px] bg-[#FE904B] rounded-full flex items-center justify-center ml-1\">\r\n                 <Image src={tiltarrow} alt=\"Arrow\" width={30} height={30} className='p-1 sm:p-2' />\r\n               </span>\r\n             </Link>\r\n           </div>\r\n           \r\n          </div>\r\n        </div>\r\n        \r\n        {/* Flower decoration on right */}\r\n        <div className=\"absolute top-0 right-0 h-full\">\r\n          <div className=\"relative h-full\">\r\n            <Image\r\n              src={rightflower}\r\n              alt=\"Decorative flower\"\r\n              className=\"h-full w-auto object-contain object-right\"\r\n              priority\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GetTouch;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YACC,WAAU;;8BAGV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmH;;;;;;;;;;;0CAOpI,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;oCAAY,WAAU;;sDAChC,8OAAC;4CAAO,WAAU;sDAAuK;;;;;;sDAGzL,8OAAC;4CAAK,WAAU;sDACd,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAK,0SAAA,CAAA,UAAS;gDAAE,KAAI;gDAAQ,OAAO;gDAAI,QAAQ;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,oTAAA,CAAA,UAAW;4BAChB,KAAI;4BACJ,WAAU;4BACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;uCAEe", "debugId": null}}]}