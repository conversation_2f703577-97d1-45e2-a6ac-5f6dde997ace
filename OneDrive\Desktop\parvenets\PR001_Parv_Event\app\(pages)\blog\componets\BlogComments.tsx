"use client";
import React, { useState, useEffect } from "react";
import {
  FiMessageSquare,
  FiUser,
  FiMail,
  FiSend,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
} from "react-icons/fi";
import {
  submitComment,
  getApprovedComments,
  Comment,
  formatCommentDate,
} from "@/lib/api/blogs/commentApi";

interface BlogCommentsProps {
  blogId: string | undefined;
  blogTitle?: string;
}

interface CommentFormData {
  name: string;
  email: string;
  comment: string;
}

const BlogComments: React.FC<BlogCommentsProps> = ({ blogId, blogTitle }) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CommentFormData>({
    name: "",
    email: "",
    comment: "",
  });
  const [formErrors, setFormErrors] = useState<Partial<CommentFormData>>({});

  useEffect(() => {
    if (blogId) {
      fetchComments();
    }
  }, [blogId]);

  const fetchComments = async () => {
    if (!blogId) return;

    try {
      setLoading(true);
      const response = await getApprovedComments({ limit: 50, blogId });
      // Handle the response data properly
      if (response.status && response.data) {
        setComments(Array.isArray(response.data) ? response.data : []);
      } else {
        setComments([]);
      }
    } catch (err) {
      console.error("Error fetching comments:", err);
      setError("Failed to load comments");
      setComments([]);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const errors: Partial<CommentFormData> = {};

    if (!formData.name.trim()) {
      errors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      errors.name = "Name must be at least 2 characters";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    if (!formData.comment.trim()) {
      errors.comment = "Comment is required";
    } else if (formData.comment.trim().length < 10) {
      errors.comment = "Comment must be at least 10 characters";
    } else if (formData.comment.trim().length > 1000) {
      errors.comment = "Comment must be less than 1000 characters";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    if (formErrors[name as keyof CommentFormData]) {
      setFormErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !blogId) return;

    try {
      setSubmitting(true);
      setError(null);

      const response = await submitComment({
        name: formData.name.trim(),
        email: formData.email.trim(),
        comment: formData.comment.trim(),
        blogId: blogId,
      });

      if (response.status) {
        setFormData({ name: "", email: "", comment: "" });
        setShowSuccess(true);
        fetchComments(); // Refresh comments after submission
        setTimeout(() => setShowSuccess(false), 5000);
      } else {
        setError(response.message || "Failed to submit comment. Please try again.");
      }
    } catch (err: any) {
      console.error("Error submitting comment:", err);

      // Handle different types of errors
      let errorMessage = "Failed to submit comment. Please try again.";

      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }

      // Handle specific database connection errors
      if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('mongodb.net')) {
        errorMessage = "Database connection error. Please try again later or contact support.";
      }

      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="w-full text-gray-900 py-8 md:py-12">
      <div className="w-full px-0 md:px-4">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-2 flex items-center">
            <FiMessageSquare className="mr-3 h-5 w-5 md:h-6 md:w-6 text-orange-500" />
            Comments ({comments.length})
          </h2>
          <p className="text-gray-600 text-sm md:text-base">
            Share your thoughts about this article. Your comment will be
            reviewed before publishing.
          </p>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="mb-4 md:mb-6 bg-green-50 border border-green-200 rounded-lg p-3 md:p-4">
            <div className="flex items-center">
              <FiCheckCircle className="h-4 w-4 md:h-5 md:w-5 text-green-600 mr-2 md:mr-3" />
              <div>
                <h3 className="text-xs md:text-sm font-medium text-green-800">
                  Comment Submitted Successfully!
                </h3>
                <p className="text-xs md:text-sm text-green-700 mt-1">
                  Thank you for your comment. It will be reviewed and published
                  soon.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-4 md:mb-6 bg-red-50 border border-red-200 rounded-lg p-3 md:p-4">
            <div className="flex items-center">
              <FiAlertCircle className="h-4 w-4 md:h-5 md:w-5 text-red-600 mr-2 md:mr-3" />
              <div>
                <h3 className="text-xs md:text-sm font-medium text-red-800">
                  Error
                </h3>
                <p className="text-xs md:text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Comment Form */}
        <div className="bg-white border border-gray-200 shadow-lg rounded-lg p-4 md:p-6 mb-6 md:mb-8">
          <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">
            Leave a Comment
          </h3>

          <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
              {/* Name Field */}
              <div>
                <label
                  htmlFor="name"
                  className="block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2"
                >
                  <FiUser className="inline mr-2 h-3 w-3 md:h-4 md:w-4" />
                  Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ${
                    formErrors.name ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Your full name"
                  disabled={submitting}
                />
                {formErrors.name && (
                  <p className="mt-1 text-xs md:text-sm text-red-400">
                    {formErrors.name}
                  </p>
                )}
              </div>

              {/* Email Field */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2"
                >
                  <FiMail className="inline mr-2 h-3 w-3 md:h-4 md:w-4" />
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ${
                    formErrors.email ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="<EMAIL>"
                  disabled={submitting}
                />
                {formErrors.email && (
                  <p className="mt-1 text-xs md:text-sm text-red-400">
                    {formErrors.email}
                  </p>
                )}
              </div>
            </div>

            {/* Comment Field */}
            <div>
              <label
                htmlFor="comment"
                className="block text-xs md:text-sm font-medium text-gray-600 mb-1 md:mb-2"
              >
                <FiMessageSquare className="inline mr-2 h-3 w-3 md:h-4 md:w-4" />
                Comment *
              </label>
              <textarea
                id="comment"
                name="comment"
                rows={4}
                value={formData.comment}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 md:px-4 md:py-3 bg-transparent border-b text-gray-900 placeholder-gray-500 outline-none transition-colors ${
                  formErrors.comment ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Share your thoughts about this article..."
                disabled={submitting}
              />
              <div className="flex justify-between items-center mt-1">
                {formErrors.comment ? (
                  <p className="text-xs md:text-sm text-red-400">
                    {formErrors.comment}
                  </p>
                ) : (
                  <p className="text-xs md:text-sm text-gray-500">
                    {formData.comment.length}/1000 characters
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={submitting}
                className="inline-flex items-center px-4 py-2 md:px-6 md:py-3 border border-transparent text-xs md:text-sm font-medium rounded-lg text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FiSend className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                    Submit Comment
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Existing Comments */}
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 md:mb-6">
            {comments.length > 0 ? "Recent Comments" : "No Comments Yet"}
          </h3>

          {loading ? (
            <div className="flex items-center justify-center py-6 md:py-8">
              <div className="animate-spin rounded-full h-6 w-6 md:h-8 md:w-8 border-b-2 border-orange-500"></div>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-6 md:py-8 text-gray-400">
              <FiMessageSquare className="mx-auto h-8 w-8 md:h-12 md:w-12 text-gray-600 mb-3 md:mb-4" />
              <p className="text-sm md:text-base">
                Be the first to leave a comment!
              </p>
            </div>
          ) : (
            <div className="space-y-4 md:space-y-6">
              {comments.map((comment) => (
                <div
                  key={comment.id}
                  className="bg-white border border-gray-200 shadow-sm rounded-lg p-4 md:p-6"
                >
                  <div className="flex items-start justify-between mb-2 md:mb-3">
                    <div className="flex items-center">
                      <div className="w-8 h-8 md:w-10 md:h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {comment.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="ml-2 md:ml-3">
                        <h4 className="text-gray-900 font-medium text-sm md:text-base">
                          {comment.name}
                        </h4>
                        <div className="flex items-center text-xs md:text-sm text-gray-500">
                          <FiClock className="mr-1 h-2 w-2 md:h-3 md:w-3" />
                          {formatCommentDate(comment.createdAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-700 leading-relaxed text-sm md:text-base">
                    {comment.comment}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogComments;
