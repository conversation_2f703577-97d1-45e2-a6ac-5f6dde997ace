@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";
@import "tw-animate-css";  /* Optional if using animations */

/* Hide scrollbars globally */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit browsers (Chrome, Safari, Edge) */
}

/* Ensure body and html don't show scrollbars */
html, body {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

html::-webkit-scrollbar, body::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

/* Hide scrollbars for specific containers that might overflow */
.overflow-y-auto::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar,
.overflow-auto::-webkit-scrollbar {
  display: none;
}

.overflow-y-auto,
.overflow-x-auto,
.overflow-auto {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Additional scrollbar hiding for modal and other containers */
.modal-container::-webkit-scrollbar,
.venue-cards-container::-webkit-scrollbar,
.filter-container::-webkit-scrollbar {
  display: none;
}

/* Ensure no scrollbars appear on any element */
div::-webkit-scrollbar,
section::-webkit-scrollbar,
main::-webkit-scrollbar,
article::-webkit-scrollbar {
  display: none;
}

div, section, main, article {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Custom Toast Styles - High Priority */
.Toastify__toast--success {
  background-color: #10b981 !important;
  color: white !important;
  border: none !important;
}

.Toastify__toast--error {
  background-color: #ef4444 !important;
  color: white !important;
  border: none !important;
}

.Toastify__toast--info {
  background-color: #3b82f6 !important;
  color: white !important;
  border: none !important;
}

.Toastify__toast--warning {
  background-color: #f59e0b !important;
  color: white !important;
  border: none !important;
}

.Toastify__progress-bar--success {
  background: rgba(255, 255, 255, 0.7) !important;
}

.Toastify__progress-bar--error {
  background: rgba(255, 255, 255, 0.7) !important;
}

.Toastify__progress-bar--info {
  background: rgba(255, 255, 255, 0.7) !important;
}

.Toastify__progress-bar--warning {
  background: rgba(255, 255, 255, 0.7) !important;
}

.Toastify__close-button {
  color: white !important;
  opacity: 0.8;
}

.Toastify__close-button:hover {
  opacity: 1;
}

/* Ensure toast body text is white */
.Toastify__toast--success .Toastify__toast-body {
  color: white !important;
}

.Toastify__toast--error .Toastify__toast-body {
  color: white !important;
}

.Toastify__toast--info .Toastify__toast-body {
  color: white !important;
}

.Toastify__toast--warning .Toastify__toast-body {
  color: white !important;
}


/* :root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}



@layer base {
  * {
    border: var(--border);
    outline: var(--ring) / 50;
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
  }
} */
